local UI_RankLimitHelp = Class(BaseView)

function UI_RankLimitHelp:OnInit()
    
end

function UI_RankLimitHelp:OnCreate(param)
    self.btnList = {}
    self:ShowSequenceAnim(self.ui.m_goPage1)
end

function UI_RankLimitHelp:OnRefresh(param)
    
end

function UI_RankLimitHelp:onDestroy()
end

function UI_RankLimitHelp:onUIEventClick(go,param)
    local name = go.name

end

function UI_RankLimitHelp:ShowSequenceAnim(root)
    local list = {
        GetChild(root,"icon1"),
        GetChild(root,"row1"),
        GetChild(root,"icon2"),
        GetChild(root,"row2"),
        GetChild(root,"icon3"),
    }
    self.sequence = TweenMgr:CreateSequence(UIDefine.UI_RankLimitHelp, false, nil)
    for i = 1,#list do
        list[i].transform.localScale = Vector3.New(0,0,0)
        if(i > 1) then
            self.sequence:AppendInterval(0.15)
        end
        self.sequence:AppendCallback(function()
            if not self.ui then return end
            self:ShowHelpCell(list[i].transform)
        end)
    end
end

function UI_RankLimitHelp:ShowHelpCell(root)
    root.localScale = Vector3.New(0,0,1)
    root:DOScale(Vector3.New(1.2,1.2,1),0.1):OnComplete(function()
        root:DOScale(Vector3.New(0.9,0.9,1),0.1):OnComplete(function()
            root:DOScale(Vector3.New(1.05,1.05,1),0.1):OnComplete(function()
                root:DOScale(Vector3.New(1,1,1),0.1)
            end)
        end)
    end)
end

return UI_RankLimitHelp
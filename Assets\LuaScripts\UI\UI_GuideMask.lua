local UI_GuideMask = Class(BaseView)

GuideMask_Refresh = {
	SetShow = 1,
	SetCenter = 2,
	SetBtnSize = 3,
	SetBtnCenter = 4,
	SetCallBack = 5,
	SetArrow = 6,
	SetDialog = 7,
	SetHeadImg = 8,
    SetDialogText = 9,
}
function UI_GuideMask:OnInit()
	self.isAdaptingDialog = false
end
	
	--1 镂空显示方式与大小(形状,width,height)
	--2 镂空中心点偏移(x,y)
	--3 按钮大小(sizeX,sizeY)
	--4 缩放动画的时长(second)
	--5 点击镂空位置的回调(function)
	--6 箭头的位置(x,y)
	--7 对话框位置和内容(x,y,langId)
	--8 对话框头像(headPath)
	--9 是否使用自适应文本 isAdaptingDialog
	--10 箭头父节点的位置、选转
function UI_GuideMask:OnCreate(param)
	self.uiGameObject.transform.localScale = Vector3.New(100, 100, 1)
	self:SetAdaptingDialog(param[9] or false)
	--self.m_Renderer = GetComponent(self.uiGameObject, UEUI.Image)
	self.m_material = self.ui.m_imgMask.material
	
	self.tweenList = {}
	self.tweenIndex = 0

	self.m_tbShow = param[1] or {1,1}  --{3,100,100} 3是方形遮罩
	self.m_tbCenter = param[2] or {0,0}
	self.m_SizeOfBtn = param[3] or {1,1}
	self.m_AnimTime = param[4] or 1
	self.m_ClickCallBack = param[5]
	self.m_arrowPos = param[6] or nil
	self.m_diaLog = param[7] or nil
	self.m_headPath = param[8] or nil
	self.m_arrowParentPos = param[10] or nil
	self:SetShow()
	self:SetCenter()
	self:SetBtnSize()
	self:SetBtnCenter()
	self:SetCallBack()
	self:SetArrow()
	self:SetDialog()
	self:SetHeadImg()
 	--self.m_material:SetFloat(UE.Shader.PropertyToID("_Rect"), Vector4(0,0,200,200)) --位置xy ,尺寸大小zw
end

function UI_GuideMask:OnRefresh(param, param2)
    if param == 1 then
		--镂空显示方式与大小
		self.m_tbShow = param2 or {1,1}
		self:SetShow()
	elseif param == 2 then
		--镂空中心点偏移
		self.m_tbCenter = param2 or {0,0}
		self:SetCenter()
		self:SetBtnCenter()
	elseif param == 3 then
		--按钮大小
		self.m_SizeOfBtn = param2 or {1,1}
		self:SetBtnSize()
	elseif param == 4 then
		--缩放动画的时长
		self.m_AnimTime = param2 or 1
	elseif param == 5 then
		--点击镂空位置的回调
		self.m_ClickCallBack = param2
		self:SetCallBack()
	elseif param == 6 then
		self.ui.m_goArrow.transform:DOKill()
		self.m_arrowPos = param2
		self:SetArrow()
	elseif param == 7 then
		self.m_diaLog = param2
		self:SetDialog()
	elseif param == 8 then
		self.m_headPath = param2
		self:SetHeadImg()
    elseif param == 9 then
        self.ui.m_txtDialog1.text = param2
        self.ui.m_txtDialog2.text = param2
	end
end

function UI_GuideMask:onDestroy()
	RemoveUIComponentEventCallback(self.ui.m_btnGuide, UEUI.Button)
	
	for k, v in pairs(self.tweenList) do
		if v ~= nil then
			v:Kill()
		end
	end
	self.tweenList=nil
end

function UI_GuideMask:onUIEventClick(go,param)
    local name = go.name

end
function UI_GuideMask:SetAdaptingDialog(isAdaptingDialog)
	self.ui.m_goAdaptingDialog:SetActive(isAdaptingDialog)
	self.ui.m_goNormalDialog:SetActive(not isAdaptingDialog)
	self.isAdaptingDialog = isAdaptingDialog
end
function UI_GuideMask:SetShow()
	if self.m_tbShow then
		if self.m_tbShow[1] == 1 then
			self.m_material:SetFloat(UE.Shader.PropertyToID("_Slider"), self.m_tbShow[2])
			self.m_material:SetVector(UE.Shader.PropertyToID("_Rect"), Vector4.New())
		elseif self.m_tbShow[1] == 2 then
			function OnScale(value)
				self.m_material:SetFloat(UE.Shader.PropertyToID("_Slider"), value)
			end
			--local dotween = Tween.To(OnScale, self.m_tbShow[2], self.m_tbShow[3], 0.3)
			local dotween = Tween.To(OnScale, self.m_tbShow[2], self.m_tbShow[3], self.m_AnimTime)
			self.tweenList[self.tweenIndex] = dotween
			self.tweenIndex = self.tweenIndex + 1
			self.m_material:SetVector(UE.Shader.PropertyToID("_Rect"), Vector4.New())
		elseif self.m_tbShow[1] == 3 then
			self.m_material:SetFloat(UE.Shader.PropertyToID("_Slider"), 0)
			local posY = Game.IsWechatGame() and self.m_tbCenter[2] - 150 / 2 or self.m_tbCenter[2];
			self.m_material:SetVector(UE.Shader.PropertyToID("_Rect"), Vector4(self.m_tbCenter[1] or 0,posY or 0,self.m_tbShow[2],self.m_tbShow[3])) --位置xy ,尺寸大小zw
		end
	end

end

function UI_GuideMask:SetCenter()
	if self.m_tbCenter then
		local posY =  self.m_tbCenter[2];

		if Game.IsWechatGame() then
            local t =  (UIHeight - GameUtil.SafeAreaTop)  / UIHeight
            posY  = posY  - GameUtil.SafeAreaTop/2 *  t
        end

		self.m_material:SetVector(UE.Shader.PropertyToID("_Center"), Vector4(
				self.m_tbCenter[1] or 0,
				posY or 0,
				0, 0))
	end
end

function UI_GuideMask:SetBtnSize()
	if self.m_SizeOfBtn then
		self.ui.m_btnGuide.transform.localScale = Vector3.New(self.m_SizeOfBtn[1], self.m_SizeOfBtn[2])
	end
end
function UI_GuideMask:SetBtnCenter()
	self.ui.m_btnGuide.transform.anchoredPosition = Vector3.New(self.m_tbCenter[1], self.m_tbCenter[2])
end

function UI_GuideMask:SetCallBack()
	RemoveUIComponentEventCallback(self.ui.m_btnGuide, UEUI.Button)
	AddUIComponentEventCallback(self.ui.m_btnGuide, UEUI.Button,
		function (go,param)
			local callback = param[1]
			if callback then
				callback()
			end
		end,
		{self.m_ClickCallBack})
end

function UI_GuideMask:SetArrow()
	if self.m_arrowPos then
		SetActive(self.ui.m_goArrow,true)		
		self.ui.m_goArrow.transform.anchoredPosition = Vector3.New(self.m_arrowPos[1], self.m_arrowPos[2])
		if #self.m_arrowPos > 2 then
			self.ui.m_goArrow.transform.rotation = Quaternion.Euler(self.m_arrowPos[3], self.m_arrowPos[4],self.m_arrowPos[5])	
		end
		DOLocalMoveY(self.ui.m_goArrow.transform , self.m_arrowPos[2]+0.5 , 0.5 , nil ,Ease.InOutSine):SetLoops(-1 , LoopType.Yoyo)
	else
		SetActive(self.ui.m_goArrow,false)
	end
	
	if self.m_arrowParentPos then
		self.ui.m_goArrowParent.transform.anchoredPosition = Vector3.New(self.m_arrowParentPos[1], self.m_arrowParentPos[2])
		if #self.m_arrowParentPos > 2 then
			self.ui.m_goArrowParent.transform.rotation = Quaternion.Euler(self.m_arrowParentPos[3], self.m_arrowParentPos[4],self.m_arrowParentPos[5])
		end
	end
end

function UI_GuideMask:SetDialog()
	if self.m_diaLog then
		SetActive(self.ui.m_goDialog,true)
		self.ui.m_goDialog.transform.anchoredPosition = Vector3.New(self.m_diaLog[1], self.m_diaLog[2])
		if self.isAdaptingDialog then
			self.ui.m_txtDialog2.text = LangMgr:GetLang(self.m_diaLog[3]):gsub("\\n", "\n")
		else
			self.ui.m_txtDialog1.text = LangMgr:GetLang(self.m_diaLog[3])
		end
	else
		SetActive(self.ui.m_goDialog,false)
	end
end
function UI_GuideMask:SetHeadImg()
	if self.m_headPath then
		SetImageSync(self.ui.m_imgHead,self.m_headPath,false)
	end
end


return UI_GuideMask
local UI_SkiingScoreRank = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local oneToOneItem = Class(ItemBase)
local ItemShowNums = 11

function UI_SkiingScoreRank:OnInit()

end
--{activityId,playerData,myselfData}s
function UI_SkiingScoreRank:OnCreate(serverData,isPush,activityId,playerData,myselfData,noRank)
    local logic = function()
        SetActive(self.ui.m_goNoRank,noRank)
        SetActive(self.ui.m_goMyItemRank,not noRank)
        SetActive(self.ui.m_goRankScroll,not noRank)

        self.playAni = false
        self.isPush = isPush
        self.airIndex = 1
        if not noRank then
            if self.isPush then
                if activityId then
                    self.active = LimitActivityController:GetActiveMessage(activityId)
                    self.playerListData = playerData
                    self.myselfData = myselfData
                end
                self.playAni = true
            else
                self.playerListData = serverData.ranking
                self.myselfData = serverData.player
            end

            local transform = self.uiGameObject.transform
            self.slider = require("UI.Common.SlideRect").new()
            self.slider:Init(transform:Find("bg/m_goRankScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)

            self.playerList = {}
            for i = 1, ItemShowNums do
                self.playerList[i] = oneToOneItem.new()
                self.playerList[i]:Init(UEGO.Instantiate(self.ui.m_goItemRank.transform))
            end
            self.slider:SetItems(self.playerList,0,Vector2.New(0,10))
            self.slider:SetData(self.playerListData,self.myselfData.rank+20)

            self:SetMyself()
            if self.playAni then
                local rankReward = NewSkiingManager:GetRankRewardConfig(v2n(self.myselfData.ranking)).reward
                local rewardT = {}
                rankReward = string.split(rankReward,";")
                for _, v in pairs(rankReward) do
                    local t = {}
                    local arr = string.split(v,"|")
                    t[1] = v2n(arr[1])
                    t[2] = v2n(arr[2])
                    table.insert(rewardT,t)
                end
                UI_SHOW(UIDefine.UI_ItemRankFinishReward, rewardT,6)
            end
            UI_UPDATE(UIDefine.UI_NewSkiingMatch,66,self.myselfData.rank)
            self.slider:MoveToIndex(self.myselfData.rank,2)
        end
    end
    
    --创建头像
    CreateCommonHeadAsync(GetChild(self.ui.m_goMyItemRank, "headNode",UE.Transform),0.4, nil, function (go, trans)
        self.myHeadNode = go
        CreateCommonHeadAsync(GetChild(self.ui.m_goItemRank, "headNode",UE.Transform),0.4,nil,function(a,b)
            logic()
        end)
    end)
    
    --self.myHeadNode = CreateCommonHead(GetChild(self.ui.m_goMyItemRank, "headNode",UE.Transform),0.4)
    --CreateCommonHead(GetChild(self.ui.m_goItemRank, "headNode",UE.Transform),0.4)
end

function UI_SkiingScoreRank:OnRefresh(type,param)
    if type == 1 then
        --结算奖励
        SetActive(self.ui.m_goTarget,true)
        self:FlyMidToAir()
    end
end

function UI_SkiingScoreRank:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnClose" then
        self:Close()
    end
end

function UI_SkiingScoreRank:onDestroy()
    Tween.Kill("AutoMoveFunc")
    self.myHeadNode = nil
    if self.isPush then
        NetPushViewData:RemoveViewByIndex(PushDefine.UI_SkiingScoreRank)
        NetPushViewData:CheckOtherView(true)
        if self.active and self.active:IsActivityEnd() then

			local sledgeNum = NetNewSkiingData:GetSledgeNum()
			if sledgeNum > 0 then
				NetPushViewData:PushView(PushDefine.UI_SkiingFinish,sledgeNum)
			end

            local rankReward = NewSkiingManager:GetRankRewardConfig(v2n(self.myselfData.ranking)).reward
            NetGlobalData:GetRewardToMap(rankReward,"UI_SkiingScoreRank")
			NetNewSkiingData:GetCacheReward()
            self.active:CloseActive()
        end
    end
end

local InitItemInfo = function(obj,config,isPlayer)
    local icon = GetChild(obj,"icon",UEUI.Image)
    local num = GetChild(obj,"num",UEUI.Text)
    local arr = string.split(config,"|")
    local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
    SetUIImage(icon, ItemConfig:GetIcon(itemId), false)
    num.text = "x"..v2n(arr[2])
    local color = Color.HexToRGB(isPlayer and "c65200" or "0382c4")
    num.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = color
    num.gameObject:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow)).effectColor = color
    
    local btn = icon.gameObject.transform:GetComponent(typeof(UEUI.Button))
    RemoveUIComponentEventCallback(btn,UEUI.Button)
    AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
        if itemId > 0  then
            UI_SHOW(UIDefine.UI_ItemTips,itemId)
        end
    end)
    SetActive(obj,true)
end

--销毁指定节点下所有子物体
local DestroyAllChild = function(trans)
    local childCount = trans.childCount
    if childCount > 0 then
        for i = 0,childCount-1 do
            local child = trans:GetChild(i)
            if child then
                UEGO.Destroy(child.gameObject)
            end
        end
    end
end

function UI_SkiingScoreRank:SetMyself()
    local moreRewardNode     = GetChild(self.ui.m_goMyItemRank, "moreReward", UE.Transform)
    local moreContent        = GetChild(self.ui.m_goMyItemRank, "moreReward/Viewport/Content", UE.Transform)
    local rewardItem         = GetChild(self.ui.m_goMyItemRank,"rankRewardItem")
    
    local rank = self.isPush and v2n(self.myselfData.ranking) or v2n(self.myselfData.rank)

    if rank <= 3 then
        local rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number"..rank..".png"
        SetUIImage(self.ui.m_imgRank,rankSprite,false)
    end
    SetActive(self.ui.m_imgRank.gameObject,rank <= 3)
    
    self.ui.m_txtRank.text = rank
    self.ui.m_txtScore.text = self.myselfData.point .. "m"

    local rankReward = NewSkiingManager:GetRankRewardConfig(rank).reward
    local rewardT = string.split(rankReward,";")
    local index = table.count(rewardT)
    
    DestroyAllChild(moreContent)
    SetActive(rewardItem,false)
    for i = 1,index do
        local obj = UEGO.Instantiate(rewardItem,moreContent)
        InitItemInfo(obj,rewardT[i],true)
    end

    local isOver5 = (index > 4)
    if isOver5 then
        SetUIPivot(moreContent,0,1)
        SetUIAnchors(moreContent,0,1,0,1)
    else
        SetUIPivot(moreContent,1,1)
        SetUIAnchors(moreContent,1,1,1,1)
    end
    local scrollRect = GetComponent(moreRewardNode,UEUI.ScrollRect)
    scrollRect.normalizedPosition = Vector2.New(0,1);
   
    --local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --if headConfig then
    --    SetUIImage(self.ui.m_imgHead, headConfig.icon, false)
    --end
    SetMyHeadAndBorderByGo(self.myHeadNode)
    self.myHeadNode.transform.localPosition = Vector3.zero
    
    self.ui.m_txtName.text = NetUpdatePlayerData:GetPlayerInfo().name
end

function UI_SkiingScoreRank:FlyMidToAir(callBack)
    if nil == self.myselfData.ranking then
        return
    end

    local assetPath     = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "COM_FlyItem")
    ResMgr:LoadAssetAsync(assetPath,AssetDefine.LoadType.Instant,function(itemListRes)
        local rankReward = NewSkiingManager:GetRankRewardConfig(v2n(self.myselfData.ranking)).reward
        local strArr = string.split(rankReward,";")
        for k, v in pairs(strArr) do
            local arr = string.split(v,"|")
            local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
            local num = v2n(arr[2])

            if self.airIndex <= 5 then
                local rewardGo      = GetChild(self.ui.m_goPanel,"pos" .. self.airIndex ,UEUI.Image)
                self.airIndex = self.airIndex + 1
                SetActive(rewardGo,true)
                SetImageSprite(rewardGo,ItemConfig:GetIcon(itemId),false)
            end
            local endPos = GetUIPosByVector3(self.ui.m_goTarget.transform)
            local go            = UEGO.Instantiate(itemListRes)

            if go then
                local goIcon = GetComponent(go,UEUI.Image)
                go.transform:SetParent(self.ui.m_goMidPlant.transform)
                go.transform.localPosition = Vector3(0,0,0)
                go.transform.localScale = Vector3.New(1, 1, 1)
                SetImageSprite(goIcon,ItemConfig:GetIcon(itemId),false)
                SetUISize(go,100,100)
                go.transform:SetParent(self.ui.m_goPanel.transform)
                DOLocalMove(go.transform,Vector3(0,0,0),k*0.4,function ()
                    UEGO.Destroy(go)
                    if callBack then
                        callBack()
                    end
                    callBack = nil
                end)
            end
        end
    end)
end

-------------
function oneToOneItem:OnInit(transform)
    local obj = transform
    self.trans				= transform
    self.bgImg				= GetChild(obj, "bg", UEUI.Image)
    self.img_rank			= GetChild(obj, "img_rank", UEUI.Image)
    self.scoreBg			= GetChild(obj, "scoreBg", UEUI.Image)
    self.img_head			= GetChild(obj, "head/img_head", UEUI.Image)
    self.txt_name			= GetChild(obj, "txt_name", UEUI.Text)
    self.txt_score			= GetChild(obj, "txt_score", UEUI.Text)
    self.txt_rank			= GetChild(obj, "txt_rank", UEUI.Text)
    self.moreRewardNode     = GetChild(obj, "moreReward", UE.Transform)
    self.moreContent     = GetChild(obj, "moreReward/Viewport/Content", UE.Transform)
    self.rewardItem         = GetChild(obj,"rankRewardItem")
    self.smallRewardNode    = GetChild(obj,"rewardList")
    self.customHeadObj      = GetChild(obj,"headNode/CustomHead")
    
    self.rewardCellList = {}
    local rewardList = GetChild(obj, "rewardList", UE.RectTransform)
    for i = 1, 4 do
        local cell = {}
        local obj = GetChild(rewardList, "reward"..i, UE.RectTransform)
        local icon = GetChild(obj,"icon",UEUI.Image)
        local num = GetChild(obj,"num",UEUI.Text)
        cell["obj"] = obj
        cell["icon"] = icon
        cell["num"] = num
        table.insert(self.rewardCellList,cell)
    end
end

function oneToOneItem:UpdateData(data,index1)
    if not data then return end
    local rankIndex = data.rank
    if rankIndex <= 3 then
        local rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number"..rankIndex..".png"
        SetUIImage(self.img_rank,rankSprite,false)
    end
    SetActive(self.img_rank.gameObject,rankIndex <= 3)
    
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, data.icon)
    --if headConfig then
    --    SetUIImage(self.img_head, headConfig.icon, false)
    --end
    
    SetHeadAndBorderByGo(self.customHeadObj,data.icon,data.border)
    self.customHeadObj.transform.localPosition = Vector3.zero

    local rankReward = NewSkiingManager:GetRankRewardConfig(rankIndex).reward
    local rewardT = string.split(rankReward,";")
    local index = table.count(rewardT)
    --是否超过四个，如果超过四个，使用滑动列表显示奖励
    local isOver5 = index > 4
    SetActive(self.moreRewardNode,isOver5)
    SetActive(self.smallRewardNode,not isOver5)
    SetActive(self.rewardItem,false)
    if isOver5 then
        DestroyAllChild(self.moreContent)
        for i = 1,index do
            local obj = UEGO.Instantiate(self.rewardItem,self.moreContent)
            InitItemInfo(obj,rewardT[i],data.playerId)
        end
        local scrollRect = GetComponent(self.moreRewardNode,UEUI.ScrollRect)
        scrollRect.normalizedPosition = Vector2.New(0,1);
    else
        for k, v in pairs(self.rewardCellList) do
            if index >= k then
                InitItemInfo(v.obj,rewardT[k],data.playerId)
            end
            SetActive(v.obj,index >= k)
        end
    end
    
    self.txt_name.text = data.name
    
    local value = data.playerId
    local hex = value and "#BA3D00" or "#036bc4";
    self.txt_rank.text = string.format("<color="..hex..">%s</color>", rankIndex)
    self.txt_score.text = string.format("<color="..hex..">%sm</color>", data.point)
    local path = "Sprite/ui_huodongjingsai_1V1duijue/"
    local path1 = "Sprite/ui_huodongjingsai_1V1duijue/"
    
    SetUIImage(self.bgImg,  path..(value and "duijue_paihang_list2.png" or "duijue_paihang_list1.png"),false)
    SetUIImage(self.scoreBg,path1..(value and "duijue_paihang_jifenkuang1.png" or "duijue_paihang_jifenkuang2.png"),false)
    
    self.txt_name.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB(hex)
    self.txt_name.gameObject:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow)).effectColor = Color.HexToRGB(hex)
end

function oneToOneItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function oneToOneItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

return UI_SkiingScoreRank
local UI_LeagueChatSelectItem = Class(BaseView)

local i_HelpItemConfigID = 0 --选中的请求帮助配置ID

local timer_HelpItemRefresh

local tableViewH_NormalItemList = nil
local tableViewH_HighLvItemList = nil

local selectIndex = -1
local isSelectNormal = false

function UI_LeagueChatSelectItem:OnInit()
    
end

function UI_LeagueChatSelectItem:OnCreate(param)
	tableViewH_NormalItemList = GetChild(self.ui.m_goPopFrame, "goNormalItemList/goScroll", CS.Mosframe.TableView)
	tableViewH_HighLvItemList= GetChild(self.ui.m_goPopFrame, "goHighLvItemList/goScroll", CS.Mosframe.TableView)
	
	EventMgr:Add(EventID.REFRESH_CHAT_SELECTITEM,self.onSelectItem,self)
	EventMgr:Add(EventID.REFRESH_CHAT_HELPITEM,self.ShowHelpItemInit,self)
	
	LeagueChatManager:OnHelpItemData()
end

function UI_LeagueChatSelectItem:OnRefresh(param)
    
end

function UI_LeagueChatSelectItem:onDestroy()
	TimeMgr:DestroyTimer(UIDefine.UI_LeagueChat, timer_HelpItemRefresh)
	
	EventMgr:Remove(EventID.REFRESH_CHAT_SELECTITEM,self.onSelectItem,self)
	EventMgr:Remove(EventID.REFRESH_CHAT_HELPITEM,self.ShowHelpItemInit,self)
	
	tableViewH_NormalItemList = nil
	tableViewH_HighLvItemList  = nil

	selectIndex = -1
end

function UI_LeagueChatSelectItem:onUIEventClick(go,param)
    local name = go.name

	if name == "closeBtn" then
		self:Close()
	elseif name == "m_btnConfirmSelect" then
		self:onConfirmSelect()
	end
end

----- 申请帮助界面 -----
function UI_LeagueChatSelectItem:onConfirmSelect()
	if i_HelpItemConfigID == 0 then
		UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9108))
		return
	end

	LeagueChatManager:SendHelpApply(v2s(i_HelpItemConfigID))

	self:Close()
end
function UI_LeagueChatSelectItem:onSelectItem(param)
	local iItemId = param[2]
	local iCount = param[3]
	local isNormal = param[4]
	i_HelpItemConfigID = LeagueChatManager:GetHelpItemConfigIDById(param[1],isNormal)

	local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, iItemId)
	if not itemCfg then
		Log.Error("item config Error! id is ",iItemId)
		return
	end

	SetImageSprite(self.ui.m_imgSelectItem, itemCfg.icon_b, false)
	self.ui.m_txtSelectCount.text = iCount
	local selectBtn = self.ui.m_imgSelectItem:GetComponent(typeof(UEUI.Button))
	if selectBtn then
		RemoveUIComponentEventCallback(selectBtn,UEUI.Button)
		AddUIComponentEventCallback(selectBtn, UEUI.Button, function()	
				UI_SHOW(UIDefine.UI_ItemTips,iItemId)
			end)
	end
	SetActive(self.ui.m_goNoItem, false)
	SetActive(self.ui.m_imgSelectItem.gameObject, true)
	SetActive(self.ui.m_txtSelectCount.gameObject, true)

	tableViewH_NormalItemList:UpdateTableView()
	tableViewH_HighLvItemList:UpdateTableView()
end

function UI_LeagueChatSelectItem:onHelpItemRefresh()
	TimeMgr:DestroyTimer(UIDefine.UI_LeagueChatSelectItem, timer_HelpItemRefresh)

	timer_HelpItemRefresh = TimeMgr:CreateTimer(UIDefine.UI_LeagueChatSelectItem,
		function(param)
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 9)
			if not config then
				Log.Error("union_setting config Error! id is 9")
				return
			end
			local iRefreshDelta = v2n(config.value)
			local iRefreshTime = NetLeagueData:GetChatHelpItemRefreshTime()
			local iCurTime = TimeMgr:GetServerTimestamp()

			if iCurTime > (iRefreshTime + iRefreshDelta) then
				LeagueChatManager:OnHelpItemData()
			else
				local t = (iRefreshTime + iRefreshDelta) - iCurTime
				self.ui.m_txtItemRefreshCountDown.text = string.format(LangMgr:GetLang(9118), TimeMgr:ConverSecondToString(t))
			end

		end,
		1, -1)
end

function UI_LeagueChatSelectItem:ShowHelpItemInit()
	local strTimesHighLv = LeagueChatManager:GetHelpItemNeedTimesById(1, false)
	local strTimesNormal = LeagueChatManager:GetHelpItemNeedTimesById(1, true)
	
	self.ui.m_txtNormalItemNeed.text = string.format(LangMgr:GetLang(9110), "<color=#D74200>"..strTimesNormal.."</color>")
	self.ui.m_txtHighLvIItemNeed.text = string.format(LangMgr:GetLang(9109), "<color=#D74200>"..strTimesHighLv.."</color>")
	self.ui.m_txtSelectCount.text = 0

	i_HelpItemConfigID = 0
	SetActive(self.ui.m_goNoItem, true)
	SetActive(self.ui.m_imgSelectItem.gameObject, false)
	SetActive(self.ui.m_txtSelectCount.gameObject, false)

	self:onHelpItemRefresh()
	self:LoadTableView_HelpItem(true)
	self:LoadTableView_HelpItem(false)
end

function UI_LeagueChatSelectItem:LoadTableView_HelpItem(isNormal)
	if not self.cellObj then
		local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_LeagueChatSelectItemCell")
		ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant, function(cellObj)
			self.cellObj = cellObj;
			self:LoadTableView(isNormal);
		end)
	else
		self:LoadTableView(isNormal);
	end
end

function UI_LeagueChatSelectItem:LoadTableView(isNormal)
	if isNormal then
		tableViewH_NormalItemList.GetItemCount = function (num)
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 11)
			if not config then
				Log.Error("union_setting config Error! id is 11")
				return 0
			end
			local value = config.value
			local iCount = v2n(value)
			return iCount
		end
		tableViewH_NormalItemList.GetItemGo = function(obj)
			--return self.ui.m_goItemShow
			return self.cellObj
		end
		tableViewH_NormalItemList.UpdateItemCell = function(idx, obj)
			self:LoadCellData_HelpItem(idx, obj, isNormal)
		end
		tableViewH_NormalItemList:InitTableViewByIndex(0)
	else
		tableViewH_HighLvItemList.GetItemCount = function (num)
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_setting, 10)
			if not config then
				Log.Error("union_setting config Error! id is 10")
				return 0
			end
			local value = config.value
			local iCount = v2n(value)
			return iCount
		end
		tableViewH_HighLvItemList.GetItemGo = function(obj)
			--return self.ui.m_goItemShow
			return self.cellObj
		end
		tableViewH_HighLvItemList.UpdateItemCell = function(idx, obj)
			self:LoadCellData_HelpItem(idx, obj, isNormal)
		end
		tableViewH_HighLvItemList:InitTableViewByIndex(0)
	end
end

function UI_LeagueChatSelectItem:LoadCellData_HelpItem(index, obj, isNormal)
	local tId = index + 1
	local iItemId, iCount
	if isNormal then
		iItemId, iCount = LeagueChatManager:GetHelpItemDataNormalById(tId)
	else
		iItemId, iCount = LeagueChatManager:GetHelpItemDataHighLvById(tId)
	end
	
	local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item, iItemId)
	if not itemCfg then
		Log.Error("item config Error! id is ",iItemId)
		return
	end

	local imgItem = GetChild(obj, "imgItem", UEUI.Image)
	local txtCount = GetChild(obj, "txtCount", UEUI.Text)
	local btn = GetChild(obj, "imgBg", UEUI.Button)
	local selectMark = GetChild(obj,"imgSelect")

	SetImageSprite(imgItem, itemCfg.icon_b, false)
	txtCount.text = v2s(iCount)

	local isShowMark = false
	if selectIndex ~= -1 then
		if isNormal == isSelectNormal and selectIndex == tId then
			isShowMark = true
		end
	end
	SetActive(selectMark,isShowMark)
	
	RemoveUIComponentEventCallback(btn, UEUI.Button)
	AddUIComponentEventCallback(btn, UEUI.Button, function (go,param)
		    isSelectNormal = isNormal
		    selectIndex = tId
		    EventMgr:Dispatch(EventID.REFRESH_CHAT_SELECTITEM, param)
		end,
		{tId, iItemId, iCount, isNormal})
end

return UI_LeagueChatSelectItem
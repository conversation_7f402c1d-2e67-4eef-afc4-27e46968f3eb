local UI_Setting = Class(BaseView);

local BaseSlideItem = require("UI.Common.BaseSlideItem");
local setBtnItem = Class(BaseSlideItem);

local cpCustomHead = require "UI.CustomHead"

function UI_Setting:OnInit()

end

function UI_Setting:OnCreate(param)
    local level, exp, maxExp = NetUpdatePlayerData:GetLevel();
    self.ui.m_txtLv.text = level;
    self.ui.m_txtExp.text = NumToGameString(exp)  .. "/" .. NumToGameString(maxExp);

    local scale = exp / maxExp;
    scale = scale >= 1 and 1 or scale;
    SetUISize(self.ui.m_imgExp, 488 * scale, 33);

	self.ui.m_txtUser.text = NetUpdatePlayerData:GetPlayerInfo().id;
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_goUser.transform);

	CS.StartGame.Instance:SetUIVer(self.ui.m_txtVersion);
	self.versionStr = self.ui.m_txtVersion.text;
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_goVersion.transform);

	local config = ConfigMgr:GetData(ConfigDefine.ID.game_setting);
	self.itemList = {};
	local index = 1;
	local item;
	local dataList = {};
	local isShield;
	for k, v in ipairs(config) do
		isShield = v.shield == 1;
		if not isShield then
			if v.id == 1 then -- 兑换码
				local ServerSwitch = Game.GetServerSwitch();
				isShield = ServerSwitch and ServerSwitch.redemption_show == 0;
			elseif v.id == 11 then -- 广告链接
				isShield = not SdkHelper:CanShowAdsConsentForm();
			end
		end
		
		if not isShield then
            item = setBtnItem.new();
            item:Init(UEGO.Instantiate(self.ui.m_transDragItem));
            item:setParam(param, self.versionStr);
            self.itemList[index] = item;
			
            dataList[index] = v;
            index = index + 1;
        end
	end
	table.sort(dataList, function(a, b)
		return a.sort < b.sort;
	end)

	self.slideRect = require("UI.Common.SlideRect").new();
	self.slideRect:Init(self.ui.m_scrollview, 2);
	self.slideRect:SetItems(self.itemList, 8, Vector2.New(0, 5));
	self.slideRect:SetData(dataList);

	local showId;
	if param == 1 then
		showId = 2; -- 管理账号
	elseif param == 2 then
		showId = 7; -- 关注我们
	end
	if showId ~= nil then
		local curIdx = 1;
		for i = 1, #dataList do
			if dataList[i].id == showId then
				curIdx = i;
				break;
			end
		end
		self.slideRect:MoveToIndex(curIdx, 0.5);
	end

	self.m_CustomHead = cpCustomHead.new()
	self.m_CustomHead:CreateHead(self.ui.m_imgHead.transform)
	self.m_CustomHead:SetClickCall(function()
		UI_SHOW(UIDefine.UI_SettingHeadChange)
	end)
	
	self.musicOnSp = "Sprite/ui_setting/shezhi_music_on_yinxiao.png";
	self.musicOffSp = "Sprite/ui_setting/shezhi_music_off_yinxiao.png";
	self.soundOnSp = "Sprite/ui_setting/shezhi_music_on_yinliang.png";
	self.soundOffSp = "Sprite/ui_setting/shezhi_music_off_yinliang.png";
	
	self:SetIsUpdateTick(true);
	self:OnRefreshHead();
	self:OnRefreshNameText();
	self:RefreshRedPoint();
	self:UpdateUIAudio();

	EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
end

function UI_Setting:OnRefresh(param)
	if param == 1 then
		self:OnRefreshNameText()
	elseif param == 2 then
		self:OnRefreshHead()
	elseif param == 3 then
		self:RefreshRedPoint()
	elseif param == 4 then
		if self.m_CustomHead then
			self.m_CustomHead:RefreshHead()
		end
	end
end

function UI_Setting:onDestroy()
	if self.m_CustomHead then
		self.m_CustomHead:onDestroy()
		self.m_CustomHead = nil
	end
	EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
end

function UI_Setting:onUIEventClick(go, param)
	local name = go.name
	if name == "closeBtn" then
		AudioMgr:Play(5)
		self:Close()
	elseif name == "headChangeBtn" then
		UI_SHOW(UIDefine.UI_SettingHeadChange)
	elseif name == "changeNameBtn" then
		UI_SHOW(UIDefine.UI_SettingNameChange)
	elseif name == "copyBtn" then
		UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(133))
		SdkHelper:Copy(NetUpdatePlayerData:GetPlayerInfo().id)
	elseif name == "musicBtn"or name == "m_imgMusic" then
		self:UpdateUIAudio(1);
		local v = AudioMgr:GetSoundSetting(PlayerPrefsKey.Music)
		if v == 0 then
			AudioMgr:SetSoundSetting(PlayerPrefsKey.Music, 1)
		else
			AudioMgr:SetSoundSetting(PlayerPrefsKey.Music, 0)
		end
		CheatCodeModule:PressCheatCode(1)
	elseif name == "soundBtn" or name == "m_imgSound" then
		self:UpdateUIAudio(2);
		local v = AudioMgr:GetSoundSetting(PlayerPrefsKey.Sound)
		if v == 0 then
			AudioMgr:SetSoundSetting(PlayerPrefsKey.Sound, 1)
		else
			AudioMgr:SetSoundSetting(PlayerPrefsKey.Sound, 0)
		end
		CheatCodeModule:PressCheatCode(2)
	end
end

function UI_Setting:TickUI(deltaTime)
	if self.m_IsTouchedSlider then
		local count = CS.TouchMono.Instance:GetTouchCount()
		if count > 0 then
			self.m_IsDragging = true
		else
			if self.m_IsDragging then
				if self.m_IsTouchedSlider == 1 then
					if self.ui.m_sliderMusic.value > 0.5 then
						AudioMgr:SetSoundSetting(PlayerPrefsKey.Music, 1)
					else
						AudioMgr:SetSoundSetting(PlayerPrefsKey.Music, 0)
					end

				elseif self.m_IsTouchedSlider == 2 then
					if self.ui.m_sliderSound.value > 0.5 then
						AudioMgr:SetSoundSetting(PlayerPrefsKey.Sound, 1)
					else
						AudioMgr:SetSoundSetting(PlayerPrefsKey.Sound, 0)
					end
				end
				self:UpdateUIAudio(0)

				self.m_IsDragging = nil
			end
			self.m_IsTouchedSlider = nil
		end
	end
end

function UI_Setting:AutoClose()
	local isSdkView = SdkHelper:DestroySDKView()
	if not isSdkView then
		self:Close()
	end
end

function UI_Setting:OnRedPointDirty(dirtyIdSet)
	if dirtyIdSet[RedID.MailRoot] then
		self:RefreshRedPoint();
	end
end

function UI_Setting:StartAnim(isClose, onDoneCall)
    if isClose then
        AudioMgr:Play(5)
    else
        AudioMgr:Play(4)
    end

    local lockKey = (isClose and "Close " or "Open ") .. self.uiName
    UIMgr:SetUILock(true,  lockKey)

    local function onUnLockDelay(key, delay)
        local function unlock()
            UIMgr:SetUILock(false,  key)
        end
        TimeMgr:CreateTimer("ui_anim_" .. key, unlock, delay, 1)
    end

    onUnLockDelay(lockKey, 0.1)

    local go = self:GetUIGameObject()
    local trans = go.transform
    if isClose then
        DOFadeAlpha(self.uiGoWallCanvasGroup,1,0,0.3,0,0,Ease.InCubic)
        GameUtil.DOAction(0, 0.3, 0.0, 1, 8, 1, onDoneCall, trans, { 0.0, 0.0, 0.0, -1000, 0.0, 0.0 })
    else
        DOFadeAlpha(self.uiGoWallCanvasGroup,0,1,0.4,0.1,0,Ease.OutCubic)
        GameUtil.DOAction(0, 0.4, 0.0, 1, 9, 1, onDoneCall, trans, { -1000, 0.0, 0.0, 0.0, 0.0, 0.0 })
    end
    return 0
end

function UI_Setting:OnRefreshHead()
	--local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, NetUpdatePlayerData:GetPlayerInfo().head)
	--SetUIImage(self.ui.m_imgHead, headConfig["icon"], false)

	self.m_CustomHead:SetHeadByID(NetUpdatePlayerData:GetPlayerInfo().head)
	self.m_CustomHead:SetHeadBorderByID(NetUpdatePlayerData:GetPlayerInfo().headBorder)
end

function UI_Setting:OnRefreshNameText()
	if NetUpdatePlayerData:GetPlayerInfo().name ~= nil then
		self.ui.m_txtName.text = NetUpdatePlayerData:GetPlayerInfo().name
	else
		self.ui.m_txtName.text = "player-" .. NetUpdatePlayerData:GetPlayerInfo().id
	end
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_goName.transform)
end

function UI_Setting:RefreshRedPoint()
	SetActive(self.ui.m_goHeadRed,  NetHeadSeasonVip:IsShowRedPoint())--NetInfoData:IsShowHeadRed() or
	
	for k, v in ipairs(self.itemList) do
		v:UpdateRedState();
	end
end

function UI_Setting:UpdateUIOnOff(state1, state2)
	if state1 then
        self.ui.m_txtMusicOn.text = state1 >= 1 and LangMgr:GetLang(107) or "";
        self.ui.m_txtMusicOff.text = state1 < 1 and LangMgr:GetLang(108) or "";
		SetUIImage(self.ui.m_imgMusic, state1 >= 1 and self.musicOnSp or self.musicOffSp, false);
	end

	if state2 then
        self.ui.m_txtSoundOn.text = state2 >= 1 and LangMgr:GetLang(107) or "";
        self.ui.m_txtSoundOff.text = state2 < 1 and LangMgr:GetLang(108) or "";
		SetUIImage(self.ui.m_imgSound, state2 >= 1 and self.soundOnSp or self.soundOffSp, false);
    end
end

function UI_Setting:UpdateUIAudio(animType)
	local slider1 = self.ui.m_sliderMusic;
	local slider2 = self.ui.m_sliderSound;

	local state1 = AudioMgr:GetSoundSetting(PlayerPrefsKey.Music)
	local state2 = AudioMgr:GetSoundSetting(PlayerPrefsKey.Sound)
	if not animType or animType == 0 then
		if not animType then
			local function onChange1(v)
                self.ui.m_txtMusicOn.text = v >= 1 and LangMgr:GetLang(107) or "";
                self.ui.m_txtMusicOff.text = v < 1 and LangMgr:GetLang(108) or "";
				SetUIImage(self.ui.m_imgMusic, v >= 1 and self.musicOnSp or self.musicOffSp, false);
				self.m_IsTouchedSlider = 1
			end
			slider1.onValueChanged:AddListener(onChange1)

			local function onChange2(v)
                self.ui.m_txtSoundOn.text = v >= 1 and LangMgr:GetLang(107) or "";
                self.ui.m_txtSoundOff.text = v < 1 and LangMgr:GetLang(108) or "";
				SetUIImage(self.ui.m_imgSound, v >= 1 and self.soundOnSp or self.soundOffSp, false);
				self.m_IsTouchedSlider = 2
			end
			slider2.onValueChanged:AddListener(onChange2)
		end

		slider1.value = state1
		slider2.value = state2
		self:UpdateUIOnOff(state1, state2)
	elseif animType == 1 then
		local function onDone()

		end

		local function onChange(v)
			slider1.value = (v / 100)
		end

		local from = state1
		local to = 1 - state1
		AddDOTweenNumberComplete(from * 100, to * 100, 0.1, onChange, onDone)

	elseif animType == 2 then
		local function onDone()

		end

		local function onChange(v)
			slider2.value = (v / 100)
		end

		local from = state2
		local to = 1 - state2
		AddDOTweenNumberComplete(from * 100, to * 100, 0.1, onChange, onDone)
	end
end



function setBtnItem:OnInit(transform)
	self.icon = transform:Find("icon"):GetComponent(typeof(UEUI.Image));
	self.btn = transform:Find("btn"):GetComponent(typeof(UEUI.Button));
	self.btnTxt = transform:Find("btn/btnTxt"):GetComponent(typeof(UEUI.Text));
    self.btnTxtAni = transform:Find("btn/btnTxt"):GetComponent(typeof(Animator));
	self.redDot = transform:Find("redDot");
    self.guideSp = transform:Find("guideSp");
    self.bindObj = transform:Find("bindObj");
    self.bindSp = transform:Find("bindObj/bindSp"):GetComponent(typeof(UEUI.Image));

	local function onBtnClick()
        self.btnTxtAni:SetTrigger("Pressed");
        self.btnTxtAni:SetTrigger("Normal");

		if self.data.open ~= nil then
			local arr = string.split(self.data.open, ';');
			local len = #arr;
			local str = arr[1];
			if len > 1 then
				if self.data.id == 7 then -- 关注我们
					NetTaskData:CheckTaskAdd(TaskFinishType.GoToFaceBook, SpecialId.GoToFaceBook)
					SdkHelper:ThinkingTrackEvent(ThinkingKey.open_fb, {["open_fb1"] = 1});
					if LangMgr:GetLangId() == 41 then
						str = arr[1]
					else
						if Game.IsTinyGame then 
							str = arr[2]
						elseif Game.IsRuGame then
							str = arr[4]
						else
							str = arr[3]
						end
					end
				elseif self.data.id == 9 then -- 常见问题
					local thinkTable = {["open_faq1"] = 1};
					SdkHelper:ThinkingTrackEvent(ThinkingKey.open_faq, thinkTable);
					if Game.IsTinyGame then
						str = arr[1]
					elseif Game.IsNativeTinyGame then
						str = arr[2]
					end
				elseif self.data.id == 10 then -- 隐私政策
					if LangMgr:GetLangId() == 22 then
						str = arr[1]
					elseif Game.IsTinyGame then 
						str = arr[2]
					elseif Game.IsNativeTinyGame then
						str = arr[3]
					end
				end
			end

			if string.contains(str, "http") then
				CS.UnityEngine.Application.OpenURL(str);
			else
                if self.data.id == 2 then -- 管理账号
                    UI_SHOW(str, self.showType);
                else
                	if self.data.id == 13 and not FriendManager:IsOpen() then -- 排行榜
                		local openLv = ConfigMgr:GetDataByID(ConfigDefine.ID.game_setting_rank_set, 4).value;
                		if NetUpdatePlayerData:GetLevel() < v2n(openLv) then
                			local desStr = LangMgr:GetLangFormat(7076, openLv);
                			UI_SHOW(UIDefine.UI_WidgetTip, desStr);
                			return;
                		end
                	end
					
					if self.data.id == 5 then
						if NetUpdatePlayerData:GetLevel() < GlobalConfig.OPEN_INVITE_FRIEND_LEVEL then
							local desStr = LangMgr:GetLangFormat(7076, GlobalConfig.OPEN_INVITE_FRIEND_LEVEL);
							UI_SHOW(UIDefine.UI_WidgetTip, desStr);
							return
						end
					end

                    UI_SHOW(str);
                end
				
			end
		else
			if self.data.id == 4 then -- 邮件
				UI_SHOW(UIDefine.UI_Mail)
			elseif self.data.id == 6 then -- 联系我们
				SdkHelper:ThinkingTrackEvent(ThinkingKey.click_contact, {});
				if SdkHelper:AIChatHelpAndShow() then
					NetGlobalData:SetDataByKey("ai_chat_red",false)
					--self:OpenAIChatService()
				else
					--if SdkHelper:AIHelpQ1_showWithApiConfig() then
					--else
					--	NetContactData:RequestInfomList(self.versionStr)
					--end
					NetContactData:RequestInfomList(self.versionStr)
				end
				SetActive(self.redDot, false);
				UIMgr:Refresh(UIDefine.UI_MainFace, 6)
			elseif self.data.id == 11 then -- 广告链接
				SdkHelper:ShowAdsConsentForm();
			elseif self.data.id == 12 then -- C#公告界面
				if IsNilOrEmpty(CS.StartGame.Instance.NoticeJsonStr) then
					UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(90200135));
				else
					CS.StartGame.Instance:OpenUINotice();
				end
			end
		end
	end
	self.btn.onClick:AddListener(onBtnClick);
end

function setBtnItem:OpenAIChatService()
	local sdkToken = _G.CS.StartGame.Instance:GetSDKToken()
	local HOST = "https://kefu-ea.q1.com"
	local path = "/aichat/index.html?"
	local SECRET = "Uz7NbStqvBx6WthB"

	local loginType = 0
	local platFormType = 1
	local mainType = 0
	local gameId = GAME_APP_ID
	local uld = _G.CS.StartGame.Instance:GetUserId()
	local actorId = v2s(NetUpdatePlayerData:GetPlayerID())
	local sign = GameUtil.MD5String(loginType..platFormType..mainType..gameId..actorId..SECRET)

	local str = string.format("gameId=%s&loginType=%s&platFormType=%s&mainType=%s&sign=%s&actorId=%s&sdkToken=%s",gameId,loginType,platFormType,mainType,sign,actorId,sdkToken)
	local url = HOST..path..str
	--CS.UnityEngine.Application.OpenURL(url)
	if CS.LuaSdkHelper.Instance.OpenH5Url then
		CS.LuaSdkHelper.Instance:OpenH5Url(url,1)
	end
end

function setBtnItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec;
end

function setBtnItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition;
end

function setBtnItem:UpdateData(data, index)
	if data == nil then return end;
	self.data = data;
	
	SetUIImage(self.icon, data.icon, true);
	
	if self.data.id == 10 and LangMgr:GetLangId() == 22 then -- 隐私政策
		self.btnTxt.text = "その他";
	else
		self.btnTxt.text =  LangMgr:GetLang(data.button);
	end

    if self.data.id == 2 then -- 管理账号
        local bindState = SdkHelper:IsBinding();
         if bindState > 0 then
            local spritePath;
            if bindState == 1 then
				spritePath = "Sprite/ui_setting/iconbutton2_Facebook.png";
            elseif bindState == 2 then
				spritePath = "Sprite/ui_setting/iconbutton2_Google.png";
            elseif bindState == 7 then
				spritePath = "Sprite/ui_setting/iconbutton2_Apple.png";
            else
				spritePath = "Sprite/ui_setting/iconbutton2_Twitter.png";
            end
			 SetUIImage(self.bindSp, spritePath, false);

            --SetUIPos(self.btnTxt, -152, 15);
            SetActive(self.bindObj, true);
            UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.bindObj.transform);
        else
            --SetUIPos(self.btnTxt, -152, 0);
            SetActive(self.bindObj, false);
        end
    end

    local showId;
    if self.showType == 1 then
		showId = 2; -- 管理账号
	elseif self.showType == 2 then
		showId = 7; -- 关注我们
	end
	if self.data.id == showId then
		SetActive(self.guideSp, true);
	end
	
	self:UpdateRedState();
end

function setBtnItem:onDestroy()
	self.btn.onClick:RemoveAllListeners();
	UEGO.Destroy(self.transform.gameObject);
end

function setBtnItem:UpdateRedState()
	local state = false;
	if self.data.id == 4 then
		state = RedPointMgr:IsRed(RedID.MailRoot)
	elseif self.data.id == 5 then
		state = NetInviteFriendData:CheckRedPoint()
	elseif self.data.id == 6 then
		--state = NetContactData:IsHavePushMsg();
		state = NetGlobalData:GetIsAIChatRed()
	elseif self.data.id == 13 then
		state = NetFriendData:GetNewDailyRewardRed();
	elseif self.data.id == 14 then
		state = NetInfoData:IsShowHeadRed() or NetHeadSeasonVip:IsShowRedPoint()
	end
	SetActive(self.redDot, state);
end

function setBtnItem:setParam(showType, versionStr)
    self.showType = showType;
	self.versionStr = versionStr;
end

return UI_Setting;
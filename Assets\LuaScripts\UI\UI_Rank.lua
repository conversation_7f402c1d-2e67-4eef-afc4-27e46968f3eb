local UI_Rank = Class(BaseView)
local M = UI_Rank
local BubbleItem = require("UI.BubbleItem")
local RankBox = require("UI.RankBox")
local RankBoxFlyAnim = require("UI.RankBoxFlyAnim")

local RewardState = {
    Normal = 1,
    Unclaimed = 2,
    Received = 3,
}


function M:OnInit()
    
end

function M:OnCreate(isPush)
    self.isDestroy = false
    self.isCheckClose = false;

    self.bgCanvasGroup = GetChild(self.uiGameObject, "bg", UE.CanvasGroup);
    self.bgCanvasGroup.alpha = 0;
    self.bgCanvasGroup.interactable = false;
	CreateCommonHead(GetChild(self.ui.m_goUp,"iconBg").transform,0.5)
	CreateCommonHead(GetChild(self.ui.m_goDown,"iconBg").transform,0.5)
	CreateCommonHead(GetChild(self.ui.m_goList,"iconBg").transform,0.5)
	CreateCommonHead(GetChild(self.ui.m_goRank,"iconBg").transform,0.5)
	local isOpen, activity = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.Rank);
	local activeId = activity.info.activeId;
    
    UIMgr:ShowWaiting(true);

    local function funBack()
        local param = {}
        param.id = activeId
        HttpClient:SendToGS("/active/activetranking", param, function(objJson)
            local isErr, code = HttpClient:GetNetJsonError(objJson)
            if isErr then
                -- UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(7062))
                SetActive(self.ui.m_imgNone, true);
                UIMgr:ShowWaiting(false);
                return
            end

            local data = objJson["data"]
            LimitActivityController:RefreshRank(param.id, data)
            self:InItUI(activeId, data, isPush);
        end)
    end

    if activity:GetRemainingTimeReal() <= 0 then
        funBack()
    else
        NetRankData:MergeRankPoint(activeId, activity.info.integral, funBack, true)
    end
end

function M:InItUI(activeId, rankMess, isPush)
    UIMgr:ShowWaiting(false);

    self.bgCanvasGroup.alpha = 1;
    self.bgCanvasGroup.interactable = true;

    self.rankBox = nil
    self.lastY = nil
    self.active = LimitActivityController:GetActiveMessage(activeId)
    self.activeInfo = self.active.info
    self.activeForm = self.active.form
    self.rankStage = self.activeInfo.rankStage
    self:SetItem(rankMess, isPush)
    self.rankConfig = self.activeForm.rankStage[self.rankStage]
    self.rewardList = {}
    self.m_childGo = nil
    self.initSelf = nil
    self.finishState = 0
    self.ui.m_txtTitle.text = LangMgr:GetLang(self.active.form.title)
    --SetImageSprite(self.ui.m_imgBox,self.activeForm.activeImg2,false)
	
    --self.rankStageConfig = GlobalConfig.RANK_STAGE[self.rankStage]
    --SetImageSprite(self.ui.m_imgRank,self.rankStageConfig[2],false)
    --if self.rankStageConfig[5]then
        --EffectConfig:CreateEffect(self.rankStageConfig[5],0,0,0,self.ui.m_imgRank.transform,function()
            --self:SortOrderAllCom()
        --end)
    --end
	self:OnRefreshRankImage(self.rankStage)
	
    self:InitNotHave()

	local imagePath = self.activeForm.activeMess.pass_icon2
	self:ChangeImage(self.ui.m_goList,imagePath)
	-- self:ChangeImage(self.ui.m_goUp,imagePath)
	-- self:ChangeImage(self.ui.m_goDown,imagePath)
	
    --local spine = self.ui.m_spuiLeft
    --local item = RoleSpineConfig:GetSkeletonDataById(self.activeForm.activeImg1)
    --spine.skeletonDataAsset = item.spine
    --spine:Initialize(true)

    self.bubble = BubbleItem.new("UI_Rank")
    self.bubble:Init(self.uiGameObject,{x= -480, y=410},function()
        self:BubbleClose();
    end)

    self:InitReward()

    if rankMess.ranking and #rankMess.ranking > 0  then
        SetActive(self.ui.m_goHave,true)
        SetActive(self.ui.m_goNotHave,false)
        self:InitUIList()
    else
        SetActive(self.ui.m_goHave,false)
        SetActive(self.ui.m_goNotHave,true)
    end
    self.deltaTime = 0

    local ranking = rankMess and v2n(rankMess.player.ranking) or 0
    if ranking > 0 then
        self:PlayFinishReward(true)
    else
        self:SetIsUpdateTick(true)
    end
    self:Timer(true)

    self:UpdateBoxRedPoint();
end

function M:ChangeImage(obj,path)
	local child = GetChild(obj,"Image")
	local image = GET_UI(child, "Image", TP(UEUI.Image))
	SetImageSprite(image,path,true)
end

function M:InitNotHave()
    --local str1 = string.split(LangMgr:GetLang(self.activeForm.endDes1) ,"|")
    --local pos = string.split(str1[2],",")
    self.ui.m_txtCr1.text =LangMgr:GetLang(self.activeForm.endDes1)
    --SetUIPos(self.ui.m_goCr1,pos[1],pos[2])
    --str1 = string.split(LangMgr:GetLang(self.activeForm.endDes2) ,"|")
   -- pos = string.split(str1[2],",")
    self.ui.m_txtCr2.text = LangMgr:GetLang(self.activeForm.endDes2)
    --SetUIPos(self.ui.m_goCr2,pos[1],pos[2])
    self.ui.m_txtCr3.text = LangMgr:GetLang(self.activeForm.endDes3)

    local listpoint = self.rankConfig.listpoint
    local progress = self.activeInfo.integral / listpoint;
    if progress > 1 then
        progress = 1;
    end
    SetUISize(self.ui.m_imgSlider2,progress * 567, 32)
    self.ui.m_txtProgress2.text = self.activeInfo.integral .. "/" .. listpoint
end

function M:Timer(init)
    local endTime = self.active:GetRemainingTime()
    endTime = math.max(endTime,0)
	local endTime2 = self.active:GetRemainingTimeReal()
    self.ui.m_txtCount.text = TimeMgr:CheckHMSNotEmpty(endTime2)
    if endTime <= 0 and self.finishState <= 0 then
        local ranking = v2n(self.rankMess.player.ranking)
        if ranking > 0 then
            self:PlayFinishReward(true)
        end
    end
end

function M:PlayFinishReward(isInit)
    self.finishState = 2
    if not isInit then
        self.ui.m_TableViewD:ReloadData(true)
        self.ui.m_TableViewD:ShowCenter(self.nowRank)
    end
    local rewardCfg = self.rankConfig.rankRewards
    local reward
	local thinkTable = {["activity_type"] = self.activeInfo.activeId,["activity_number"] = self.activeInfo.integral,
		["activity_id"] = self.nowRank,["lastStage"] = NetUpdatePlayerData:GetPlayerInfo().rankStage,
		["level"] = NetUpdatePlayerData:GetLevelMess() }
	SdkHelper:ThinkingTrackEvent(ThinkingKey.activity,thinkTable)
	
    for _, v in ipairs(rewardCfg) do
        if self.nowRank >= v.start  and  self.nowRank <= v.finish   then
            reward = v
            break
        end
    end
    if reward and self.activeInfo.rankIsReward == false then
        --self:CreateScheduleFun(function()
            local rankBox = RankBoxFlyAnim.new()
            rankBox:Init(self.uiGameObject,nil,reward.rewards,nil,1)
			if(IsNil(self.m_childGo)) then 
			 	rankBox:AnimFlyItem(reward.icon,Vector3.zero)
			else
            	rankBox:AnimFlyItem(reward.icon,self.m_childGo.transform.position)
			end
		
		local thinkTable = { ["activity_type"] = self.activeInfo.activeId,["activity_number"] = self.activeInfo.integral,
		["activity_id"] = self.nowRank,["lastStage"] = NetUpdatePlayerData:GetPlayerInfo().rankStage,
		["level"] = NetUpdatePlayerData:GetLevelMess() ,["rankReward"] = 1}
		SdkHelper:ThinkingTrackEvent("rankRewardActivity",thinkTable)
		
       -- end,0.5,1)
    else
        self:ReceiveUPReward()
		if self.activeInfo.rankIsReward == false then
			self:CheckRankLevel()
		end 
    end
end

function M:SetItem(rankMess,isPush)
    self.rankMess = rankMess
    self.nowRank = self.activeInfo.rank
	SdkHelper:UserSet(UserSet.rank_Merge,self.activeInfo.rank)
    if rankMess then
        self.lastStage = v2n(rankMess.player.lastStage)
    end
    self.isPush = isPush
end

function M:InitReward()
    local pointSch,sur = self:GetNowPointSch()
    local pointsList = self.rankConfig.rankPoints
    local isOver = pointSch >= #pointsList
    local nowMax = isOver and pointsList[pointSch] or pointsList[pointSch+1]
    local sumSur = 0
    if pointSch > 0 then
        sumSur = isOver and pointsList[pointSch-1] or pointsList[pointSch]
    end
    local per = sur/(nowMax - sumSur)
    local progress = math.min((pointSch + per)  * 0.2,1)
	if progress >= 1 then
		progress = 1
	end
    if isOver then
        SetActive(self.ui.m_goNormal,false)
        SetActive(self.ui.m_goSpe,true)
        self.ui.m_txtProgress1.text = self.activeInfo.integral
    else
        SetActive(self.ui.m_goNormal,true)
        SetActive(self.ui.m_goSpe,false)
        self.ui.m_txtProgress.text = self.activeInfo.integral .. "/" .. nowMax
    end
    SetUISize(self.ui.m_imgSlider,progress * 875, 32)
    local rewardList = self.rankConfig.rewards
    for i = 1, 5 do
        local item = {}
        item.img = self.ui["m_imgR" .. i]
        item.txt = self.ui["m_txtC" .. i]
        item.stateImg = self.ui["m_imgP"..i]
        item.reImg = self.ui["m_imgT"..i]
        item.mess = rewardList[i]
        SetImageSprite(item.img,ItemConfig:GetIcon(item.mess[1]),false)
        item.txt.text = "x" .. item.mess[2]
        self.rewardList[i] = item
        if i > pointSch then
            self:SetRewardState(item,RewardState.Normal)
        else
            if i > self.activeInfo.schedule and i <= pointSch then
                self:SetRewardState(item,RewardState.Unclaimed)
            else
                self:SetRewardState(item,RewardState.Received)
            end
        end
    end
end

function M:GetNowPointSch()
    local pointsList = self.rankConfig.rankPoints
    local nowSch = 0
    local integral = self.activeInfo.integral
    local sur = integral
    local temp
    for i, v in ipairs(pointsList) do
        temp = v2n(v)
        if integral >= temp then
            nowSch = i
        else
            break
        end
    end
    temp = nowSch > 0 and pointsList[nowSch] or 0
    sur = sur - temp
    return nowSch,sur
end

function M:SetRewardState(item,state)

    if state == RewardState.Normal then
        -- SetUIBtnGrayAndEnable(item.img,false)
        SetUIBtnGray(item.img, false);
        item.img.color = Color.New(1,1,1,1)
        SetActive(item.reImg,false)
        SetActive(item.stateImg,false)
    elseif state == RewardState.Unclaimed then
        -- SetUIBtnGrayAndEnable(item.img,true)
        SetUIBtnGray(item.img, true);
        item.img.color = Color.New(1,1,1,1)
        SetActive(item.reImg,true)
        SetActive(item.stateImg,false)
    elseif state == RewardState.Received then
        -- SetUIBtnGrayAndEnable(item.img,false)
        SetUIBtnGray(item.img, false);
        item.img.color = Color.New(1,1,1,0.5)
        SetActive(item.reImg,false)
        SetActive(item.stateImg,true)
    end
end

function M:InitUIList()
    local rankMess = self.rankMess
    local rankConfig = self.rankConfig

    local function SetRankItem(go, index, isNotTip, isShow)
        local mess = self.rankMess.ranking[index]
        local myBg = GetChild(go,"mybg")
        -- local imgBox = GetChild(go,"boxIcon",UEUI.Image)
        local boxObj = GetChild(go, "Scrollview/Viewport/boxObj");

        local nowRank = mess.rank
        if not isShow then
            if self.nowRank == index then
                SetActive(myBg,true)
                if not self.initSelf then
                    self.initSelf = true
                    -- SetRankItem(self.ui.m_goUp,index)
                    -- SetRankItem(self.ui.m_goDown,index)
                end
                if isNotTip then
                    -- self.m_childGo = imgBox.gameObject
                    self.m_childGo = boxObj;
                end
            else
                SetActive(myBg,false)
            end
        end

        local imgNum = GetChild(go,"imgNum",UEUI.Image)
        local num = GetChild(go,"num",UEUI.Text);
        if mess.rank <= 3 then
            SetActive(imgNum,true)
            SetImageSprite(imgNum, "Sprite/ui_huodongjingsai/paihang_win4_icon".. mess.rank .. ".png",false)
            num.text = ""
        else
            SetActive(imgNum,false)
            num.text = mess.rank
        end

        local change = GetChild(go,"icon/change",UEUI.Image)
        if rankConfig.promotion and nowRank <= rankConfig.promotion then
            SetImageSprite(change,"Sprite/ui_huodongjingsai/paihang_jiantou_up.png",true)
            SetActive(change,true)
        elseif rankConfig.demotion and mess.rank >= rankConfig.demotion then
            SetImageSprite(change,"Sprite/ui_huodongjingsai/paihang_jiantou_down.png",true)
            SetActive(change,true)
        else
            SetActive(change,false)
        end

        local name = GetChild(go,"iconBg/name",UEUI.Text);
        name.text = mess.name
        --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, mess.icon)
        --if headConfig then
            --SetImageSprite(GetChild(go,"iconBg/head",UEUI.Image),headConfig["icon"],false)
        --end
		local customHeadObj = GetChild(go,"iconBg/CustomHead")
		SetHeadAndBorderByGo(customHeadObj,mess.icon,mess.border)
		
        local point = GetChild(go,"Image/point",UEUI.Text);
        point.text = mess.point
        SetImageSprite(GetChild(go,"icon",UEUI.Image),self.rankStageConfig[1],false)

        local isSelf = self.nowRank == index;
        if isSelf then
            name.color = Color.New(213/255, 60/255, 0, 1); -- D53C00
            point.color = Color.New(213/255, 60/255, 0, 1); -- D53C00
        else
            name.color = Color.New(27/255, 94/255, 205/255, 1); -- 1B5ECD
            point.color = Color.New(27/255, 94/255, 205/255, 1); -- 1B5ECD
        end

        local index = 0
        if isNotTip then
            local canvasGroup = GetComponent(go,UE.CanvasGroup)
            canvasGroup.alpha = 1
        end

        for i, v in ipairs(rankConfig.rankRewards) do
            if nowRank >= v.start  and  nowRank <= v.finish then
                index = i
                -- SetImageSprite(imgBox,v.icon,false)
                break
            end
        end

        if index > 0 then
            local rewardStr;
            if self.activeInfo.openLevel >= RANKLEVEL then
                rewardStr = rankConfig.rankRewards[index].reward_1;
            else
                rewardStr = rankConfig.rankRewards[index].reward;
            end

            rewardStr = NetSeasonActivity:GetChangeItemId(rewardStr)
            local rewardList = string.split(rewardStr, ";")
            local count = #rewardList

            local len = boxObj.transform.childCount;
            local item;
            local bg;
            local btn;
            local icon;
            local numTxt;
            local numTxtOutline;
            local numTxtOutShadow;
            for i = 1, len do
                item = GetChild(boxObj, "boxIcon" .. i);
                bg = GetComponent(item, UEUI.Image);
                btn = GetComponent(item, UEUI.Button);
                icon = GetChild(item, "icon", UEUI.Image);
                numTxt = GetChild(item, "numTxt", UEUI.Text);
                numTxtOutline = GetChild(item, "numTxt", UEUI.Outline);
                numTxtOutShadow = GetChild(item, "numTxt", CS.Coffee.UIEffects.UIShadow);

                if i <= count then
                    local info = string.split(rewardList[i], "|");
                    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, v2n(info[1]));
                    SetImageSync(icon, itemConfig.icon_b, false);
                    numTxt.text = "x" .. info[2];

                    if isSelf then
                        SetImageSync(bg, "Sprite/ui_huodongjingsai/paihang_win4_liebiao2_iconbg.png", false);
                        numTxtOutline.effectColor = Color.New(189/255, 53/255, 0, 1); -- BD3500
                        numTxtOutShadow.effectColor = Color.New(189/255, 53/255, 0, 1); -- BD3500
                    else
                        SetImageSync(bg, "Sprite/ui_huodongjingsai/paihang_win4_liebiao1_iconbg.png", false);
                        numTxtOutline.effectColor = Color.New(27/255, 94/255, 205/255, 1); -- 1B5ECD
                        numTxtOutShadow.effectColor = Color.New(27/255, 94/255, 205/255, 1); -- 1B5ECD
                    end

                    local itemId = itemConfig.id;
                    RemoveUIComponentEventCallback(btn, UEUI.Button);
                    AddUIComponentEventCallback(btn, UEUI.Button, 
                        function(arg1, arg2)
                            if itemId > 0  then
                                UI_SHOW(UIDefine.UI_ItemTips, itemId);
                            end
                        end);
                end
                SetActive(item, i <= count);
            end
        end
        SetActive(boxObj, index > 0);
    end

    local count = #rankMess.ranking
    local newCount = count
    local rankLength = self.nowRank
    local upPer = rankConfig.promotion
    if upPer then
        if upPer > count then
            upPer = count
        end
        newCount = newCount + 1
        if rankLength > upPer then
            rankLength = rankLength +1
        end
    end

    local downPer = rankConfig.demotion
    local downCount = 0
    if downPer then
        if downPer > count then
            downPer  = nil
        else
            if count < downPer then
                downPer = nil
            else
                downCount = count - downPer + 1
                newCount = newCount + 1
                if rankLength >= downPer then
                    rankLength = rankLength +1
                end
                if upPer then
                    downPer = downPer + 1
                end
            end
        end
    end

    local height = 120
    local up = (rankLength - 1) * height - 10
    local down = (rankLength - 5) * height + 10
    if self.nowRank == 1 then
        up = up + 15
    elseif self.nowRank == count then
        down = down - 40
    end
    local initScroll = false

    if upPer then
        SetActive(self.ui.m_goFirstBg,true)
        SetUISize(self.ui.m_imgView1,955,upPer * height + 105)
    else
        SetActive(self.ui.m_goFirstBg,false)
    end
    if downCount > 0 then
        SetActive(self.ui.m_goLastBg,true)
        SetUISize(self.ui.m_imgView2,955,downCount * height + 105)
    else
        SetActive(self.ui.m_goLastBg,false)
    end
    self.ui.m_TableViewD_Scroll.onValueChanged:AddListener(function(newScrollValue)
        --local relativeScroll = 1 - newScrollValue.y
        --local m_scrollY = relativeScroll * scrollableHeight
        if self.lastY and self.rankBox  then
            local temp = math.abs(self.lastY - self.ui.m_rtransContent.anchoredPosition.y)
            if temp > 1 then
                self:SetBoxClose()
            end
        end
        self.lastY = self.ui.m_rtransContent.anchoredPosition.y
        local y = self.ui.m_rtransContent.anchoredPosition.y
        self.ui.m_rtransBgContent.anchoredPosition = self.ui.m_rtransContent.anchoredPosition
        if not initScroll then return end
        -- if y >= up then
        --     SetActive(self.ui.m_goUp,true)
        --     SetActive(self.ui.m_goDown,false)
        --     DOScale(self.ui.m_goUp.transform, Vector3.New(1.01, 1.01, 1), 0.2, nil, Ease.OutBack)
        -- elseif y <= down then
        --     SetActive(self.ui.m_goUp,false)
        --     SetActive(self.ui.m_goDown,true)
        --     DOScale(self.ui.m_goDown.transform, Vector3.New(1.01, 1.01, 1), 0.2, nil, Ease.OutBack)
        -- else
        --     self.ui.m_goUp.transform:SetLocalScale(1,1,1)
        --     self.ui.m_goDown.transform:SetLocalScale(1,1,1)

        --     SetActive(self.ui.m_goUp,false)
        --     SetActive(self.ui.m_goDown,false)
        -- end

    end)

    self.ui.m_TableViewD.GetCellCount = function() return newCount end

    self.ui.m_TableViewD.GetCellSize = function( tableView, index )
        if (upPer and index == upPer) or (downPer and index == downPer - 1) then
            return Vector2(1450, height / 2)
        end
        return Vector2(1450, height)
    end

    self.ui.m_TableViewD.UpdateCell = function( tableView,index )
        if not tableView then
            return nil
        end
        local cell = tableView:GetReusableCell()
        local chatObj
        if not cell then
            chatObj = UEGO.Instantiate(self.ui.m_goList)
            SetActive(chatObj,true)
			self:ChangeImage(chatObj,self.activeForm.activeMess.pass_icon2)
            cell = GetAndAddComponent(chatObj,CS.CCTableViewCell)
            chatObj.name = v2s(index + 1)
        end
        local upValue = 0
        local isCreate = false
        function SetSpecialObj(cell)
            isCreate = true
            local canvasGroup = GetComponent(cell.gameObject,UE.CanvasGroup)
            canvasGroup.alpha = 0
        end
        if upPer  then
            if index == upPer then
                SetSpecialObj(cell)
            elseif index > upPer then
                upValue = upValue - 1
            end
        end
        if downPer then
            if index == downPer - 1 then
                SetSpecialObj(cell)
            elseif index > downPer - 1 then
                upValue = upValue - 1
            end
        end
        if not isCreate then
            SetRankItem(cell.gameObject,index+1+upValue,true)
        end
        return cell
    end
    self.ui.m_TableViewD:ReloadData(true)
    self.ui.m_rtransBgContent.sizeDelta = self.ui.m_rtransContent.sizeDelta
    local lastRank = NetInfoData:GetDataMess(NetInfoData.Define.LastRank)
    if lastRank then
        self.ui.m_TableViewD:ShowCenter(lastRank)
        if lastRank ~= self.nowRank then
            self.ui.m_TableViewD:ShowCenter(self.nowRank,function()
                NetInfoData:SetDataMess(NetInfoData.Define.LastRank,self.nowRank)
                initScroll = true
            end)
        else
            initScroll = true
        end
    else
        self.ui.m_TableViewD:ShowCenter(self.nowRank,function()
            NetInfoData:SetDataMess(NetInfoData.Define.LastRank,self.nowRank)
            initScroll = true
        end)
    end

    local isShow = self.nowRank > 0 and self.nowRank <= count;
    if isShow  then
        SetRankItem(self.ui.m_goRank, self.nowRank, false, true);
    end
    SetActive(self.ui.m_goRank, isShow);
    
    local TableCtrl = GetChild(self.ui.m_goHave, "TableCtrl", UE.RectTransform);
    TableCtrl.offsetMin = Vector2.New(TableCtrl.offsetMin.x, isShow and 480 or 325);
end

function M:SetBoxClose()
    if self.rankBox then
        self.rankBox:Destory()
        self.rankBox = nil
    end
end

function M:OnRefresh(type,param,isPush)
    if type == 1 then
        --Game.StopSave(100)
        for _, v in ipairs(param) do
            if v.Id < ItemID._RESOURCE_MAX then
                MapController:AddResourceBoomAnim(v.Pos.x,v.Pos.y,v.Id,v.count)
                NetUpdatePlayerData:AddResource(PlayerDefine[v.Id], v.count,nil,nil,"UI_Rank")
            else
                self.bubble:FlyItem(v.Id,v.count,v.Pos)
            end
        end
		self.active:ClaimRankReward()
        self:ReceiveUPReward()
        self:CheckRankLevel()
		
		local thinkTable = { ["activity_type"] = self.activeInfo.activeId,["activity_number"] = self.activeInfo.integral,
			["activity_id"] = self.nowRank,["lastStage"] = NetUpdatePlayerData:GetPlayerInfo().rankStage,
			["level"] = NetUpdatePlayerData:GetLevelMess() ,["rankReward"] = 2}
		SdkHelper:ThinkingTrackEvent("rankRewardActivity",thinkTable)
		
    elseif type == 2 then
        self:SetItem(param,isPush)
    elseif type == 3 then
        self:CheckClose();
    elseif type == 4 then
        self:UpdateBoxRedPoint();
    end
end

function M:CheckRankLevel()
    UIMgr:SetUILock(true, UIDefine.UI_Rank)
    local new = self.lastStage
    if new == 0 then Log.Error("==========最终段位为0===========",new) end
    if new == self.rankStage then
        UIMgr:SetUILock(false, UIDefine.UI_Rank)
        return
    else
        self:CreateScheduleFun(function()
            UIMgr:SetUILock(false, UIDefine.UI_Rank)
			self:OnRefreshRankImage(new)
            UI_SHOW(UIDefine.UI_RankLevel,new,self.rankStage)
        end,1.2,1)
    end
end

function M:onDestroy()
    self:ClosePush()

    if not self.isCheckClose and self.bubble and self.bubble:IsHaveItem() then
        self.bubble:SetCloseCall(nil);
        self.bubble:CloseImmediately();
    end

    UIMgr:RefreshAllMainFace(48);
end

function M:TickUI(deltaTime)
    if self.init then
        self.init = self.init + deltaTime
    end
    if self.init and self.init > 2 then
        self.init = nil

    end
    self.deltaTime = deltaTime + self.deltaTime
    if self.deltaTime > 0.5 then
        self.deltaTime = 0
        self:Timer()
    end
end

function M:ReceiveUPReward(index)
    local pointSch = self:GetNowPointSch()
    if pointSch > self.activeInfo.schedule then
        local now = self.activeInfo.schedule + 1
		self.active:SetSchedule(pointSch)
        for i = pointSch,now,-1 do
            local item = self.rewardList[i]
            self.bubble:FlyItem(item.mess[1],item.mess[2],item.img.transform.position)
            self:SetRewardState(item,RewardState.Received)
			
			--local thinkTable = {["activity_type"] = self.activeInfo.activeId,["activity_number"] = self.activeInfo.integral,["activity_id"] = now}
			--SdkHelper:ThinkingTrackEvent(ThinkingKey.activity,thinkTable)
			--Log.Error("thinkTable",thinkTable)
        end
    elseif index then
        index = v2n(index);
        if index and index > 0 then
			local item = self.rewardList[index];
            if item and item.mess[1] > 0 then
                UI_SHOW(UIDefine.UI_ItemTips, item.mess[1]);
            end
        end
    end
end

function M:onUIEventClick(go,param)
    local name = go.name
    if name == "close" or name == "closeBtn" then
        self:CheckClose()
    elseif name == "m_imgRank" or name == "m_imgBox" then
        UI_SHOW(UIDefine.UI_RankDetail,self.rankStage)
	elseif name == "btnBg" then
		UI_SHOW(UIDefine.UI_RankHelp, self.activeInfo.activeId)
    elseif string.startswith(name,"m_imgR")then
        local index = string.gsub(name, "m_imgR", "");
		self:ReceiveUPReward(index)
    end
    self:SetBoxClose()

end

function M:OnRefreshRankImage(rankStage)
    self.rankStageConfig = GlobalConfig.RANK_STAGE[rankStage]
    if self.rankStageConfig then
        SetImageSprite(self.ui.m_imgRank, self.rankStageConfig[2], false)
        if self.rankStageConfig[5] then
            EffectConfig:CreateEffect(self.rankStageConfig[5], 0, 0, 0, self.ui.m_imgRank.transform, function()
                self:SortOrderAllCom()
            end)
        end
    end
end

function M:CheckClose()
    if self.rankBox then
        self:SetBoxClose()
    end

    if self.bubble and self.bubble:IsHaveItem() then
        self.bubble:Close()
    else
        self.isCheckClose = true;
        UI_CLOSE(UIDefine.UI_ActivityRankCenter);
    end
end

function M:AutoClose()
    self:CheckClose()
end

function M:ClosePush()
    Log.Info("============ClosePush===================",self.rankMess)
    -- if self.isPush then
    --     NetPushViewData:RemoveViewByIndex(PushDefine.Rank)
    --     NetPushViewData:CheckOtherView(true)
    -- end
    Game.StopSave(0)
    if self.isDestroy == false and self.active and self.active:IsActivityEnd() and self.finishState >= 2 then
        self.isDestroy = true
        NetInfoData:SetDataMess(NetInfoData.Define.LastRank,nil)
        NetUpdatePlayerData:GetPlayerInfo().rankStage = self.lastStage
        EventMgr:Dispatch(EventID.RANK_CHANGE)
        self.active:CloseActive()
    end
end

function M:UpdateBoxRedPoint()
    SetActive(self.ui.m_imgBoxRed, NetUpdatePlayerData:GetPlayerRankOneRewardRed());
end

function M:BubbleClose()
    self.isCheckClose = true;
    UI_CLOSE(UIDefine.UI_ActivityRankCenter);
end

return M
{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17984, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17984, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17984, "tid": 135245, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17984, "tid": 135245, "ts": 1758538010698583, "dur": 409, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17984, "tid": 135245, "ts": 1758538010701017, "dur": 421, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17984, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17984, "tid": 1, "ts": 1758538008130435, "dur": 3227, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17984, "tid": 1, "ts": 1758538008133664, "dur": 14806, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17984, "tid": 1, "ts": 1758538008148478, "dur": 23659, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17984, "tid": 135245, "ts": 1758538010701441, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 17984, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008129323, "dur": 5308, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008134632, "dur": 2559406, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008135695, "dur": 2175, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008137874, "dur": 954, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008138831, "dur": 183, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139016, "dur": 5, "ph": "X", "name": "ProcessMessages 20507", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139022, "dur": 15, "ph": "X", "name": "ReadAsync 20507", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139038, "dur": 14, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139054, "dur": 10, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139066, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139091, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139109, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139129, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139142, "dur": 11, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139155, "dur": 10, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139166, "dur": 10, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139178, "dur": 11, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139190, "dur": 10, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139201, "dur": 13, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139216, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139228, "dur": 12, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139242, "dur": 10, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139254, "dur": 12, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139268, "dur": 25, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139294, "dur": 12, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139308, "dur": 9, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139318, "dur": 9, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139328, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139339, "dur": 9, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139349, "dur": 9, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139359, "dur": 15, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139376, "dur": 11, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139388, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139405, "dur": 9, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139415, "dur": 8, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139424, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139445, "dur": 10, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139456, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139473, "dur": 11, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139486, "dur": 10, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139497, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139517, "dur": 9, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139527, "dur": 12, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139541, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139553, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139567, "dur": 10, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139579, "dur": 8, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139588, "dur": 16, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139606, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139619, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139638, "dur": 11, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139650, "dur": 11, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139663, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139681, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139702, "dur": 11, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139714, "dur": 15, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139731, "dur": 10, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139742, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139753, "dur": 12, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139767, "dur": 9, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139777, "dur": 10, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139789, "dur": 11, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139801, "dur": 13, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139816, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139841, "dur": 10, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139852, "dur": 10, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139863, "dur": 10, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139874, "dur": 9, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139885, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139898, "dur": 9, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139908, "dur": 10, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139919, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139955, "dur": 11, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139967, "dur": 11, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139979, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008139995, "dur": 9, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140005, "dur": 10, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140017, "dur": 13, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140031, "dur": 10, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140042, "dur": 10, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140053, "dur": 10, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140064, "dur": 15, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140081, "dur": 9, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140091, "dur": 11, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140104, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140114, "dur": 9, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140125, "dur": 9, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140136, "dur": 9, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140146, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140147, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140158, "dur": 9, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140169, "dur": 11, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140181, "dur": 11, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140193, "dur": 12, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140207, "dur": 9, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140217, "dur": 10, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140229, "dur": 7, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140237, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140249, "dur": 9, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140259, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140270, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140281, "dur": 9, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140291, "dur": 9, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140302, "dur": 9, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140313, "dur": 7, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140322, "dur": 11, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140335, "dur": 10, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140346, "dur": 10, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140357, "dur": 11, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140369, "dur": 10, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140381, "dur": 10, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140392, "dur": 8, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140402, "dur": 11, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140414, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140427, "dur": 10, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140438, "dur": 12, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140452, "dur": 11, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140464, "dur": 13, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140479, "dur": 10, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140491, "dur": 11, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140503, "dur": 16, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140522, "dur": 25, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140548, "dur": 16, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140566, "dur": 10, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140577, "dur": 12, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140591, "dur": 10, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140602, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140613, "dur": 13, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140628, "dur": 29, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140658, "dur": 13, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140673, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140684, "dur": 12, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140698, "dur": 12, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140712, "dur": 13, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140727, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140728, "dur": 16, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140746, "dur": 12, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140760, "dur": 13, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140775, "dur": 11, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140787, "dur": 10, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140799, "dur": 10, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140810, "dur": 10, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140822, "dur": 11, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140834, "dur": 11, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140847, "dur": 11, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140859, "dur": 9, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140870, "dur": 12, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140884, "dur": 10, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140895, "dur": 11, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140907, "dur": 10, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140919, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140932, "dur": 10, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140943, "dur": 9, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140953, "dur": 9, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140964, "dur": 11, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140977, "dur": 10, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008140989, "dur": 11, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141001, "dur": 10, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141013, "dur": 10, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141024, "dur": 11, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141037, "dur": 9, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141048, "dur": 9, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141058, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141070, "dur": 10, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141082, "dur": 17, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141102, "dur": 11, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141115, "dur": 10, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141127, "dur": 31, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141160, "dur": 9, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141170, "dur": 10, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141182, "dur": 10, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141193, "dur": 31, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141226, "dur": 10, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141238, "dur": 9, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141248, "dur": 9, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141258, "dur": 11, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141270, "dur": 10, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141282, "dur": 10, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141293, "dur": 13, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141308, "dur": 10, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141319, "dur": 11, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141331, "dur": 10, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141343, "dur": 9, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141353, "dur": 29, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141384, "dur": 9, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141394, "dur": 28, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141424, "dur": 11, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141437, "dur": 10, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141448, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141459, "dur": 8, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141470, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141482, "dur": 10, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141493, "dur": 10, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141505, "dur": 11, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141517, "dur": 14, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141533, "dur": 10, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141544, "dur": 9, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141556, "dur": 11, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141569, "dur": 13, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141583, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141584, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141601, "dur": 10, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141613, "dur": 10, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141624, "dur": 11, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141637, "dur": 10, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141649, "dur": 9, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141660, "dur": 13, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141674, "dur": 10, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141687, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141705, "dur": 11, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141718, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141720, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141744, "dur": 10, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141756, "dur": 9, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141766, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141779, "dur": 10, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141792, "dur": 11, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141804, "dur": 11, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141817, "dur": 11, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141830, "dur": 11, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141843, "dur": 11, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141856, "dur": 12, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141870, "dur": 12, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141884, "dur": 12, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141897, "dur": 13, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141912, "dur": 12, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141926, "dur": 16, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141944, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141963, "dur": 12, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141976, "dur": 11, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008141989, "dur": 12, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142003, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142026, "dur": 9, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142037, "dur": 10, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142049, "dur": 10, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142060, "dur": 10, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142072, "dur": 10, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142083, "dur": 9, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142094, "dur": 9, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142104, "dur": 12, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142117, "dur": 19, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142138, "dur": 12, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142151, "dur": 10, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142164, "dur": 12, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142178, "dur": 10, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142190, "dur": 10, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142202, "dur": 71, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142275, "dur": 24, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142301, "dur": 12, "ph": "X", "name": "ReadAsync 1448", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142314, "dur": 9, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142325, "dur": 11, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142337, "dur": 12, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142351, "dur": 10, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142363, "dur": 70, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142435, "dur": 22, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142459, "dur": 12, "ph": "X", "name": "ReadAsync 2008", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142473, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142492, "dur": 12, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142506, "dur": 11, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142518, "dur": 13, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142535, "dur": 16, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142554, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142556, "dur": 369, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008142927, "dur": 86, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143017, "dur": 4, "ph": "X", "name": "ProcessMessages 11228", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143022, "dur": 14, "ph": "X", "name": "ReadAsync 11228", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143039, "dur": 12, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143054, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143077, "dur": 17, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143097, "dur": 11, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143110, "dur": 17, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143129, "dur": 18, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143149, "dur": 15, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143166, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143183, "dur": 12, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143197, "dur": 13, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143213, "dur": 11, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143227, "dur": 30, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143261, "dur": 37, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143300, "dur": 12, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143314, "dur": 12, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143328, "dur": 29, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143359, "dur": 10, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143372, "dur": 14, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143388, "dur": 12, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143402, "dur": 13, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143416, "dur": 10, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143428, "dur": 11, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143440, "dur": 14, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143456, "dur": 15, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143472, "dur": 11, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143486, "dur": 49, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143537, "dur": 12, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143552, "dur": 11, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143565, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143584, "dur": 23, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143609, "dur": 12, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143623, "dur": 14, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143639, "dur": 23, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143664, "dur": 15, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143682, "dur": 12, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143696, "dur": 12, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143710, "dur": 10, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143722, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143736, "dur": 12, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143749, "dur": 11, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143762, "dur": 29, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143794, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143811, "dur": 11, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143824, "dur": 12, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143838, "dur": 11, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143851, "dur": 10, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143863, "dur": 11, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143876, "dur": 12, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143890, "dur": 12, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143903, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143919, "dur": 12, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143932, "dur": 12, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143945, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143947, "dur": 11, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143960, "dur": 16, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143978, "dur": 16, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008143996, "dur": 14, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144013, "dur": 11, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144027, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144042, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144055, "dur": 148, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144204, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144220, "dur": 11, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144232, "dur": 10, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144245, "dur": 10, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144257, "dur": 16, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144274, "dur": 10, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144286, "dur": 13, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144301, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144312, "dur": 192, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144507, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144521, "dur": 274, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144796, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144824, "dur": 2, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144827, "dur": 42, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144873, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144887, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144905, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144916, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144928, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144940, "dur": 8, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144951, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144976, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144987, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008144999, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145080, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145082, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145112, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145113, "dur": 89, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145205, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145222, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145238, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145251, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145267, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145284, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145301, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145316, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145330, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145344, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145358, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145371, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145387, "dur": 15, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145404, "dur": 12, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145418, "dur": 12, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145432, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145446, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145459, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145473, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145490, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145504, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145518, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145567, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145571, "dur": 17, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145589, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145606, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145620, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145633, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145646, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145675, "dur": 11, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145689, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145703, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145719, "dur": 11, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145732, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145749, "dur": 13, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145763, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145784, "dur": 11, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145796, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145824, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145837, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145852, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145865, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145878, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145895, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145909, "dur": 11, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145921, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145936, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145950, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145963, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145976, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008145988, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146001, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146019, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146020, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146054, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146071, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146083, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146203, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146214, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146256, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146269, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146295, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146308, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146461, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146471, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146543, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146556, "dur": 159, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146716, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146729, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146846, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008146858, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147038, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147051, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147096, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147111, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147143, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147156, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147168, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147184, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147196, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147236, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008147250, "dur": 2598, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149852, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149863, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149868, "dur": 2, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149872, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149888, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149971, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149972, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008149990, "dur": 247, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150241, "dur": 344, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150588, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150635, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150637, "dur": 10, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150648, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150649, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150673, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150691, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150701, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150780, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150793, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150814, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150826, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150838, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150867, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150892, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150915, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150927, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150960, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150974, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008150990, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151001, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151021, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151032, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151042, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151091, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151103, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151138, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151150, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151161, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151182, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151194, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151388, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151400, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151456, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151467, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151520, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151531, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151556, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151567, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151592, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151603, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151696, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151710, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151774, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151785, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151807, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151819, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151840, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151851, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151867, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151880, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151892, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151903, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151914, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151928, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151944, "dur": 11, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151956, "dur": 11, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151968, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151981, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008151993, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152031, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152042, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152086, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152097, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152146, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152158, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152227, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152240, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152252, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152266, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152279, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152309, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152319, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152331, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152345, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152356, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152489, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152506, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152579, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152591, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152603, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152618, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152631, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152649, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152666, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152682, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152701, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152711, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152740, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152753, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152791, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152805, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152827, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152838, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152850, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152868, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152880, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152891, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152912, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152924, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008152935, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153007, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153023, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153128, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153150, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153173, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153189, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153206, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153226, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153278, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153294, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153321, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153337, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153370, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153385, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153410, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153426, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153567, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153590, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153612, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153629, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153649, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153765, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153781, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153816, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153830, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153832, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153848, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153860, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153960, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008153987, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154021, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154040, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154053, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154066, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154091, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154116, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154118, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154141, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154156, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154175, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154189, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154204, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154236, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154253, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154267, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154327, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154350, "dur": 208, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154561, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154572, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154579, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154588, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154597, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154614, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154658, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154669, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154688, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154699, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154709, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154868, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154878, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154895, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154906, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008154914, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008155168, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008155169, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008155182, "dur": 828, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008156012, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008156023, "dur": 364, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538008156388, "dur": 2145818, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010302211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010302214, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010302276, "dur": 5542, "ph": "X", "name": "ProcessMessages 9959", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010307822, "dur": 33, "ph": "X", "name": "ReadAsync 9959", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010307857, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010307858, "dur": 1352, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010309216, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010309241, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010309252, "dur": 31041, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010340297, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010340300, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010340336, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010340338, "dur": 347044, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010687387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010687389, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010687407, "dur": 1428, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010688840, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010688842, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010688860, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010688875, "dur": 15, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010688893, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010688894, "dur": 529, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010689426, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010689459, "dur": 232, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758538010689692, "dur": 3838, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17984, "tid": 135245, "ts": 1758538010701448, "dur": 480, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17984, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17984, "tid": 8589934592, "ts": 1758538008127705, "dur": 44476, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17984, "tid": 8589934592, "ts": 1758538008172183, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17984, "tid": 8589934592, "ts": 1758538008172186, "dur": 881, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17984, "tid": 135245, "ts": 1758538010701928, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17984, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17984, "tid": 4294967296, "ts": 1758538008114016, "dur": 2580588, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758538008117276, "dur": 5965, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758538010694611, "dur": 2443, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758538010696007, "dur": 55, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758538010697095, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17984, "tid": 135245, "ts": 1758538010701933, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758538008134125, "dur": 742, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758538008134875, "dur": 317, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758538008135260, "dur": 530, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758538008136344, "dur": 2269, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_A38B4233660E9CB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758538008139438, "dur": 887, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1758538008144247, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1758538008135805, "dur": 9812, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758538008145624, "dur": 2544831, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758538010690729, "dur": 718, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758538008135639, "dur": 9992, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008145650, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758538008145710, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1758538008145644, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758538008145776, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008146464, "dur": 613, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1758538008147110, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758538008147272, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008147975, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008148083, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008149625, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008150494, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008151014, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008151213, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008151655, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008151861, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008153203, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008153297, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008153973, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1758538008154072, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/IngameDebugConsole.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1758538008154138, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008154237, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008154622, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008154707, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008155096, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008155379, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008155487, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008155592, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008156020, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538008156259, "dur": 2151679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758538010307938, "dur": 382314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008135939, "dur": 10358, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008146409, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1758538008146531, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008146663, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008146842, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1758538008147050, "dur": 798, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008147851, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008147973, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008148853, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008149062, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008149447, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\UnitTesting\\SyncTestRunEventsHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758538008150456, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\UnitTesting\\CallbackInitializer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758538008149161, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008151045, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008151193, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008151255, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008151593, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008152204, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758538008152300, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758538008152400, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758538008152491, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758538008152766, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758538008152829, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758538008152901, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758538008154319, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008154502, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758538008154702, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758538008155085, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758538008154478, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758538008155364, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008155497, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008155558, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008155611, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008155993, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538008156277, "dur": 2151650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758538010307942, "dur": 544, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1758538010307942, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1758538010308511, "dur": 2009, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1758538010310523, "dur": 379734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008135678, "dur": 9969, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008145653, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758538008145706, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1758538008145652, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758538008145767, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008146184, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758538008146183, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_78AB6F32676F7162.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758538008146426, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_78AB6F32676F7162.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758538008146856, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758538008146965, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758538008147299, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008147722, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008148643, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008148959, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008149311, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008149687, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\OverlayRect.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758538008149495, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008150995, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008151220, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008151650, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008151862, "dur": 1133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008152995, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758538008153078, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008153257, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008153993, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008154083, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.DOTween.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1758538008154153, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008154265, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008154616, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008154711, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008155091, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008155171, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008155367, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008155493, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008155564, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008156038, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538008156240, "dur": 2151721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758538010307961, "dur": 382377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008135982, "dur": 10525, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008146604, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008146708, "dur": 1151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008147906, "dur": 1004, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\SDK\\View\\AgreeWidget.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758538008147861, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008149638, "dur": 988, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\Interface\\IGL.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758538008149246, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008150915, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008151232, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008151621, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008152456, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758538008152866, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758538008152980, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758538008153082, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Sprite.Editor.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758538008153081, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_DDBAA27A82CE9E28.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758538008153350, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008154272, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008154642, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008155097, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008155370, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008155476, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008155586, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008156024, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538008156299, "dur": 2151665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758538010307964, "dur": 382329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008135720, "dur": 9939, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008145669, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758538008145665, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758538008145772, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008146399, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008146644, "dur": 728, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008147377, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758538008147613, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\Editor\\DemiEditor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758538008148153, "dur": 785, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758538008149007, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758538008149325, "dur": 1542, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758538008150901, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758538008147574, "dur": 3548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758538008151185, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008151254, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008151575, "dur": 1688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008153263, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008154350, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008154638, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008155098, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008155363, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008155459, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008155600, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008155982, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538008156331, "dur": 2151614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758538010307945, "dur": 382446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008135740, "dur": 9956, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008145716, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1758538008145699, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758538008145869, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008146138, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758538008146414, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758538008146733, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008146999, "dur": 1402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008148403, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008148531, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\TimeoutController.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758538008149447, "dur": 1278, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Internal\\UnityWebRequestExtensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758538008148513, "dur": 2372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008150885, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008151239, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008151614, "dur": 1445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008153060, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758538008153475, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758538008154095, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008154231, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008154636, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008155100, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008155361, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008155472, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008155594, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008156003, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538008156324, "dur": 2151663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758538010307988, "dur": 382345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008135753, "dur": 9948, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008145705, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758538008145764, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008146258, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758538008146257, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758538008146440, "dur": 928, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758538008147369, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008147930, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008148988, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008149197, "dur": 1273, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\Discovery.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758538008149171, "dur": 1949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008151121, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008151192, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008151252, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008151582, "dur": 1633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008153255, "dur": 1097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008154352, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008154611, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008154687, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008155097, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008155371, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008155487, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008155585, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008156030, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538008156284, "dur": 2151653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758538010307937, "dur": 382467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008135771, "dur": 9966, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008145753, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008146052, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008146248, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008146429, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758538008146792, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008146856, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758538008146920, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008146980, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008147084, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008147185, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008147256, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008147324, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008147518, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\StorageManagerWrap.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758538008147518, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008148288, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008149733, "dur": 1074, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Noise\\classicnoise4D.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758538008149733, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008151219, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008151639, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008151987, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758538008152053, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008152198, "dur": 2160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\FlexReader\\ICSharpCode.SharpZipLib.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758538008154840, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758538008152134, "dur": 3056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758538008155191, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008155369, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758538008155451, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758538008155901, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758538008155998, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758538008156287, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758538008156192, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758538008157015, "dur": 80, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538008157336, "dur": 2146625, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758538010308157, "dur": 33269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758538010307927, "dur": 33588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758538010341516, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758538010341595, "dur": 348734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008135786, "dur": 9959, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008145761, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008146241, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008146439, "dur": 933, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_036EBEF6E01DAAEF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758538008147376, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758538008147604, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008148135, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008148329, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008148712, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008148803, "dur": 476, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008149287, "dur": 1567, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008150901, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008147511, "dur": 3653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758538008151266, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758538008151645, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758538008151923, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758538008152138, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008152235, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758538008152389, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008152467, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758538008152944, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008153000, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758538008153126, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008153274, "dur": 958, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.Serialization.Config.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008154253, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\UnityChannel\\ChannelPurchase.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008154399, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008154600, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758538008153199, "dur": 1759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758538008154959, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008155150, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008155356, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008155412, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008155483, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008155591, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008156013, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538008156305, "dur": 2151645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758538010307950, "dur": 382442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008135806, "dur": 9949, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008145825, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008146001, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008146146, "dur": 526, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_17AA03F72B124B32.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758538008146838, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758538008147007, "dur": 1456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008148465, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008148773, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008149503, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008149671, "dur": 1144, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\BuildGetEventExtraInfoFunction.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758538008149604, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008150902, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008151235, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008151620, "dur": 1311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008152931, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758538008153049, "dur": 1226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008154600, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\AppendPrepend.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758538008154276, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758538008154784, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008155478, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008155580, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008156036, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538008156234, "dur": 2151694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538010307929, "dur": 33659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758538010341610, "dur": 348713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008135817, "dur": 9963, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008146170, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008146255, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758538008146254, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_328CD27C104796DE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758538008146425, "dur": 306, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_328CD27C104796DE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758538008146846, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1758538008146966, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1758538008147311, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008147942, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008148594, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008148837, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008148945, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008149045, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008149154, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008149633, "dur": 833, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Scripts\\Runtime\\Core\\Opcode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758538008149249, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008150886, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008151239, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008151613, "dur": 1476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008153207, "dur": 765, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758538008154048, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758538008153089, "dur": 1356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1758538008154445, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008154600, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758538008154757, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758538008154527, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1758538008155232, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008155367, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008155460, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008155626, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008155922, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758538008156011, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538008156313, "dur": 2151663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758538010307976, "dur": 382374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008135833, "dur": 9967, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008146119, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_21B3FB5693DEDB8E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758538008146248, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008146302, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758538008146301, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_79178A3807C7FCA5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758538008146477, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_79178A3807C7FCA5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758538008146898, "dur": 1253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008148155, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008148380, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008149726, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint4.gen.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758538008149726, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008150846, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008151246, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008151595, "dur": 1536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008153131, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758538008153198, "dur": 1226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008154882, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758538008154429, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758538008155014, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008155156, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008155359, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008155468, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008155592, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008156012, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538008156307, "dur": 2151637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758538010307944, "dur": 382499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008135859, "dur": 10266, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008146148, "dur": 396, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E2C9DE69A35782B8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758538008146728, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008146863, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1758538008146924, "dur": 1236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008148163, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008148688, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\Tools\\Explorer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758538008148273, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008149627, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008150995, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008151225, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008151634, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008152149, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758538008152235, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758538008152459, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758538008152705, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758538008152922, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008153261, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008153913, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008153968, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.dll"}}, {"pid": 12345, "tid": 13, "ts": 1758538008154055, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityWebSocket.Runtime.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1758538008154054, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityWebSocket.Runtime.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1758538008154179, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008154509, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758538008154753, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008154882, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\Editor\\DemiEditor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1758538008154879, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758538008155385, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008155469, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008155590, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008156023, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538008156300, "dur": 2151681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758538010307981, "dur": 382473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008135873, "dur": 10269, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008146197, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008146443, "dur": 558, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_A6E727B0B6898769.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758538008147003, "dur": 1338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008148344, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008148894, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008149005, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008149102, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008149459, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008149570, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008150589, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008150692, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008150842, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008151247, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008151634, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008152199, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758538008152350, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758538008152444, "dur": 847, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758538008153291, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1758538008153539, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1758538008153766, "dur": 818, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008154681, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008155160, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008155402, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008155456, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008155606, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008155890, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008156045, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008156210, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538008156490, "dur": 2151466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758538010307957, "dur": 382462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008135887, "dur": 10306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008146258, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1758538008146258, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758538008146422, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1758538008146653, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008147230, "dur": 1259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008148491, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008149297, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008150461, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Diff\\DiffPanel.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758538008149401, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008151009, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008151219, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008151641, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008151930, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1758538008152177, "dur": 1088, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008153267, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758538008153676, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 15, "ts": 1758538008153394, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1758538008153814, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008153890, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008153997, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758538008154105, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1758538008154341, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008154674, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008155092, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008155154, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008155363, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008155465, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008155600, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008155975, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538008156338, "dur": 2151596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758538010307934, "dur": 382516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008135905, "dur": 10364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008146274, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758538008146271, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758538008146457, "dur": 572, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758538008147293, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008147442, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008147542, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008147764, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008147867, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008148362, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008148979, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008149074, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008149169, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008149457, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008149693, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008150122, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008150228, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008150329, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008150798, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008150858, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008151245, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008151601, "dur": 1501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008153149, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758538008153256, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008153714, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1758538008153942, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008154012, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008154637, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008155095, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008155376, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008155451, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008155606, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008155917, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758538008156005, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538008156319, "dur": 2151627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758538010307947, "dur": 382490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008135919, "dur": 10362, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008146286, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758538008146283, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758538008146475, "dur": 508, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758538008147038, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008147276, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008147448, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008147580, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008147796, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008148609, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\FancyScrollView\\Sources\\Runtime\\ScrollRect\\FancyScrollRectContext.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758538008148571, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008149382, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008149537, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008150477, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008150973, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008151232, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008151626, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008152454, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758538008152704, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758538008152926, "dur": 1499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008154442, "dur": 959, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008155482, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008155578, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008156043, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008156221, "dur": 18907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538008175129, "dur": 2132802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758538010307932, "dur": 382527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008135659, "dur": 9982, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008145645, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758538008145752, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008146122, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_68485FEE8B3A7A7E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758538008146296, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758538008146295, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AD339B360B767264.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758538008146470, "dur": 531, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AD339B360B767264.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758538008147243, "dur": 1298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008148543, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008149372, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008150068, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008150174, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008150225, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008150326, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008150430, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008150836, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008151252, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008151588, "dur": 1626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008153260, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008153682, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758538008153798, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758538008154134, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008154266, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008154566, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008154942, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008155094, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008155369, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008155458, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008155603, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008155902, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758538008155969, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538008156337, "dur": 2151617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758538010307954, "dur": 382481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008135954, "dur": 10374, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008146401, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008146623, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008146818, "dur": 900, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008147721, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008147832, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008147935, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008148396, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008149740, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008150926, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008151233, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008151648, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008152188, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758538008153072, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008153176, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758538008153575, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758538008153809, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008153976, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758538008154062, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008154237, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008154637, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008155096, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008155392, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008155484, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008155598, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008156000, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538008156271, "dur": 2151654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538010307927, "dur": 380720, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758538010307926, "dur": 380722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758538010690109, "dur": 60, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758538010688664, "dur": 1522, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758538008135968, "dur": 10471, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008146457, "dur": 749, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1758538008147211, "dur": 811, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008148024, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008148140, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008148437, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008149742, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008150324, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008150427, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008150747, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008150919, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008151239, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008151606, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008152196, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758538008153149, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008153293, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758538008154005, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008154594, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008154919, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758538008155266, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758538008155613, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008156040, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538008156192, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758538008156484, "dur": 2151484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758538010307968, "dur": 382269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008135700, "dur": 9953, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008145662, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1758538008145725, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1758538008145658, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758538008145884, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1758538008145883, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758538008146125, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_63B80DE4FB9B8B85.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758538008146288, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 21, "ts": 1758538008146287, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_4C96AFA9785B3E8A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758538008146432, "dur": 418, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_4C96AFA9785B3E8A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758538008146988, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008147277, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008147520, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008148166, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008148551, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008149235, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008150051, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008150148, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008150454, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\PlatformSpecificSetup.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758538008150246, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008151078, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008151186, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008151260, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008151584, "dur": 1341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008152925, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758538008152982, "dur": 1082, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008154274, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 21, "ts": 1758538008154102, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758538008154430, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008154535, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008154682, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008155103, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008155377, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008155491, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008155591, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008156032, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538008156252, "dur": 2151694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758538010307946, "dur": 382342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008135995, "dur": 10482, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008146493, "dur": 536, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1758538008147175, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008147355, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008147973, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008148926, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\Range.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758538008148375, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008149700, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Developer\\CheckinProgress.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758538008149571, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008150512, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008150972, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008151226, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008151648, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008151898, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758538008151955, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008152189, "dur": 843, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758538008153154, "dur": 770, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758538008153958, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float3x4.gen.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758538008152062, "dur": 2028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758538008154090, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008154505, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758538008155202, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758538008154719, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758538008155574, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008156033, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538008156246, "dur": 2151707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758538010307953, "dur": 382418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008136008, "dur": 10434, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008146467, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008146717, "dur": 881, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008147603, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008148040, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008148332, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008148454, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008148572, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008148779, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008149341, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008150023, "dur": 901, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_MaterialManager.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758538008149660, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008150989, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008151228, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008151627, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008152444, "dur": 1619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008154164, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1758538008154098, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758538008154995, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008155085, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758538008155584, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008156036, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538008156240, "dur": 2151729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758538010307970, "dur": 382422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008136022, "dur": 10468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008146694, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008147380, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008147599, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\LevelManagerWrap.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758538008147518, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008148382, "dur": 889, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\Components\\SplinePositionerEditor.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758538008148333, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008149400, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008150065, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008150156, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008150248, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008150413, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008150721, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008150860, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008151241, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008151607, "dur": 1482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008153115, "dur": 1059, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008154177, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008154558, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008154609, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008154713, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008155086, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\FancyScrollView.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1758538008155086, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FancyScrollView.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1758538008155278, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008155466, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008155596, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008155994, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538008156325, "dur": 2151631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758538010307956, "dur": 382501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008136039, "dur": 10449, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008146596, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008146688, "dur": 688, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008147457, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_RaycastHit2DWrap.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758538008147379, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008148098, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008148224, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008148799, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\SimpleJSON.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758538008148764, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008149631, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008150200, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008150307, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008150416, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008150739, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008150993, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008151226, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008151633, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008152265, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758538008152330, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758538008152381, "dur": 896, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008153279, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1758538008153514, "dur": 969, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008154503, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTween\\DOTween46.dll"}}, {"pid": 12345, "tid": 25, "ts": 1758538008154653, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1758538008154881, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 25, "ts": 1758538008154499, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1758538008155161, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008155463, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008155599, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008155988, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538008156331, "dur": 2151618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758538010307949, "dur": 382483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008136059, "dur": 10425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008146532, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008146989, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008147248, "dur": 1295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008148545, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008149739, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008151057, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008151211, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008151642, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008151870, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758538008152189, "dur": 1080, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758538008153283, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758538008152011, "dur": 1580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758538008153635, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758538008153703, "dur": 714, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008154843, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758538008154978, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758538008155085, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_BitmapShaderGUI.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758538008154420, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758538008155241, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008155291, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758538008155501, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008155571, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008156043, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008156219, "dur": 18153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008174373, "dur": 752, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538008175126, "dur": 2132813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758538010307939, "dur": 382509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008136070, "dur": 10401, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008146488, "dur": 453, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1758538008146985, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008147108, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1758538008147174, "dur": 1273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008148457, "dur": 815, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Components\\TubeGenerator.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758538008148450, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008149370, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008150475, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\AssetMenu\\ProjectViewAssetSelection.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758538008149605, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008151047, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008151222, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008151637, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008152190, "dur": 1079, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758538008152065, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758538008153453, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758538008153557, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758538008153654, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758538008153885, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008154609, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008154712, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008155098, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008155475, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008155585, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008156030, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538008156294, "dur": 2151666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758538010307961, "dur": 382446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008136089, "dur": 10381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008146483, "dur": 779, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1758538008147322, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008148235, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Common\\TinyLocalizationText.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758538008147760, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008149023, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008149126, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008149374, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008149521, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008150446, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008150859, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008151246, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008151599, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008152198, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758538008152302, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758538008152405, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758538008152697, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758538008153003, "dur": 1185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008154248, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008154316, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008154671, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008155095, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008155386, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008155486, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008155597, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008156019, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538008156265, "dur": 2151665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758538010307930, "dur": 382324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758538010693075, "dur": 1415, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17984, "tid": 135245, "ts": 1758538010702201, "dur": 1812, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17984, "tid": 135245, "ts": 1758538010704042, "dur": 518, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17984, "tid": 135245, "ts": 1758538010700236, "dur": 4814, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
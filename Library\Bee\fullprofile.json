{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17984, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17984, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17984, "tid": 933053, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17984, "tid": 933053, "ts": 1758546504400095, "dur": 485, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17984, "tid": 933053, "ts": 1758546504402946, "dur": 419, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17984, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17984, "tid": 1, "ts": 1758546496403881, "dur": 3879, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17984, "tid": 1, "ts": 1758546496407763, "dur": 30699, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17984, "tid": 1, "ts": 1758546496438469, "dur": 28745, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17984, "tid": 933053, "ts": 1758546504403369, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 17984, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496402760, "dur": 9421, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496412183, "dur": 7981842, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496412925, "dur": 1997, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496414926, "dur": 463, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496415392, "dur": 19254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496434651, "dur": 150, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496434802, "dur": 195, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496435000, "dur": 588, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496435590, "dur": 602, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436194, "dur": 5, "ph": "X", "name": "ProcessMessages 19517", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436200, "dur": 99, "ph": "X", "name": "ReadAsync 19517", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436300, "dur": 3, "ph": "X", "name": "ProcessMessages 11795", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436304, "dur": 21, "ph": "X", "name": "ReadAsync 11795", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436328, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436330, "dur": 26, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436357, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436358, "dur": 17, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436377, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436390, "dur": 11, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436403, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436422, "dur": 11, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436435, "dur": 10, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436447, "dur": 11, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436459, "dur": 9, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436470, "dur": 13, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436484, "dur": 11, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436496, "dur": 10, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436507, "dur": 13, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436523, "dur": 12, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436537, "dur": 11, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436549, "dur": 10, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436561, "dur": 9, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436572, "dur": 12, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436586, "dur": 14, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436601, "dur": 11, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436613, "dur": 12, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436627, "dur": 11, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436639, "dur": 12, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436653, "dur": 10, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436665, "dur": 10, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436676, "dur": 11, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436688, "dur": 11, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436700, "dur": 17, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436719, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436736, "dur": 11, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436749, "dur": 9, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436759, "dur": 13, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436773, "dur": 17, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436793, "dur": 14, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436809, "dur": 12, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436823, "dur": 31, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436856, "dur": 10, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436867, "dur": 11, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436880, "dur": 9, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436891, "dur": 11, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496436903, "dur": 96, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437001, "dur": 1, "ph": "X", "name": "ProcessMessages 2436", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437002, "dur": 11, "ph": "X", "name": "ReadAsync 2436", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437014, "dur": 10, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437026, "dur": 37, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437065, "dur": 16, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437083, "dur": 13, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437098, "dur": 10, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437110, "dur": 9, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437121, "dur": 11, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437134, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437151, "dur": 13, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437167, "dur": 11, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437179, "dur": 11, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437192, "dur": 11, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437205, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437229, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437249, "dur": 12, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437262, "dur": 11, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437275, "dur": 11, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437289, "dur": 10, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437300, "dur": 11, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437313, "dur": 11, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437325, "dur": 11, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437338, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437362, "dur": 12, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437376, "dur": 11, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437388, "dur": 11, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437401, "dur": 13, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437417, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437433, "dur": 9, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437443, "dur": 23, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437469, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437482, "dur": 13, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437497, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437513, "dur": 10, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437524, "dur": 14, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437539, "dur": 12, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437552, "dur": 10, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437564, "dur": 11, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437576, "dur": 11, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437589, "dur": 11, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437601, "dur": 9, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437611, "dur": 11, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437623, "dur": 11, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437636, "dur": 11, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437648, "dur": 13, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437663, "dur": 10, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437675, "dur": 13, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437689, "dur": 11, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437702, "dur": 11, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437714, "dur": 12, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437728, "dur": 12, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437742, "dur": 9, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437752, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437765, "dur": 10, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437776, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437791, "dur": 12, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437805, "dur": 21, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437828, "dur": 11, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437840, "dur": 12, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437855, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437871, "dur": 14, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437887, "dur": 14, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437902, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437918, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437936, "dur": 16, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437954, "dur": 9, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437965, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437977, "dur": 11, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496437989, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438007, "dur": 13, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438021, "dur": 11, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438033, "dur": 11, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438046, "dur": 11, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438059, "dur": 13, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438073, "dur": 10, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438085, "dur": 11, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438097, "dur": 13, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438111, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438130, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438142, "dur": 11, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438154, "dur": 10, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438165, "dur": 11, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438178, "dur": 12, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438191, "dur": 14, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438207, "dur": 11, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438219, "dur": 11, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438231, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438249, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438260, "dur": 17, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438278, "dur": 14, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438294, "dur": 11, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438306, "dur": 12, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438319, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438338, "dur": 36, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438376, "dur": 13, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438390, "dur": 13, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438405, "dur": 11, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438417, "dur": 10, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438429, "dur": 12, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438442, "dur": 10, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438453, "dur": 11, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438465, "dur": 84, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438551, "dur": 37, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438588, "dur": 1, "ph": "X", "name": "ProcessMessages 2391", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438590, "dur": 15, "ph": "X", "name": "ReadAsync 2391", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438607, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438624, "dur": 11, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438636, "dur": 10, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438648, "dur": 11, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438661, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438775, "dur": 1, "ph": "X", "name": "ProcessMessages 2201", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438777, "dur": 11, "ph": "X", "name": "ReadAsync 2201", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438789, "dur": 15, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438806, "dur": 11, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438818, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438833, "dur": 10, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438845, "dur": 16, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438863, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438876, "dur": 9, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438887, "dur": 16, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438905, "dur": 10, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438916, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438938, "dur": 11, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438951, "dur": 10, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438963, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438974, "dur": 10, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496438986, "dur": 13, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439000, "dur": 13, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439015, "dur": 10, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439027, "dur": 11, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439039, "dur": 10, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439050, "dur": 9, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439061, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439090, "dur": 11, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439102, "dur": 17, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439121, "dur": 11, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439134, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439151, "dur": 11, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439163, "dur": 48, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439212, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439213, "dur": 10, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439225, "dur": 11, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439238, "dur": 10, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439250, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439261, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439278, "dur": 10, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439290, "dur": 12, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439303, "dur": 13, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439317, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439333, "dur": 12, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439346, "dur": 10, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439358, "dur": 9, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439368, "dur": 12, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439381, "dur": 11, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439394, "dur": 11, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439406, "dur": 11, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439419, "dur": 11, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439431, "dur": 11, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439444, "dur": 10, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439456, "dur": 9, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439466, "dur": 10, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439477, "dur": 25, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439505, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439507, "dur": 20, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439529, "dur": 16, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439548, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439568, "dur": 14, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439585, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439601, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439614, "dur": 10, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439626, "dur": 10, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439638, "dur": 11, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439651, "dur": 13, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439665, "dur": 10, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439676, "dur": 14, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439692, "dur": 26, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439719, "dur": 9, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439729, "dur": 11, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439742, "dur": 11, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439755, "dur": 19, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439775, "dur": 11, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439788, "dur": 17, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439806, "dur": 17, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439825, "dur": 9, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439835, "dur": 13, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439849, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439871, "dur": 11, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439883, "dur": 88, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439972, "dur": 1, "ph": "X", "name": "ProcessMessages 2010", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439973, "dur": 10, "ph": "X", "name": "ReadAsync 2010", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439985, "dur": 10, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496439996, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440014, "dur": 9, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440025, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440045, "dur": 13, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440060, "dur": 10, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440071, "dur": 13, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440086, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440110, "dur": 11, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440123, "dur": 12, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440137, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440159, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440176, "dur": 17, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440194, "dur": 17, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440213, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440229, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440249, "dur": 12, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440262, "dur": 13, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440276, "dur": 10, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440288, "dur": 11, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440300, "dur": 12, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440314, "dur": 12, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440327, "dur": 10, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440338, "dur": 10, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440349, "dur": 11, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440361, "dur": 13, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440375, "dur": 10, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440387, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440411, "dur": 10, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440422, "dur": 21, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440444, "dur": 9, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440455, "dur": 10, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440467, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440485, "dur": 16, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440502, "dur": 11, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440515, "dur": 22, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440539, "dur": 11, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440551, "dur": 14, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440566, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440583, "dur": 11, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440596, "dur": 16, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440613, "dur": 10, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440625, "dur": 8, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440635, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440646, "dur": 11, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440658, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440659, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440676, "dur": 10, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440688, "dur": 26, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440716, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440732, "dur": 11, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440745, "dur": 13, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440760, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440779, "dur": 8, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440789, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440800, "dur": 14, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440816, "dur": 12, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440829, "dur": 10, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440841, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440868, "dur": 9, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440879, "dur": 10, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440891, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440909, "dur": 11, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440922, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440934, "dur": 10, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440946, "dur": 11, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440958, "dur": 10, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440970, "dur": 9, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496440980, "dur": 136, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441117, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441135, "dur": 10, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441147, "dur": 10, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441159, "dur": 10, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441171, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441183, "dur": 11, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441196, "dur": 11, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441208, "dur": 9, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441218, "dur": 10, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441230, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496441243, "dur": 778, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496442025, "dur": 366, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496442393, "dur": 175, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496442570, "dur": 201, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496442772, "dur": 4, "ph": "X", "name": "ProcessMessages 2032", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496442778, "dur": 98, "ph": "X", "name": "ReadAsync 2032", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496442878, "dur": 2, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496442880, "dur": 671, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496443556, "dur": 24, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496443581, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496443583, "dur": 1939, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496445527, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496445570, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496445572, "dur": 1010, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496446585, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496446628, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496446630, "dur": 285, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496446919, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496446970, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496446986, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496446987, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496447013, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496447033, "dur": 685, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496447723, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496447777, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496447779, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496447931, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496447997, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496448116, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496448136, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496448158, "dur": 978, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496449141, "dur": 1948, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451092, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451095, "dur": 64, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451162, "dur": 4, "ph": "X", "name": "ProcessMessages 1856", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451168, "dur": 14, "ph": "X", "name": "ReadAsync 1856", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451184, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451216, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451297, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451299, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451336, "dur": 199, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451541, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451569, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451571, "dur": 230, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451805, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496451846, "dur": 189, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452038, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452107, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452109, "dur": 119, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452230, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452302, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452334, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452492, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496452509, "dur": 722, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496453234, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496453250, "dur": 378, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546496453629, "dur": 7492607, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503946245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503946248, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503946280, "dur": 9906, "ph": "X", "name": "ProcessMessages 9959", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503956192, "dur": 77, "ph": "X", "name": "ReadAsync 9959", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503956272, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503956274, "dur": 392, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503956671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503956673, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503956712, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503956740, "dur": 27156, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503983903, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503983906, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546503983925, "dur": 402718, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504386648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504386651, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504386672, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504386674, "dur": 1587, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504388265, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504388275, "dur": 13, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504388289, "dur": 549, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504388840, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504388850, "dur": 242, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17984, "tid": 12884901888, "ts": 1758546504389093, "dur": 3953, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17984, "tid": 933053, "ts": 1758546504403376, "dur": 404, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17984, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17984, "tid": 8589934592, "ts": 1758546496401054, "dur": 66188, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17984, "tid": 8589934592, "ts": 1758546496467243, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17984, "tid": 8589934592, "ts": 1758546496467246, "dur": 874, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17984, "tid": 933053, "ts": 1758546504403782, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17984, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17984, "tid": 4294967296, "ts": 1758546496274482, "dur": 8120648, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758546496278077, "dur": 117579, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758546504395142, "dur": 3468, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758546504397286, "dur": 75, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17984, "tid": 4294967296, "ts": 1758546504398651, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17984, "tid": 933053, "ts": 1758546504403787, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758546496410785, "dur": 19571, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546496430364, "dur": 488, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546496430926, "dur": 598, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546496432132, "dur": 3437, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_A38B4233660E9CB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758546496436178, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1758546496436783, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1758546496431542, "dur": 10269, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546496441819, "dur": 7947103, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546504388923, "dur": 146, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546504389093, "dur": 95, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546504389412, "dur": 979, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758546496431452, "dur": 10376, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496441883, "dur": 411, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1758546496441841, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758546496442440, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496442621, "dur": 411, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_7BBCF5494DCBE09D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758546496443066, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496443631, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.OdinInspector.Attributes.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496443816, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\WX-WASM-SDK-V2\\Editor\\wx-editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496443972, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496444083, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496444265, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496444587, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496444732, "dur": 1824, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496446619, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496446949, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496447026, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496447126, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496443418, "dur": 3994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758546496447497, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496448333, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496448666, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496448978, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758546496449066, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758546496449366, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496449683, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496450201, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758546496450287, "dur": 888, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496451192, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496451613, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496451685, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758546496451743, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496451811, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496452112, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496452395, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496452540, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546496452892, "dur": 7500914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758546503953806, "dur": 435108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496431488, "dur": 10414, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496441916, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1758546496441905, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758546496442266, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496442394, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496442782, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496443543, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1758546496443647, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496443762, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496443963, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496444058, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496444313, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496444405, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496444586, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496444886, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445108, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445194, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445283, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445385, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445480, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445581, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445690, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445835, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496445939, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496446064, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496446531, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496446641, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496446782, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496447669, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496448366, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496448679, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496448757, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758546496448830, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758546496449095, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496449194, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758546496449529, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496450469, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758546496450313, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758546496450873, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496451202, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496451608, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496451688, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1758546496451746, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496451823, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Lofelt.NiceVibrations.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1758546496451883, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496452116, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496452393, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496452546, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546496452885, "dur": 7501005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758546503953891, "dur": 435017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496431463, "dur": 10374, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496441884, "dur": 755, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1758546496441841, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758546496442865, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496443733, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496444467, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496444782, "dur": 2047, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\SkeletonUtility\\SkeletonUtilityConstraint.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758546496444706, "dur": 2444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496447150, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496447513, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496448326, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496448680, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496448741, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758546496448799, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496449449, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758546496449150, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758546496449650, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496450327, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496450408, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758546496450462, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496451645, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758546496450538, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758546496452001, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758546496452071, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758546496452370, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496452426, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496452541, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546496452892, "dur": 7500925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758546503953817, "dur": 435094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496431479, "dur": 10364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496441883, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1758546496441849, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758546496442149, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496442498, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496442968, "dur": 2233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496445204, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496445801, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496445986, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496446579, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496446762, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496447334, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496447522, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496448324, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496448696, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496448757, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758546496448970, "dur": 408, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758546496449449, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758546496449539, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758546496448891, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758546496449921, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496449993, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758546496450051, "dur": 1375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496451438, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758546496452326, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496452515, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496452569, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758546496452771, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758546496453545, "dur": 73, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546496453822, "dur": 7494909, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758546503953912, "dur": 30316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758546503953687, "dur": 30637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758546503984325, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758546503984418, "dur": 404471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496431502, "dur": 10421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496441937, "dur": 512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1758546496441927, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758546496442450, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496442899, "dur": 2199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445100, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445187, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445299, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445401, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445508, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445609, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445727, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445830, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496445953, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496446063, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496446564, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496446751, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496447152, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496447513, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496448339, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496448652, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496449620, "dur": 943, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758546496449492, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758546496450797, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496450869, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758546496451358, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496451468, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496451613, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496451685, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496451811, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496451875, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496452089, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496452390, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496452599, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546496452828, "dur": 7500920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546503953749, "dur": 30652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758546503984439, "dur": 404457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496431523, "dur": 10422, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496441962, "dur": 485, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1758546496441948, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758546496442571, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758546496442571, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758546496442886, "dur": 2080, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496444967, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496445063, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496445237, "dur": 1299, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\UniTaskSynchronizationContext.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758546496445154, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496446547, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496446657, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496446774, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496448113, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496448346, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496448645, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496449569, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758546496450456, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496450569, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496450867, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496451003, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496451276, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496451365, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496451458, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496451577, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496451674, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496451816, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/AppleAuth.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758546496451880, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496452100, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496452383, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496452602, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496452800, "dur": 15886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496468687, "dur": 1105, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546496469792, "dur": 7483975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758546503953767, "dur": 435200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496431537, "dur": 10424, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496441975, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1758546496441963, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758546496442194, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758546496442570, "dur": 456, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758546496442569, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758546496443177, "dur": 930, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758546496444107, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\StorageManagerWrap.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758546496444107, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496444898, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496445080, "dur": 1927, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\TakeUntilCanceled.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758546496445014, "dur": 2061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496447076, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496447526, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496448332, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496448672, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496448970, "dur": 1003, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758546496448901, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758546496450129, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758546496449997, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758546496450428, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496450707, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496450932, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496451020, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496451296, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496451422, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496451582, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496451680, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496451815, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496452102, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496452386, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496452601, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496452822, "dur": 16972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546496469795, "dur": 7483960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758546503953756, "dur": 435211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496431550, "dur": 10416, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496441979, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1758546496441968, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7C2C4F34C6B423AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758546496442441, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496442782, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496443175, "dur": 484, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758546496443660, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758546496443981, "dur": 971, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\StaticLuaCallbacks.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758546496443961, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496445079, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496445640, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496445822, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496446085, "dur": 886, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ai.navigation@1.1.6\\Editor\\ConversionSystem\\SystemConvertersEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758546496446003, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496447179, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496447498, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496448343, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496448665, "dur": 2929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496451594, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496451700, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/IngameDebugConsole.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758546496451751, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496451817, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496452107, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496452373, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496452607, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496452787, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546496452868, "dur": 7500906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758546503953774, "dur": 435179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496431568, "dur": 10403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496441994, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1758546496441977, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BDC059FEE0C56713.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758546496442273, "dur": 623, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758546496442272, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_03C0A16AC7611605.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758546496443012, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496443241, "dur": 2050, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496445366, "dur": 702, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Image.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758546496446395, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\AsyncOperation.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758546496445293, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496447104, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496447518, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496448338, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496448658, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496449621, "dur": 1319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758546496449492, "dur": 1681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758546496451173, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496451810, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758546496452085, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496452398, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496452583, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546496452848, "dur": 7500835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758546503953686, "dur": 433473, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758546503953685, "dur": 433476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758546504387190, "dur": 1650, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758546496431584, "dur": 10395, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496441996, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1758546496441982, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_24A8B6F9BAE245F7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758546496442568, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496442886, "dur": 1718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496444614, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496445152, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496445288, "dur": 1751, "ph": "X", "name": "File", "args": {"detail": "Assets\\AppleSignIn\\AppleAuth\\Native\\PasswordCredential.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758546496445271, "dur": 2130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496447401, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496447508, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496448340, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496448646, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496449557, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758546496449610, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496450203, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758546496450453, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758546496450315, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758546496450657, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496450872, "dur": 902, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496451841, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496452128, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496452342, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496452400, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496452566, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546496452858, "dur": 7500942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758546503953800, "dur": 435116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496431600, "dur": 10383, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496441999, "dur": 471, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1758546496441985, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_3D07E2B87C74DF17.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758546496442497, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758546496442496, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EEA89CD5119B3433.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758546496442629, "dur": 693, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EEA89CD5119B3433.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758546496443379, "dur": 628, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1758546496444008, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496444217, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496444315, "dur": 88, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496444403, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496444778, "dur": 2041, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Custom\\UITrigger.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758546496444506, "dur": 2524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496447030, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496447533, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496448317, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496448680, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496448789, "dur": 705, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496449620, "dur": 1236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758546496450874, "dur": 798, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758546496449498, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1758546496452006, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758546496452079, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1758546496452357, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496452469, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758546496452533, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546496452864, "dur": 7500931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758546503953795, "dur": 435125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496431615, "dur": 10385, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496442016, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1758546496442002, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8D0CB068D769A31A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758546496442364, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496443010, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496443330, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496443641, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496443931, "dur": 1014, "ph": "X", "name": "File", "args": {"detail": "Assets\\Editor\\CustomTools\\UIToggleEditor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758546496444955, "dur": 2092, "ph": "X", "name": "File", "args": {"detail": "Assets\\Editor\\CustomTools\\TableViewEditor\\TableViewEditor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758546496443698, "dur": 3483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496447181, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496447486, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496447537, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496448332, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496448665, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496448977, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758546496449057, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758546496449135, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758546496449500, "dur": 1741, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496451245, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758546496451800, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/AppleAuth.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1758546496451905, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496452084, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496452399, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496452579, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546496452850, "dur": 7500956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758546503953806, "dur": 435109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496431628, "dur": 10376, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496442018, "dur": 388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1758546496442007, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1542A8759536CB9C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758546496442433, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496442956, "dur": 2014, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496444972, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445069, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445170, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445261, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445382, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445484, "dur": 94, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445578, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445689, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445806, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496445919, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496446388, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_FontAssetUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758546496446022, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496447176, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496447504, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496448341, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496448660, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496448982, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758546496449048, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496449278, "dur": 1921, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1758546496449145, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758546496451377, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758546496451684, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/IngameDebugConsole.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1758546496451741, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496451836, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496452105, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496452379, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496452605, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496452793, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546496453057, "dur": 7500731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758546503953788, "dur": 435133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496431643, "dur": 10380, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496442037, "dur": 468, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1758546496442026, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_7B26F11653A8BB46.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758546496442562, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496442621, "dur": 406, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_ED95F4E26287FEC9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758546496443109, "dur": 2135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496445246, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496445470, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496445577, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496445682, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496445789, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496445910, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496446060, "dur": 978, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_SubMesh.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758546496446012, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496447088, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496447522, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496448336, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496448665, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496448978, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758546496449056, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758546496449140, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1758546496449470, "dur": 1226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496450725, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496450934, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496451631, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496451701, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496451843, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496452101, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496452392, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496452594, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546496452842, "dur": 7500968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758546503953811, "dur": 435104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496431658, "dur": 10399, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496442071, "dur": 447, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1758546496442060, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_4FFE5013DEBA169E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758546496442575, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496442855, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496443542, "dur": 557, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1758546496444099, "dur": 2322, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_ColliderWrap.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758546496444099, "dur": 2401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496446506, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2.gen.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758546496446500, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496447168, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496447512, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496448339, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496448652, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496449556, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758546496449610, "dur": 1129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496450763, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496450897, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496451204, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496451541, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496451691, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496451850, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496452098, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496452392, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496452596, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546496452834, "dur": 7501051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758546503953885, "dur": 435023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496431671, "dur": 10404, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496442094, "dur": 799, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1758546496442083, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F085666F1110AAE3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758546496443149, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496443342, "dur": 885, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496444229, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496444327, "dur": 88, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496444451, "dur": 2018, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Proto\\Tower.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758546496444415, "dur": 2486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496446901, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496447036, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496447529, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496448323, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496448679, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496448742, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758546496448970, "dur": 741, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758546496448814, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1758546496449873, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496449954, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496450083, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758546496450939, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1758546496451183, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496451743, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496451824, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Lofelt.NiceVibrations.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758546496451884, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496452074, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496452400, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496452573, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546496452855, "dur": 7500995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758546503953851, "dur": 435063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496431693, "dur": 10437, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496442133, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758546496442133, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_7F3AF681D6CECB6F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758546496442574, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1758546496442568, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758546496442812, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496443396, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1758546496443827, "dur": 1096, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Hierarchy 2\\Editor\\SceneRenamePopup.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758546496444934, "dur": 1578, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Hierarchy 2\\Editor\\ObjectCustomization.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758546496443734, "dur": 2889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496446640, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496446696, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496446804, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496446922, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496447302, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496447474, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496447533, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496448347, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496448690, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496449699, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758546496450868, "dur": 371, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758546496450834, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758546496451461, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496451600, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496451675, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496451866, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496452082, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496452399, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496452576, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546496452851, "dur": 7501012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758546503953863, "dur": 435046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496431705, "dur": 10429, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496442153, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496442488, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496442801, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496443307, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496443360, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1758546496443555, "dur": 548, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1758546496444103, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496444659, "dur": 2361, "ph": "X", "name": "File", "args": {"detail": "Assets\\FlexReader\\Converter\\CustomConverters\\ArrayConverter.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758546496444620, "dur": 2671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496447291, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496447480, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496447533, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496448318, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496448679, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496448802, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758546496448854, "dur": 856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496450188, "dur": 707, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\PendingChanges\\Dialogs\\LaunchDependenciesDialog.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758546496449711, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758546496450983, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758546496451207, "dur": 425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758546496451646, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758546496451057, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758546496451912, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496452008, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758546496452062, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496452124, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758546496452372, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496452609, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546496452860, "dur": 7500962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758546503953823, "dur": 435117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496431723, "dur": 10419, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496442147, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758546496442144, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758546496442202, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496442271, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758546496442270, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7B54AD0B13F0D210.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758546496442420, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496442613, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_C2B00A29F8F2F428.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758546496442931, "dur": 1254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496444188, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496444268, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496444353, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496444444, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496444777, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496445391, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\Scopes\\GUIGroupScope.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758546496445301, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496445989, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496446434, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496446538, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496446663, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496446793, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496447966, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496448352, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496448694, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496448777, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758546496448970, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758546496449449, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758546496449689, "dur": 518, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\CompilerServices\\AsyncUniTaskVoidMethodBuilder.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758546496448867, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758546496450468, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758546496450301, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758546496450688, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496451230, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496451602, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496451700, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496451830, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496452137, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496452387, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496452552, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546496452878, "dur": 7501023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758546503953901, "dur": 435021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496431737, "dur": 10493, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496442434, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496442626, "dur": 724, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_ABC31C6E784BF4F9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758546496443353, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758546496443585, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496444017, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496444081, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496444250, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496444582, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496444670, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496444747, "dur": 2074, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496446826, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496446891, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496447008, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496447117, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496443421, "dur": 4018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758546496447660, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496447850, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496448002, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496447570, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758546496448414, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758546496448978, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758546496449709, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758546496449957, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496450092, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758546496450187, "dur": 712, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.OdinInspector.Attributes.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758546496450175, "dur": 943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758546496451179, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496451619, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496451706, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496451824, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496452143, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496452381, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496452560, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496452771, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758546496452969, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546496453050, "dur": 7500749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758546503953800, "dur": 435116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496431752, "dur": 10545, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496442384, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496442638, "dur": 691, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_56F1B5FAA41F1F22.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758546496443363, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1758546496443551, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1758546496443688, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496444288, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496444766, "dur": 1864, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Common\\Encrypt\\AesRijndael.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758546496444540, "dur": 2140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496446680, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496446805, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496446914, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496447029, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496447530, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496448317, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496448687, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496448745, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758546496448815, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758546496449088, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496449514, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758546496449725, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758546496450082, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496450166, "dur": 787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496450955, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496451213, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496451607, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496451697, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496451835, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496452133, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496452388, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496452547, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546496452885, "dur": 7501011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758546503953896, "dur": 434994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496431766, "dur": 10818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496442699, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496442949, "dur": 1333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496444284, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496444374, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496444601, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496444928, "dur": 94, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496445093, "dur": 1715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\Publish.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758546496445022, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496446854, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496446963, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496447205, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496447515, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496448325, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496448687, "dur": 2499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496451186, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496451614, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496451710, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496451818, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496452150, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496452381, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496452559, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546496452871, "dur": 7501041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758546503953912, "dur": 435014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496431780, "dur": 10854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496442898, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496443171, "dur": 867, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1758546496444065, "dur": 880, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_RectOffsetWrap.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758546496444039, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445010, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445161, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445294, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445389, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445549, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445678, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445786, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496445900, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496446499, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496446616, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496446685, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496446794, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496447801, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496448355, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496448688, "dur": 1163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496449855, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758546496449980, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758546496450116, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758546496450305, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758546496450804, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496450942, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496451016, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496451404, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496451485, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496451611, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496451684, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496451812, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496452093, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496452387, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496452492, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758546496452609, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546496452836, "dur": 7500867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758546503953705, "dur": 963, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1758546503953705, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1758546503954729, "dur": 2495, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1758546503957228, "dur": 431660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496431793, "dur": 11198, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496443017, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496443286, "dur": 3697, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496447040, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496447529, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496448325, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496448673, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496448864, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758546496448940, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758546496449449, "dur": 1056, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758546496449068, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1758546496450555, "dur": 695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496451252, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1758546496451587, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496451682, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496451862, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496452088, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496452397, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496452586, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546496452846, "dur": 7501022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758546503953869, "dur": 435041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496431820, "dur": 11368, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496443204, "dur": 2071, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496445278, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496446402, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\ObjectMenuCreation\\ItemCreationUtility.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758546496445652, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496447074, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496447526, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496448330, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496448673, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496448815, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758546496448888, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758546496448958, "dur": 750, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496449710, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1758546496449988, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496450175, "dur": 1299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496451481, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496451556, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496451688, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496451856, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496452092, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496452395, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496452589, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546496452844, "dur": 7501030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758546503953874, "dur": 435017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496431831, "dur": 11550, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496443406, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1758546496443649, "dur": 1198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496444935, "dur": 2109, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\Attachments\\AtlasAttachmentLoader.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758546496444849, "dur": 2463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496447312, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496447473, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496447579, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496448371, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496448686, "dur": 2194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496450883, "dur": 627, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758546496450883, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758546496451536, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496451695, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496451851, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496452095, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496452394, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496452592, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546496452841, "dur": 7501038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758546503953879, "dur": 435062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496431844, "dur": 11694, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496443644, "dur": 1200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496444847, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496445519, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496445641, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496445762, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496445869, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496445975, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496446531, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496446653, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496446796, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496447360, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496447525, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496448337, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496448659, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496449138, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758546496449475, "dur": 1032, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\FacebookSDK\\Plugins\\Editor\\Facebook.Unity.Editor.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758546496449136, "dur": 1585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758546496450873, "dur": 760, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758546496450761, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758546496451987, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496452112, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496452348, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496452401, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496452570, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546496452855, "dur": 7501002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758546503953857, "dur": 435054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496431859, "dur": 11735, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496443598, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496443651, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496443704, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496444004, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496444474, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496444946, "dur": 1888, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Modules\\AttachmentTools\\AttachmentTools.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758546496444758, "dur": 2127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496446885, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496447030, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496447504, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496448331, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496448673, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496448968, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758546496449060, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758546496449330, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496449494, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758546496449681, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758546496450056, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758546496450187, "dur": 776, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1758546496450130, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758546496451144, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496451244, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758546496451595, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496451703, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496451830, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496452141, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496452382, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496452553, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546496452875, "dur": 7501031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758546503953907, "dur": 434979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758546504391858, "dur": 1419, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17984, "tid": 933053, "ts": 1758546504404094, "dur": 2645, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17984, "tid": 933053, "ts": 1758546504406765, "dur": 16999, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17984, "tid": 933053, "ts": 1758546504402014, "dur": 22349, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
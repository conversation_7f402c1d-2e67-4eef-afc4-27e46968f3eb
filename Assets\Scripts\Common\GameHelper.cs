using UnityEngine;
using System.IO;
using System.Linq;
using XLua;
using System;
using System.Collections.Generic;
using UnityEngine.EventSystems;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using UnityEngine.UI;
using Spine.Unity;
using System.Text;
using System.Text.RegularExpressions;
using System.Security.Cryptography;

public class GameHelper
{
        static List<float> s_ActionAllParam = null;

        /// <summary>
        /// 是否是webgl
        /// </summary>
        /// <returns></returns>
        public static bool IsWebGL()
        {
#if UNITY_WEBGL && !UNITY_EDITOR
                return true;
#else
                return false;
#endif
        }

        /// <summary>
        /// 安全区域的顶端 高度 目前只在微信平台上使用(包含胶囊菜单)
        /// </summary>
        public static float SafeAreaTop = 0;
        
        public static string persistentDataPath
        {
                get
                {
                        if (IsWebGL())
                                return WeChatWASM.WX.env.USER_DATA_PATH;
                        else
                                return Application.persistentDataPath;
                }
        }
        
        public static void Init()
        {
                s_ActionAllParam = new List<float>(6);
                for (int i = 0; i < 6; i++)
                {
                        s_ActionAllParam.Add(0.0f);
                }
                AddToManagerTrans(null);
        }

        public static void SetMaterialCache(string saveKey, Material mat)
        {
                if (!m_DictMaterials.ContainsKey(saveKey))
                {
                        m_DictMaterials.Add(saveKey, mat);
                }
        }

        static Dictionary<string, Material> m_DictMaterials = new Dictionary<string, Material>();
        public static Material GetMaterial(string shaderName, string txtName)
        {
                string saveKey = shaderName + txtName;
                if (m_DictMaterials.TryGetValue(saveKey, out Material matOut))
                {
                        return matOut;
                }

                Shader shader = Shader.Find(shaderName);
                if (shader == null)
                {
                        return null;
                }
                Material newMaterial = new Material(shader);
                m_DictMaterials.Add(saveKey, newMaterial);
                return newMaterial;
        }

        public static void Instantiate(GameObject prefab, out GameObject go, out Transform trans)
        {
                if (prefab == null)
                        go = new GameObject();
                else
                        go = GameObject.Instantiate(prefab);
                if (go != null)
                        trans = go.transform;
                else
                        trans = null;
        }

        public static void InstantiateAllWithPos(GameObject prefab, Transform pa, List<List<float>> tList)
        {
                if (prefab == null)
                        return;
                int len = tList.Count;
                for (int i = 0; i < len; i++)
                {
                        List<float> pos = tList[i];

                        GameObject obj = GameObject.Instantiate(prefab, pa, false);
                        obj.transform.localPosition = new Vector3(pos[0], pos[1], pos[2]);
                }
        }

        public static Component SearchChild(Transform trans, string name, Type t)
        {
                if (trans == null)
                        return null;
                Transform c = trans.Find(name);
                if (c == null) return null;
                if (t == null)
                {
                        return c;
                }
                return c.GetComponent(t);
        }

        public static Transform SearchParent(Transform trans, int level)
        {
                Transform pa = trans.parent;
                for (int i = 1; i < level; i++)
                {
                        if (pa != null)
                                pa = pa.parent;
                }
                return pa;
        }

        public static Transform SearchParentGo(GameObject go, int level)
        {
                return SearchParent(go.transform, level);
        }

        public static void SetSprite(SpriteRenderer renderer, Sprite sp)
        {
                if (renderer == null)
                        return;
                renderer.sprite = sp;
        }

        public static void AddColliderBox(GameObject go, float x, float y)
        {
                if (go == null)
                        return;

                BoxCollider2D box = go.AddComponent<BoxCollider2D>();
                if (box != null)
                        box.size = new Vector2(x, y);
        }

        public static void SetColliderBoxEnable(GameObject go, int isOpen, float ox = 0, float oy = 0, float x = 1, float y = 1)
        {
                BoxCollider2D box = go.GetComponent<BoxCollider2D>();
                if(isOpen == 0)
                {
                        if(box)
                        {
                               box.enabled = false;   
                        }
                        return;
                }
                if (box == null)
                {
                        box = go.AddComponent<BoxCollider2D>();
                }
                if (isOpen == 2)
                {
                        box.offset = new Vector2(ox, oy);
                        box.size = new Vector2(x, y);
                }
                if (box != null)
                {
                        box.enabled = (isOpen != 0);
                }
        }

        public static void SetColliderPolyEnable(GameObject go, int isOpen, List<Vector2> tVtList = null)
        {//isOpen 2 == auto 3 == shape 4 == set
                PolygonCollider2D poly = go.GetComponent<PolygonCollider2D>();
                 if(isOpen == 0)
                {
                        if(poly)
                        {
                               poly.enabled = false;   
                        }
                        return;
                }
                if (poly == null)
                {
                        poly = go.AddComponent<PolygonCollider2D>();
                }
                if (isOpen == 3)
                {
                        tVtList = new List<Vector2>();
                        go.GetComponent<SpriteRenderer>().sprite.GetPhysicsShape(0, tVtList);
                }
                if (tVtList != null)
                {
                        poly.SetPath(0, tVtList);
                }
                if (poly != null)
                {
                        poly.enabled = (isOpen != 0);
                }
        }

        public static void SetLayer(GameObject go, int layerId, int allChild)
        {
                go.layer = layerId;
                if (allChild == 1)
                {

                        ChangeAllChild(go, (GameObject item) => { item.layer = layerId; });

                }
        }

        static void ChangeAllChild(GameObject go, Action<GameObject> call)
        {
                call(go);
                Transform trans = go.transform;
                for (int i = 0; i < trans.childCount; i++)
                {
                        Transform child = trans.GetChild(i);
                        ChangeAllChild(child.gameObject, call);
                }
        }

        public static void SetOrder(Renderer render, int layerId, int orderInLayer)
        {
                if (render == null) return;
                render.sortingLayerID = layerId;
                render.sortingOrder = orderInLayer;

                if (orderInLayer > 32767 || orderInLayer < -32768)
                {
                        LogMan.Error("###### OrderInLayer Limited !! " + render.name + " Num:" + orderInLayer);
                }
        }

        public static SortingLayerMono SetOrderScript(GameObject go, int type, int flag, int layerId, int order)
        {
                SortingLayerMono mono = go.GetComponent<SortingLayerMono>();
                if (mono == null)
                {
                        mono = go.AddComponent<SortingLayerMono>();
                }
                mono.Setup(type, flag, layerId, order);
                return mono;
        }

        public static void SetSpineMaterial(GameObject gameObject, string msg)
        {
                SkeletonRenderer skrender = gameObject.GetComponent<SkeletonRenderer>();
                if (skrender == null) return;
                Renderer render = gameObject.GetComponent<Renderer>();
                if (render == null) return;
                UnityEngine.Texture txt = render.sharedMaterial.GetTexture("_MainTex");
                if (txt == null) return;
                Material mat = GetMaterial(msg, txt.name);
                if (mat == null) return;
                mat.SetTexture("_MainTex", txt);
                skrender.CustomMaterialOverride.Clear();
                skrender.CustomMaterialOverride.Add(render.sharedMaterial, mat);
        }

        public static MaterialPropertyBlock s_SharedMatBlock = null;
        public static void SetSpineColor(GameObject go, float r, float g, float b, float a, float phase)
        {
                SkeletonRenderer skrender = go.GetComponent<SkeletonRenderer>();
                if (skrender == null) return;
                Renderer render = skrender.GetComponent<Renderer>();
                if (render == null) return;

                MaterialPropertyBlock block = new MaterialPropertyBlock();
                render.GetPropertyBlock(block);

                int idColor = Shader.PropertyToID("_FillColor");
                block.SetColor(idColor, new Color(r, g, b, a));


                int idPhase = Shader.PropertyToID("_FillPhase");
                block.SetFloat(idPhase, phase);

                render.SetPropertyBlock(block);
        }

        public static void SetSpineGray(GameObject go, int isGray, float phase)
        {
                SkeletonRenderer skrender = go.GetComponent<SkeletonRenderer>();
                if (skrender == null) return;
                Renderer render = skrender.GetComponent<Renderer>();
                if (render == null) return;

                MaterialPropertyBlock block = new MaterialPropertyBlock();
                render.GetPropertyBlock(block);

                int idGray = Shader.PropertyToID("_GrayMode");
                block.SetInt(idGray, isGray);

                int idPhase = Shader.PropertyToID("_FillPhase");
                block.SetFloat(idPhase, phase);

                render.SetPropertyBlock(block);
        }
        // public static void SetShaderProperty(GameObject go, string property, float value)
        // {
        //         SkeletonRenderer skrender = go.GetComponent<SkeletonRenderer>();
        //         if (skrender == null) return;
        //         Renderer render = skrender.GetComponent<Renderer>();
        //         if (render == null) return;
        //         MaterialPropertyBlock block = new MaterialPropertyBlock();
        //         render.GetPropertyBlock(block);
        //         int idGray = Shader.PropertyToID(property);
        //         block.SetFloat(idGray, value);
        //         render.SetPropertyBlock(block);
        // }
        public static void SetShaderProperty(GameObject go, string property, int value)
        {
                SkeletonRenderer skrender = go.GetComponent<SkeletonRenderer>();
                if (skrender == null) return;
                Renderer render = skrender.GetComponent<Renderer>();
                if (render == null) return;

                MaterialPropertyBlock block = new MaterialPropertyBlock();
                render.GetPropertyBlock(block);

                int idGray = Shader.PropertyToID(property);
                block.SetInt(idGray, value);
                render.SetPropertyBlock(block);
        }
        public static void SetShaderSpriteProperty(GameObject go, string property, int value)
        {
                Renderer render = go.GetComponent<Renderer>();
                if (render == null) return;

                MaterialPropertyBlock block = new MaterialPropertyBlock();
                render.GetPropertyBlock(block);

                int idGray = Shader.PropertyToID(property);
                block.SetInt(idGray, value);
                render.SetPropertyBlock(block);
        }
        public static void SetShaderSpriteAllProperty(Transform tParent, string property, int value)
        {
                // int len = tParent.childCount;

                // for (int i = 0; i < len; i++)
                // {
                SpriteRenderer[] render = tParent.GetComponentsInChildren<SpriteRenderer>();
                for (int j = 0; j < render.Length; j++)
                {
                        if (render[j])
                        {
                                MaterialPropertyBlock block = new MaterialPropertyBlock();
                                render[j].GetPropertyBlock(block);
                                int idGray = Shader.PropertyToID(property);
                                block.SetInt(idGray, value);
                                render[j].SetPropertyBlock(block);
                        }
                }
                //            }
        }

        public static void SetColorSpriteRenderer(SpriteRenderer renderer, float r, float g, float b, float a)
        {
                if (renderer == null) return;
                renderer.color = new Color(r, g, b, a);
        }

        public static void SetColorSpriteRendererAll(Transform tParent, float r, float g, float b, float a)
        {
                Color c = new Color(r, g, b, a);
                int len = tParent.childCount;
                for (int i = 0; i < len; i++)
                {
                        SpriteRenderer render = tParent.GetChild(i).GetComponent<SpriteRenderer>();
                        if (render)
                        {
                                render.color = c;
                        }
                }
        }

        public static void SetSpriteRendererSMaterial(SpriteRenderer render, string name)
        {
                if (render)
                {
                        //TODO HXQ   
                        // if (render.sharedMaterial != null)
                        // {
                        //     if (name == render.sharedMaterial.shader.name) return;
                        // }
                        Material mat = GetMaterial(name, "shared");
                        if (mat != null)
                        {
                                //                 bool isSet = false;
                                //                 if (render.sharedMaterial != null)
                                //                 {
                                //                     UnityEngine.Texture txt = render.sharedMaterial.GetTexture("_MainTex");
                                //                     if (txt != null)
                                //                     {
                                //                         mat.SetTexture("_MainTex", txt);
                                //                         isSet = true;
                                //                     }
                                //                 }
                                //                 if(!isSet)
                                //                 {
                                //                     mat.SetTexture("_MainTex", render.sprite.texture);
                                //                 }

                                render.sharedMaterial = mat;
                        }
                }
        }

        public static void SetSpriteRendererSMaterialAll(Transform tParent, string name)
        {
                int len = tParent.childCount;
                for (int i = 0; i < len; i++)
                {
                        SpriteRenderer render = tParent.GetChild(i).GetComponent<SpriteRenderer>();

                        SetSpriteRendererSMaterial(render, name);
                }
        }

        public static void AddRawImageMaskBlur(GameObject go, Action<int> tFun)
        {
                if (go == null)
                {
                        tFun?.Invoke(-1);
                        return;
                }
                RawImage img = go.GetComponent<RawImage>();

                if (img == null)
                {
                        tFun?.Invoke(-2);
                        return;
                }

                UICapture capture = StartGame.Instance.UIRoot.GetCapture();
                if (capture == null)
                {
                        tFun?.Invoke(-3);
                        return;
                }

                capture.GetBlurImage((tex) =>
                {
                        if (tex != null)
                        {
                                img.texture = tex;
                                tFun?.Invoke(0);
                        }
                        else
                        {
                                tFun?.Invoke(-4);
                        }
                });
        }

        #region Dotween


        public static Tweener DOLocalMove(Transform trans, float sx, float sy, float sz, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 startValue = new Vector3(sx, sy, sz);
                Vector3 endValue = new Vector3(dx, dy, dz);

                trans.localPosition = startValue;

                return trans.DOLocalMove(endValue, duration);
        }

        public static Tweener DOLocalMoveBase(Transform trans, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 endValue = new Vector3(dx, dy, dz);

                return trans.DOLocalMove(endValue, duration);
        }

        public static Tweener DOMove(Transform trans, float sx, float sy, float sz, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 startValue = new Vector3(sx, sy, sz);
                Vector3 endValue = new Vector3(dx, dy, dz);

                trans.position = startValue;

                return trans.DOMove(endValue, duration);
        }

        public static Tweener DOMoveBase(Transform trans, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 endValue = new Vector3(dx, dy, dz);

                return trans.DOMove(endValue, duration);
        }

        public static Tweener DOLocalRotate(Transform trans, float sx, float sy, float sz, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 startValue = new Vector3(sx, sy, sz);
                Vector3 endValue = new Vector3(dx, dy, dz);

                trans.eulerAngles = startValue;

                return trans.DOLocalRotate(endValue, duration);
        }

        public static Tweener DOLocalRotateBase(Transform trans, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 endValue = new Vector3(dx, dy, dz);

                return trans.DOLocalRotate(endValue, duration);
        }

        public static Tweener DORotate(Transform trans, float sx, float sy, float sz, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 startValue = new Vector3(sx, sy, sz);
                Vector3 endValue = new Vector3(dx, dy, dz);

                trans.eulerAngles = startValue;

                return trans.DORotate(endValue, duration);
        }

        public static Tweener DORotateBase(Transform trans, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 endValue = new Vector3(dx, dy, dz);

                return trans.DORotate(endValue, duration);
        }

        public static Tweener DOScale(Transform trans, float sx, float sy, float sz, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 startValue = new Vector3(sx, sy, sz);
                Vector3 endValue = new Vector3(dx, dy, dz);

                trans.localScale = startValue;
                return trans.DOScale(endValue, duration);
        }

        public static Tweener DOScaleBase(Transform trans, float dx, float dy, float dz, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                if (isKillBefore != 0)
                        trans.DOKill();

                Vector3 endValue = new Vector3(dx, dy, dz);

                return trans.DOScale(endValue, duration);
        }

        public static Tweener DOColorSpriteRendererTrans(Transform trans, float sr, float sg, float sb, float sa, float dr, float dg, float db, float da, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                SpriteRenderer render = trans.GetComponent<SpriteRenderer>();
                if (render)
                {
                        if (isKillBefore != 0)
                                render.DOKill();
                        render.color = new Color(sr, sg, sb, sa);
                        return render.DOColor(new Color(dr, dg, db, da), duration);
                }
                return null;
        }

        public static Tweener DOFadeSpriteRendererTrans(Transform trans, float from, float to, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                SpriteRenderer render = trans.GetComponent<SpriteRenderer>();
                if (render)
                {
                        if (isKillBefore != 0)
                                render.DOKill();
                        Color c = new Color(render.color.r, render.color.g, render.color.b, from);
                        render.color = c;
                        return render.DOFade(to, duration);
                }
                return null;
        }

        public static Tweener DOColorSpriteRendererBaseTrans(Transform trans, float r, float g, float b, float a, float duration, int isKillBefore)
        {
                if (trans == null) return null;

                SpriteRenderer render = trans.GetComponent<SpriteRenderer>();
                if (render)
                {
                        if (isKillBefore != 0)
                                render.DOKill();
                        return render.DOColor(new Color(r, g, b, a), duration);
                }
                return null;
        }

        public static void DOTweenSetup(Tweener t, float delay, int loopTimes, int easeType, TweenCallback onFinished)
        {
                if (t != null)
                {
                        if (delay > 0.0f)
                                t.SetDelay(delay);

                        if (loopTimes <= 0)
                                t.SetLoops(-1, (LoopType)Math.Abs(loopTimes));
                        else
                                t.SetLoops(loopTimes);

                        if (easeType > 0)
                        {
                                if (easeType >= 1000)
                                {
                                        t.SetEase(Ease.INTERNAL_Custom);
                                        int idx = (easeType - 1000);
                                        AnimationCurve ani = GameAniManager.Instance.GetCurveByIndex(idx);
                                        if (ani != null)
                                        {
                                                t.SetEase(ani);
                                        }
                                }
                                else
                                {
                                        t.SetEase((Ease)easeType);
                                }
                        }

                        if (onFinished != null)
                                t.OnComplete(onFinished);
                }
        }

        /* All */
        public static void DOKillAll(List<Transform> tList)
        {
                int len = tList.Count;
                for (int i = 0; i < len; i++)
                {
                        tList[i].DOKill();
                }
                tList = null;
        }

        public static void DOKillAll(Transform tParent)
        {
                int len = tParent.childCount;
                for (int i = 0; i < len; i++)
                {
                        tParent.GetChild(i).DOKill();
                }
        }

        public static void DOKillSpriteRenderer(SpriteRenderer render)
        {
                if (render)
                {
                        render.DOKill();
                }
        }

        public static void DOKillAllSpriteRenderer(Transform tParent)
        {
                int len = tParent.childCount;
                for (int i = 0; i < len; i++)
                {
                        SpriteRenderer render = tParent.GetChild(i).GetComponent<SpriteRenderer>();
                        if (render)
                        {
                                render.DOKill();
                        }
                }
        }

        private static List<float> GetParamComByActType(int actType, Transform trans, List<float> param, ref List<float> tParam)
        {
                Vector3 now;
                switch (actType)
                {
                        case 0:
                                now = trans.localPosition;
                                break;
                        case 1:
                                now = trans.position;
                                break;
                        case 2:
                                now = trans.localEulerAngles;
                                break;
                        case 3:
                                now = trans.eulerAngles;
                                break;
                        case 4:
                                now = trans.localScale;
                                break;
                        default:
                                now = Vector3.one;
                                break;
                }
                tParam[0] = now.x;
                tParam[1] = now.y;
                tParam[2] = now.z;
                tParam[3] = param[0];
                tParam[4] = param[1];
                tParam[5] = param[2];
                return tParam;
        }

        public static void DOAction(int actType, float duration, float delay, int loopTimes, int easeType, int isKillBefore, TweenCallback onFinished, Transform trans, List<float> tParam)
        {
                Tweener t = null;
                switch (actType)
                {
                        case 0:
                                t = DOLocalMove(trans, tParam[0], tParam[1], tParam[2], tParam[3], tParam[4], tParam[5], duration, isKillBefore);
                                break;
                        case 1:
                                t = DOMove(trans, tParam[0], tParam[1], tParam[2], tParam[3], tParam[4], tParam[5], duration, isKillBefore);
                                break;
                        case 2:
                                t = DOLocalRotate(trans, tParam[0], tParam[1], tParam[2], tParam[3], tParam[4], tParam[5], duration, isKillBefore);
                                break;
                        case 3:
                                t = DORotate(trans, tParam[0], tParam[1], tParam[2], tParam[3], tParam[4], tParam[5], duration, isKillBefore);
                                break;
                        case 4:
                                t = DOScale(trans, tParam[0], tParam[1], tParam[2], tParam[3], tParam[4], tParam[5], duration, isKillBefore);
                                break;
                        case 5:
                                t = DOLocalMoveBase(trans, tParam[0], tParam[1], tParam[2], duration, isKillBefore);
                                break;
                        case 6:
                                t = DOMoveBase(trans, tParam[0], tParam[1], tParam[2], duration, isKillBefore);
                                break;
                        case 7:
                                t = DOLocalRotateBase(trans, tParam[0], tParam[1], tParam[2], duration, isKillBefore);
                                break;
                        case 8:
                                t = DORotateBase(trans, tParam[0], tParam[1], tParam[2], duration, isKillBefore);
                                break;
                        case 9:
                                t = DOScaleBase(trans, tParam[0], tParam[1], tParam[2], duration, isKillBefore);
                                break;
                        case 10:
                                t = DOColorSpriteRendererTrans(trans, tParam[0], tParam[1], tParam[2], tParam[3], tParam[4], tParam[5], tParam[6], tParam[7], duration, isKillBefore);
                                break;
                        case 11:
                                t = DOColorSpriteRendererBaseTrans(trans, tParam[0], tParam[1], tParam[2], tParam[3], duration, isKillBefore);
                                break;
                }
                DOTweenSetup(t, delay, loopTimes, easeType, onFinished);
        }

        public static void DOActionAll(int actId, int actType, int paramType, float duration, float delay, int loopTimes, int easeType, int isKillBefore, TweenCallback onFinished, List<Transform> tList, List<float> paramStart, List<List<float>> paramOne)
        {
                int len = tList.Count;
                for (int i = 0; i < len; i++)
                {
                        Transform trans = tList[i];
                        switch (paramType)
                        {
                                case 0:
                                        {
                                                List<float> tParam = GetParamComByActType(actType, trans, paramStart, ref s_ActionAllParam);
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, tParam);
                                        }
                                        break;
                                case 1:
                                        {
                                                List<float> tParam = GetParamComByActType(actType, trans, paramOne[i], ref s_ActionAllParam);
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, tParam);
                                        }
                                        break;
                                case 2:
                                        {
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, paramOne[i]);
                                        }
                                        break;
                                case 3:
                                        {
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, paramStart);
                                        }
                                        break;
                        }
                }
        }

        public static void DoActionAll(int actId, int actType, int paramType, float duration, float delay, int loopTimes, int easeType, int isKillBefore, TweenCallback onFinished, Transform parent, List<float> paramStart, List<List<float>> paramOne)
        {
                int len = parent.childCount;
                for (int i = 0; i < len; i++)
                {
                        Transform trans = parent.GetChild(i);
                        switch (paramType)
                        {
                                case 0:
                                        {
                                                List<float> tParam = GetParamComByActType(actType, trans, paramStart, ref s_ActionAllParam);
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, tParam);
                                        }
                                        break;
                                case 1:
                                        {
                                                List<float> tParam = GetParamComByActType(actType, trans, paramOne[i], ref s_ActionAllParam);
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, tParam);
                                        }
                                        break;
                                case 2:
                                        {
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, paramOne[i]);
                                        }
                                        break;
                                case 3:
                                        {
                                                DOAction(actType, duration, delay, loopTimes, easeType, isKillBefore, onFinished, trans, paramStart);
                                        }
                                        break;
                        }
                }
        }

        #endregion

        #region Transform & RectTransform ProperSettings

        public static RectTransform GetRectTransformWithOffsetAndAnchor(GameObject go, float x, float y, float z, float scaleX, float scaleY, float scaleZ, float offsetMinX, float offsetMinY, float offsetMaxX, float offsetMaxY, float anchorMinX, float anchorMinY, float anchorMaxX, float anchorMaxY)
        {
                if (go == null)
                        return null;
                RectTransform trans = go.GetComponent<RectTransform>();
                if (trans == null)
                {
                        trans = go.AddComponent<RectTransform>();
                }
                trans.anchoredPosition3D = new Vector3(x, y, z);
                trans.anchorMin = new Vector2(anchorMinX, anchorMinY);
                trans.anchorMax = new Vector2(anchorMaxX, anchorMaxY);
                trans.offsetMin = new Vector2(offsetMinX, offsetMinY);
                trans.offsetMax = new Vector2(offsetMaxX, offsetMaxY);
                trans.localScale = new Vector3(scaleX, scaleY, scaleZ);

                return trans;
        }

        public static RectTransform GetRectTransform(GameObject go)
        {
                if (go == null)
                        return null;
                return go.GetComponent<RectTransform>();
        }

        public static Rect GetGameObjectRect(GameObject go)
        {
                RectTransform rt = GetRectTransform(go);
                if (rt == null)
                        return Rect.zero;

                return rt.rect;
        }

        public static Vector2 GetGameObjectSize(GameObject go)
        {
                Rect rt = GetGameObjectRect(go);
                return rt.size;
        }

        //rect ui position
        public static void SetRTPosition(RectTransform rectTrans, float x, float y)
        {
                if (rectTrans == null)
                        return;
                rectTrans.anchoredPosition = new Vector2(x, y);
        }

        public static void SetRTPositionX(RectTransform rectTrans, float x)
        {
                if (rectTrans == null)
                        return;
                rectTrans.anchoredPosition = new Vector2(x, rectTrans.anchoredPosition.y);
        }

        public static void SetRTPositionY(RectTransform rectTrans, float y)
        {
                if (rectTrans == null)
                        return;
                rectTrans.anchoredPosition = new Vector2(rectTrans.anchoredPosition.x, y);
        }

        //position
        public static void SetPosition(Transform trans, float x, float y, float z)
        {
                if (trans == null)
                        return;
                trans.position = new Vector3(x, y, z);
        }

        public static void SetLocalPositionAll(List<Transform> tList, List<List<float>> tListPos, int specialId)
        {
                if (tList.Count != tListPos.Count) return;

                int len = tList.Count;
                for (int i = 0; i < len; i++)
                {
                        Transform trans = tList[i];
                       if (trans && trans.gameObject != null)
                        {
                                if (specialId != 0)
                                {
                                        trans.DOKill();
                                }

                                List<float> pos = tListPos[i];
                                trans.localPosition = new Vector3(pos[0], pos[1], pos[2]);
                        }

                }
        }

        public static void SetLocalPositionY(Transform trans, float y)
        {
                if (trans == null)
                        return;
                trans.localPosition = new Vector3(trans.localPosition.x, y, trans.localPosition.z);
        }

        public static void SetLocalPosition(Transform trans, float x, float y, float z)
        {
                if (trans == null)
                        return;
                trans.localPosition = new Vector3(x, y, z);
        }
        public static void SetGridLayoutGroup(RectTransform trans, int row)
        {
                if (trans == null)
                        return;
                GridLayoutGroup grid = trans.transform.GetComponent<GridLayoutGroup>();
                if (grid == null)
                        return;
                grid.constraintCount = row;
        }
        public static void ForceRebuildLayoutImmediate(RectTransform trans)
        {
                LayoutRebuilder.ForceRebuildLayoutImmediate(trans);
        }
        //rotation
        public static void SetRotation(Transform trans, float x, float y, float z)
        {
                if (trans == null)
                        return;
                trans.eulerAngles = new Vector3(x, y, z);
        }

        public static void SetRotationX(Transform trans, float x)
        {
                if (trans == null)
                        return;
                var angles = trans.eulerAngles;
                SetRotation(trans, x, angles.y, angles.z);
        }

        public static void SetRotationY(Transform trans, float y)
        {
                if (trans == null)
                        return;
                var angles = trans.eulerAngles;
                SetRotation(trans, angles.x, y, angles.z);
        }

        public static void SetRotationZ(Transform trans, float z)
        {
                if (trans == null)
                        return;
                var angles = trans.eulerAngles;
                SetRotation(trans, angles.x, angles.y, z);
        }

        //local rotation
        public static void SetLocalRotation(Transform trans, float x, float y, float z)
        {
                if (trans == null)
                        return;
                trans.localEulerAngles = new Vector3(x, y, z);
        }

        public static void SetLocalRotationX(Transform trans, float x)
        {
                if (trans == null)
                        return;
                var angles = trans.localEulerAngles;
                SetLocalRotation(trans, x, angles.y, angles.z);
        }

        public static void SetLocalRotationY(Transform trans, float y)
        {
                if (trans == null)
                        return;
                var angles = trans.localEulerAngles;
                SetLocalRotation(trans, angles.x, y, angles.z);
        }

        public static void SetLocalRotationZ(Transform trans, float z)
        {
                if (trans == null)
                        return;
                var angles = trans.localEulerAngles;
                SetLocalRotation(trans, angles.x, angles.y, z);
        }

        public static void SetLocalRotationGo(GameObject go, float x, float y, float z)
        {
                if (go == null) return;
                SetLocalRotation(go.transform, x, y, z);
        }

        public static void SetLocalRotationXGo(GameObject go, float x)
        {
                if (go == null) return;
                SetLocalRotationX(go.transform, x);
        }

        public static void SetLocalRotationYGo(GameObject go, float y)
        {
                if (go == null) return;
                SetLocalRotationY(go.transform, y);
        }

        public static void SetLocalRotationZGo(GameObject go, float z)
        {
                if (go == null) return;
                SetLocalRotationZ(go.transform, z);
        }
        public static Vector3 GetScreenPos(GameObject go, Camera uiCamera, RectTransform uiCanvasRect)
        {
                Vector3 objPos = go.gameObject.transform.position;

                Vector3 worldPos = uiCamera.WorldToScreenPoint(objPos);

                Vector2 uiPos;
                RectTransformUtility.ScreenPointToLocalPointInRectangle(uiCanvasRect, worldPos, uiCamera, out uiPos);

                return new Vector3(uiPos.x, uiPos.y, 0);
        }

        //scale
        public static void SetLocalScale(Transform trans, float x, float y, float z)
        {
                if (trans == null)
                        return;
                trans.localScale = new Vector3(x, y, z);
        }

        public static void SetLocalScaleX(Transform trans, float x)
        {
                if (trans == null)
                        return;
                var scale = trans.localScale;
                SetLocalScale(trans, x, scale.y, scale.z);
        }
        public static void SetLocalScaleY(Transform trans, float y)
        {
                if (trans == null)
                        return;
                var scale = trans.localScale;
                SetLocalScale(trans, scale.x, y, scale.z);
        }
        public static void SetLocalScaleZ(Transform trans, float z)
        {
                if (trans == null)
                        return;
                var scale = trans.localScale;
                SetLocalScale(trans, scale.x, scale.y, z);
        }

        public static void SetLocalScaleGo(GameObject go, float x, float y, float z)
        {
                if (go == null) return;
                SetLocalScale(go.transform, x, y, z);
        }
        public static void SetLocalScaleXGo(GameObject go, float x)
        {
                if (go == null) return;
                SetLocalScaleX(go.transform, x);
        }
        public static void SetLocalScaleYGo(GameObject go, float y)
        {
                if (go == null) return;
                SetLocalScaleY(go.transform, y);
        }
        public static void SetLocalScaleZGo(GameObject go, float z)
        {
                if (go == null) return;
                SetLocalScaleZ(go.transform, z);
        }
        #endregion

        #region EventSystems
        public static bool IsPointerOverGameObject()
        {
                // Debug.Log(Input.GetMouseButtonDown(0));
                // if (Input.GetMouseButtonDown(0) || (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began))
                {
#if UNITY_EDITOR
                        return EventSystem.current.IsPointerOverGameObject();
#elif UNITY_ANDROID || UNITY_IOS || UNITY_WEBGL
        return EventSystem.current.IsPointerOverGameObject(Input.GetTouch(0).fingerId);
#else
        return false;
#endif
                }
                // return false;
        }

        public static bool GetPointerOverGameObject(out GameObject pressObject)
        {
                pressObject = null;

                if (IsPointerOverGameObject())
                {
                        PointerEventData eventDataCurrentPosition = new PointerEventData(EventSystem.current);
                        eventDataCurrentPosition.position = new Vector2(
#if UNITY_EDITOR
                        Input.mousePosition.x, Input.mousePosition.y
#elif UNITY_ANDROID || UNITY_IOS || UNITY_WEBGL
           Input.touchCount > 0 ? Input.GetTouch(0).position.x : 0, Input.touchCount > 0 ? Input.GetTouch(0).position.y : 0
#endif
                        );

                        List<RaycastResult> results = new List<RaycastResult>();
                        EventSystem.current.RaycastAll(eventDataCurrentPosition, results);
                        if (results.Count > 0)
                        {
                                pressObject = results[0].gameObject;
                                return true;
                        }
                }
                return false;
        }
        #endregion

        #region SetActive
        public static void SetActive(GameObject go, bool isActive)
        {
                if (go == null)
                        return;
                go.SetActive(isActive);
        }

        public static void SetActiveAll(List<GameObject> tList, bool isActive)
        {
                int len = tList.Count;
                for (int i = 0; i < len; i++)
                {
                        tList[i].SetActive(isActive);
                }
        }

        public static void SetRecycleAll(List<Transform> tList)
        {
                int len = tList.Count;
                for (int i = 0; i < len; i++)
                {
                        Transform trans = tList[i];
                        trans.DOKill();
                        trans.SetParent(AssetManager.Instance.GetRecycleTrans());
                }
        }
        #endregion

        #region BoxCollider
        public static void UpdateBoxColliderSize(GameObject go)
        {
                if (go == null)
                        return;

                BoxCollider collider = go.gameObject.GetComponent<BoxCollider>();
                if (collider == null)
                        return;

                RectTransform rectTransform = go.gameObject.GetComponent<RectTransform>();

                collider.size = rectTransform.rect.size;

                float cx = (0.5f - rectTransform.pivot.x) * rectTransform.rect.size.x;
                float cy = (0.5f - rectTransform.pivot.y) * rectTransform.rect.size.y;

                collider.center = new Vector3(cx, cy, 0);
        }

        public static bool ColliderIntersects(GameObject go1, GameObject go2)
        {
                if (go1 == null || go2 == null)
                        return false;

                BoxCollider collider1 = go1.gameObject.GetComponent<BoxCollider>();
                BoxCollider collider2 = go2.gameObject.GetComponent<BoxCollider>();
                if (collider1 == null || collider2 == null)
                        return false;

                bool isInter = collider1.bounds.Intersects(collider2.bounds);
                return isInter;
        }

        public static bool ColliderContains(GameObject go, Vector3 point)
        {
                if (go == null)
                        return false;

                BoxCollider collider = go.gameObject.GetComponent<BoxCollider>();
                if (collider == null)
                        return false;

                return collider.bounds.Contains(point);
        }

        #endregion

        #region Other
        public static bool PointInbounds(GameObject go, Vector3 tPos)
        {
                if (go == null)
                        return false;

                Vector3 pos = go.gameObject.transform.localPosition;
                Vector2 size = GetGameObjectSize(go);

                RectTransform rectTrans = go.gameObject.GetComponent<RectTransform>();

                float minx = pos.x - rectTrans.pivot.x * size.x;
                float maxx = pos.x + (1 - rectTrans.pivot.x) * size.x;

                float miny = pos.y - rectTrans.pivot.y * size.y;
                float maxy = pos.y + (1 - rectTrans.pivot.y) * size.y;

                if (tPos.x >= minx && tPos.x <= maxx && tPos.y >= miny && tPos.y <= maxy)
                {
                        return true;
                }
                return false;
        }

        public static Vector3 RotateVectorWithDegree(float angle, Vector3 target, Vector3 axis)
        {
                return Quaternion.AngleAxis(angle, axis) * target;
        }

        public static UserDataMono GetMonoUserData(GameObject go)
        {
                UserDataMono data = go.GetComponent<UserDataMono>();
                if (data == null)
                {
                        data = go.AddComponent<UserDataMono>();
                }
                return data;
        }

        public static int GetMonoUserDataInt(GameObject go)
        {
                UserDataMono data = go.GetComponent<UserDataMono>();
                if (data == null)
                {
                        return 0;
                }
                return data.ParamInt;
        }

        public static double GetMonoUserDataDouble(GameObject go)
        {
                UserDataMono data = go.GetComponent<UserDataMono>();
                if (data == null)
                {
                        return 0;
                }
                return data.ParamDouble;
        }

        public static string GetMonoUserDataString(GameObject go)
        {
                UserDataMono data = go.GetComponent<UserDataMono>();
                if (data == null)
                {
                        return "";
                }
                return data.ParamString;
        }

        public static void SetMonoUserDataParam(GameObject go, int flag, object obj)
        {
                UserDataMono data = GetMonoUserData(go);

                if ((flag & 1) == 1)
                        data.ParamInt = Convert.ToInt32(obj);

                if ((flag & 2) == 2)
                        data.ParamString = (string)obj;

                if ((flag & 4) == 4)
                        data.ParamObject = obj;

                if ((flag & 8) == 8)
                        data.ParamDouble = Convert.ToDouble(obj);
        }

        #endregion

        #region Anchor
        public enum AnchorPresets
        {
                TopLeft,
                TopCenter,
                TopRight,

                MiddleLeft,
                MiddleCenter,
                MiddleRight,

                BottomLeft,
                BottonCenter,
                BottomRight,
                BottomStretch,

                VertStretchLeft,
                VertStretchRight,
                VertStretchCenter,

                HorStretchTop,
                HorStretchMiddle,
                HorStretchBottom,

                StretchAll
        }

        public static void SetAnchor(GameObject go, int allign, int offsetX = 0, int offsetY = 0)
        {
                RectTransform source = go.gameObject.GetComponent<RectTransform>();
                //source.anchoredPosition = Vector2.zero;
                //source.sizeDelta = Vector2.zero;

                //switch ((AnchorPresets)allign)
                //{
                //    case (AnchorPresets.TopLeft):
                //        {
                //            source.anchorMin = new Vector2(0, 1);
                //            source.anchorMax = new Vector2(0, 1);
                //            break;
                //        }
                //    case (AnchorPresets.TopCenter):
                //        {
                //            source.anchorMin = new Vector2(0.5f, 1);
                //            source.anchorMax = new Vector2(0.5f, 1);
                //            break;
                //        }
                //    case (AnchorPresets.TopRight):
                //        {
                //            source.anchorMin = new Vector2(1, 1);
                //            source.anchorMax = new Vector2(1, 1);
                //            break;
                //        }

                //    case (AnchorPresets.MiddleLeft):
                //        {
                //            source.anchorMin = new Vector2(0, 0.5f);
                //            source.anchorMax = new Vector2(0, 0.5f);
                //            break;
                //        }
                //    case (AnchorPresets.MiddleCenter):
                //        {
                //            source.anchorMin = new Vector2(0.5f, 0.5f);
                //            source.anchorMax = new Vector2(0.5f, 0.5f);
                //            break;
                //        }
                //    case (AnchorPresets.MiddleRight):
                //        {
                //            source.anchorMin = new Vector2(1, 0.5f);
                //            source.anchorMax = new Vector2(1, 0.5f);
                //            break;
                //        }

                //    case (AnchorPresets.BottomLeft):
                //        {
                //            source.anchorMin = new Vector2(0, 0);
                //            source.anchorMax = new Vector2(0, 0);
                //            break;
                //        }
                //    case (AnchorPresets.BottonCenter):
                //        {
                //            source.anchorMin = new Vector2(0.5f, 0);
                //            source.anchorMax = new Vector2(0.5f, 0);
                //            break;
                //        }
                //    case (AnchorPresets.BottomRight):
                //        {
                //            source.anchorMin = new Vector2(1, 0);
                //            source.anchorMax = new Vector2(1, 0);
                //            break;
                //        }

                //    case (AnchorPresets.HorStretchTop):
                //        {
                //            source.anchorMin = new Vector2(0, 1);
                //            source.anchorMax = new Vector2(1, 1);
                //            break;
                //        }
                //    case (AnchorPresets.HorStretchMiddle):
                //        {
                //            source.anchorMin = new Vector2(0, 0.5f);
                //            source.anchorMax = new Vector2(1, 0.5f);
                //            break;
                //        }
                //    case (AnchorPresets.HorStretchBottom):
                //        {
                //            source.anchorMin = new Vector2(0, 0);
                //            source.anchorMax = new Vector2(1, 0);
                //            break;
                //        }

                //    case (AnchorPresets.VertStretchLeft):
                //        {
                //            source.anchorMin = new Vector2(0, 0);
                //            source.anchorMax = new Vector2(0, 1);
                //            break;
                //        }
                //    case (AnchorPresets.VertStretchCenter):
                //        {
                //            source.anchorMin = new Vector2(0.5f, 0);
                //            source.anchorMax = new Vector2(0.5f, 1);
                //            break;
                //        }
                //    case (AnchorPresets.VertStretchRight):
                //        {
                //            source.anchorMin = new Vector2(1, 0);
                //            source.anchorMax = new Vector2(1, 1);
                //            break;
                //        }

                //    case (AnchorPresets.StretchAll):
                //        {
                //            source.anchorMin = new Vector2(0, 0);
                //            source.anchorMax = new Vector2(1, 1);
                //            break;
                //        }
                //    default:
                //        break;
                //}
        }
        #endregion

        #region UIEVENT_CALLBACK_SETTING

        public static void AddClick(Component[] coms, UnityEngine.Events.UnityAction<GameObject, object> call)
        {
                for (int i = 0; i < coms.Length; i++)
                {
                        Component component = coms[i];

                        if (component == null || call == null) return;
                        Type t = component.GetType();

                        if (t == typeof(InputField))
                        {
                                InputField tmp = coms[i] as InputField;
                                tmp.onEndEdit.RemoveAllListeners();
                                tmp.onEndEdit.AddListener(
                                   delegate (string value)
                                   {
                                           call(tmp.gameObject, value);
                                   }
                               );
                        }
                        else if (t == typeof(Button))
                        {
                                Button tmp = component as Button;
                                tmp.onClick.RemoveAllListeners();
                                tmp.onClick.AddListener(
                                    delegate ()
                                    {
                                            call(tmp.gameObject, null);
                                    }
                                );
                        }
                        else if (t == typeof(Toggle))
                        {
                                Toggle tmp = coms[i] as Toggle;
                                tmp.onValueChanged.RemoveAllListeners();
                                tmp.onValueChanged.AddListener(
                                      delegate (bool ison)
                                      {
                                              call(tmp.gameObject, ison);
                                      }
                                  );
                        }
                        else if (t == typeof(Slider))
                        {
                                Slider tmp = coms[i] as Slider;
                                tmp.onValueChanged.RemoveAllListeners();
                                tmp.onValueChanged.AddListener(
                                   delegate (float value)
                                   {
                                           call(tmp.gameObject, value);
                                   }
                               );
                        }
                }
        }
        #endregion

        #region COLOR

        public static List<Color> GenerateColors(int listNum, int Offset = 0)
        {
                if (listNum <= 0) return null;
                List<Color> list = new List<Color>();

                int du = 360 / listNum + Offset;
                du %= 360;

                for (int i = 0; i < listNum; i++)
                {
                        list.Add(Color.HSVToRGB((float)(i * du) / 360.0f, 1.0f, 1.0f));
                }
                return list;
        }

        public static List<Color> GenerateColorsW()
        {
                List<Color> list = new List<Color>();

                list.Add(Color.HSVToRGB((0.0f / 360.0f), 1.0f, 1.0f));
                list.Add(Color.HSVToRGB((225.0f / 360.0f), 1.0f, 1.0f));
                list.Add(Color.HSVToRGB((315.0f / 360.0f), 1.0f, 1.0f));
                list.Add(Color.HSVToRGB((135.0f / 360.0f), 1.0f, 1.0f));
                list.Add(Color.HSVToRGB((180.0f / 360.0f), 1.0f, 1.0f));


                return list;
        }

        #endregion

        #region TIME

        public static long GetTimeStamp()
        {
                return (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
        }

        #endregion

        #region RAY_CAST
        public static GameObject RayTouchedWithoutUI(Vector3 screenPos, Camera tCamera)
        {
                if (!EventSystem.current.IsPointerOverGameObject())
                {
                        return RayTouched3D(screenPos, tCamera);
                }
                return null;
        }

        public static RaycastHit2D[] hitResult;

        public static bool IsRayTouchedUIView(Vector2 screenPos, Camera tCamera)
        {
                List<RaycastResult> results = GraphicAllRaycaster(screenPos);
                bool isHitUI = false;

                for (int i = 0; i < results.Count; i++)
                {
                        if (results[i].gameObject.layer == 5)
                        {
                                isHitUI = true;
                        }
                }
                if (results.Count <= 0)
                        return false;
                if (isHitUI)
                {
                        return true;
                }
                return false;
        }
        public static GameObject RayTouched3D(Vector2 screenPos, Camera tCamera)
        {
                GameObject goTouched = null;
                bool isHit = false;
                RaycastHit hitResult;
                Ray ray = tCamera.ScreenPointToRay(screenPos);
                if (Physics.Raycast(ray, out hitResult, float.MaxValue, ~0))
                {
                        goTouched = hitResult.collider.gameObject;
                        isHit = true;
                }
#if UNITY_EDITOR
                if (isHit)
                        Debug.DrawLine(ray.origin, hitResult.point, Color.green, 0.5f);
                else
                        Debug.DrawLine(ray.origin, ray.origin + ray.direction * 9999.0f, Color.red, 0.5f);
#endif
                return goTouched;
        }


        public static GameObject RayTouched2D(Vector2 screenPos, Camera tCamera)
        {
                GameObject goTouched = null;
                bool isHit = false;
                Ray ray = tCamera.ScreenPointToRay(screenPos);
                RaycastHit2D hitResult = Physics2D.Raycast(ray.origin, Vector2.zero, float.MaxValue, ~0);
                if (hitResult.collider)
                {
                        goTouched = hitResult.collider.gameObject;
                        isHit = true;
                }
#if UNITY_EDITOR
                if (isHit)
                        Debug.DrawLine(ray.origin, hitResult.point, Color.green, 0.5f);
                else
                        Debug.DrawLine(ray.origin, ray.origin + ray.direction * 9999.0f, Color.red, 0.5f);
#endif
                return goTouched;
        }
        public static List<GameObject> grahicList = new List<GameObject>();
        /// <summary>
        /// UGUI坐标检测
        /// </summary>
        /// <param name="worldPosition"></param>
        /// <param name="tCamera"></param>
        /// <returns></returns>
        public static List<GameObject> RayWorldUGUI(Vector3 worldPosition, Camera tCamera, GraphicRaycaster targetGraphic)
        {
                grahicList.Clear();
                var screenPos = tCamera.WorldToScreenPoint(worldPosition);
                List<RaycastResult> res = GraphicRaycaster(screenPos, targetGraphic);
                if (res.Count <= 0)
                        return null;
                for (int i = 0; i < res.Count; i++)
                {
                        grahicList.Add(res[i].gameObject);
                }
                return grahicList;
        }
        private static List<RaycastResult> GraphicRaycaster(Vector2 pos, GraphicRaycaster targetGraphic)
        {
                var mPointerEventData = new PointerEventData(EventSystem.current);
                mPointerEventData.position = pos;
                List<RaycastResult> results = new List<RaycastResult>();
                targetGraphic.Raycast(mPointerEventData, results);
                return results;
        }
        private static List<RaycastResult> GraphicAllRaycaster(Vector2 pos)
        {
                var mPointerEventData = new PointerEventData(EventSystem.current);
                mPointerEventData.position = pos;
                List<RaycastResult> results = new List<RaycastResult>();
                EventSystem.current.RaycastAll(mPointerEventData, results);
                return results;
        }
        public static GameObject RayTouched3DUGUI(Vector2 worldPosition, Camera tCamera)
        {
                var screenPos = worldPosition; // tCamera.WorldToScreenPoint(worldPosition);
                List<RaycastResult> results = GraphicAllRaycaster(screenPos);
                bool isHitUI3D = false;
                bool isHitUI = false;

                for (int i = 0; i < results.Count; i++)
                {
                        if (results[i].gameObject.layer == 5)
                        {
                                isHitUI = true;
                        }
                        else if (results[i].gameObject.layer == 8)
                        {
                                isHitUI3D = true;
                        }

                }
                if (results.Count <= 0)
                        return null;
                if (!isHitUI && isHitUI3D)
                {
                        return results[0].gameObject;
                }
                return null;
        }

        #endregion

        public static string MD5String(string str)
        {
                if (string.IsNullOrEmpty(str))
                        return null;
                return Rijndael.MD5String(str);
        }

        public static string Base64ToString(string strData)
        {
                byte[] bytesData = System.Convert.FromBase64String(strData);
                string result = System.Text.Encoding.UTF8.GetString(bytesData);
                return result;
        }

        public static string GetXOR(string strKey, string strContent)
        {
                StringBuilder sb = new StringBuilder();
                int keyLen = strKey.Length;
                int len = strContent.Length;
                int i = 0, index = 0;
                while (i < len)
                {
                        char temp = (char)(strContent[i] ^ strKey[index]);
                        sb.Append(temp);
                        index++;
                        index %= keyLen;
                        i++;
                }
                return sb.ToString();
        }
        public static bool EmailIsMatch(string emailString)
        {
                Regex RegEmail = new Regex(@"^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$");
                Match m = RegEmail.Match(emailString);
                return m.Success;
        }

        public static void QuitGame()
        {
#if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
#else
    Application.Quit();
#endif
        }

        public static void AddToManagerTrans(Transform trans)
        {
                GameObject parent = GameObject.Find("Managers");
                if (parent == null)
                {
                        parent = new GameObject("Managers");
                }
                GameObject.DontDestroyOnLoad(parent);
                if (parent != null && trans != null)
                {
                        trans.SetParent(parent.transform);
                }
        }

        public static string ToLiteralStr(string str)
        {
                var output = str
                    .Replace("\\", @"\\")
                        .Replace("\'", @"\'")
                        .Replace("\"", @"\""")
                        .Replace("\n", @"\n")
                        .Replace("\t", @"\t")
                        .Replace("\r", @"\r")
                        .Replace("\b", @"\b")
                        .Replace("\f", @"\f")
                        .Replace("\a", @"\a")
                        .Replace("\v", @"\v")
                        .Replace("\0", @"\0");
                return output;
                // StringBuilder literal = new StringBuilder(str.Length + 2);
                // // literal.Append("\"");
                // foreach (var c in str)
                // {
                //         switch (c)
                //         {

                //                 case '\'': literal.Append(@"\'"); break;
                //                 case '\"': literal.Append("\\\""); break;
                //                 case '\\': literal.Append(@"\\"); break;
                //                 case '\0': literal.Append(@"\0"); break;
                //                 case '\a': literal.Append(@"\a"); break;
                //                 case '\b': literal.Append(@"\b"); break;
                //                 case '\f': literal.Append(@"\f"); break;
                //                 case '\n': literal.Append(@"\n"); break;
                //                 case '\r': literal.Append(@"\r"); break;
                //                 case '\t': literal.Append(@"\t"); break;
                //                 case '\v': literal.Append(@"\v"); break;
                //                 default:
                //                         // ASCII printable character
                //                         if (c >= 0x20 && c <= 0x7e)
                //                         {
                //                                 literal.Append(c);
                //                                 // As UTF16 escaped character
                //                         }
                //                         else
                //                         {
                //                                 literal.Append(@"\u");
                //                                 literal.Append(((int)c).ToString("x4"));
                //                         }
                //                         break;
                //         }
                // }
        
                // //literal.Append("\"");
                // return literal.ToString();
        }

        public static void ResetAnimation(Animation anim, string animName)
        {
                AnimationState state = anim[animName];
                if (state == null)
                {
                        Debug.LogWarning("state is null : " + animName);
                        return;
                }

                state.time = 0;
                anim.Sample();
                anim.Stop(animName);
        }

        public static void PlayAnimationState(Animation anim, string animName)
        {
            AnimationState state = anim[animName];
            if (state == null)
            {
                Debug.LogWarning("state is null : " + animName);
                return;
            }

            //state.time = 0;
            //anim.Sample();
            anim.Play(animName);
        }
        public static Vector4 GetWorldCornersByTrans(GameObject obj)
        {
            Vector3[] corners = new Vector3[4];
            if(obj)
            {
                RectTransform rect = obj.GetComponent<RectTransform>();
                if (rect)
                {
                    rect.GetWorldCorners(corners);
                }
            }
         
            return new Vector4(corners[0].x, corners[0].y, corners[2].x, corners[1].y);
        }


        public static System.Collections.IEnumerator LoadSpriteAsync(string url,Action<Sprite> callBack)
        {
            UnityEngine.Networking.UnityWebRequest www = UnityEngine.Networking.UnityWebRequestTexture.GetTexture(url);
            yield return www.SendWebRequest();

            if (www.result != UnityEngine.Networking.UnityWebRequest.Result.Success)
            {
                Debug.LogError(www.error);
            }
            else
            {
                Texture2D tex = ((UnityEngine.Networking.DownloadHandlerTexture)www.downloadHandler).texture;
                Sprite sp = Sprite.Create(tex, new Rect(0, 0, tex.width, tex.height), new Vector2(tex.width / 2, tex.height / 2));
                if (callBack!=null)
                {
                    callBack(sp);
                }
            }
        }

        public static void LoadSpriteAsyncByUrl(string url, Action<Sprite> callBack)
        {
            CoroutineRunner coroutineRunner = CoroutineRunner.GetInstance();
            if (coroutineRunner)
                coroutineRunner.StartCoroutine(LoadSpriteAsync(url, callBack));
            else
                Debug.LogError("CoroutineRunner.GetInstance() is null");
        }

        public static string HMACSHA256Encrypt(string key, string value)
        {
            StringBuilder result = new StringBuilder();
            using (HMACSHA256 mac = new HMACSHA256(Encoding.UTF8.GetBytes(key)))
            {
                byte[] bytes = mac.ComputeHash(Encoding.UTF8.GetBytes(value));
                for (int i = 0; i < bytes.Length; i++)
                {
                    result.Append(bytes[i].ToString("X2"));
                }
            }
            return result.ToString();
        }

        public static readonly RijndaelManaged ms_RijndaelManager = new RijndaelManaged
        {
            Mode = CipherMode.CBC,
            Padding = PaddingMode.PKCS7
        };
        public static string AesEncrypt(string content, string key, string iv)
        {
            byte[] bs = Encoding.UTF8.GetBytes(content);

            ms_RijndaelManager.Key = Encoding.UTF8.GetBytes(key);

            if (!string.IsNullOrEmpty(iv))
                ms_RijndaelManager.IV = Encoding.UTF8.GetBytes(iv);

            ICryptoTransform transform = ms_RijndaelManager.CreateEncryptor();
            var resultArray = transform.TransformFinalBlock(bs, 0, bs.Length);

            return Convert.ToBase64String(resultArray, 0, resultArray.Length);
        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="content"></param>
        /// <param name="key"></param>
        /// <param name="iv"></param>
        /// <returns></returns>
        public static string AesDecrypt(string content, string key, string iv)
        {
            byte[] bs = Convert.FromBase64String(content);

            ms_RijndaelManager.Key = Encoding.UTF8.GetBytes(key);

            if (!string.IsNullOrEmpty(iv))
                ms_RijndaelManager.IV = Encoding.UTF8.GetBytes(iv);

            ICryptoTransform transform = ms_RijndaelManager.CreateDecryptor();
            var resultArray = transform.TransformFinalBlock(bs, 0, bs.Length);

            return Encoding.UTF8.GetString(resultArray);
    }

    public static string BytesToString(byte[] data)
    {
        return System.Text.Encoding.Default.GetString(data);
    }

    public static void SetParticleSystemStartSpeed(ParticleSystem ps, float startSpeedMin,float startSpeedMax)
    {
        if (ps)
        {
            var main = ps.main;
            main.startSpeed = new ParticleSystem.MinMaxCurve(startSpeedMin, startSpeedMax);
        }
    }
}

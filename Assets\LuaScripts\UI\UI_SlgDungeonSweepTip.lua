local UI_SlgDungeonSweepTip = Class(BaseView)

local BagBase = require("UI.BagItem")
local BagItem = Class(BagBase)

function UI_SlgDungeonSweepTip:OnInit()
    
end

function UI_SlgDungeonSweepTip:OnCreate(param)
    self.curLevelId = DungeonManager:GetLevelId() or 1
    self.allConfigData = ConfigMgr:GetData(ConfigDefine.ID.slg_dungeon)
    self.configData = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_dungeon,(self.curLevelId - 1))

    if not self.configData then
        return
    end
    self.ui.m_txtTitle.text = LangMgr:GetLangFormat(70000094,self.curLevelId - 1)
    self:RefreshSweepReward()
    self:RefreshSweepRewardDrop()

    SetUIImage(self.ui.m_imgTickIcon1,ItemConfig:GetIcon(ItemID.SlgDungeonTickServer),false)
    SetUIImage(self.ui.m_imgTickIcon10,ItemConfig:GetIcon(ItemID.SlgDungeonTickServer),false)
end

function UI_SlgDungeonSweepTip:RefreshSweepReward()
    local dataList = self:GetSweepRewardList()
    for i, v in ipairs(dataList) do
        local bagItem = BagItem.new()
        bagItem:Create(self.ui.m_transSweepRewards,v)
        bagItem:SetScale(1, 1)
    end
end

function UI_SlgDungeonSweepTip:GetSweepRewardList()
    local dataList = {}

    local rewards = Split1(self.configData.reward_sweep,";")
    for i, v in ipairs(rewards) do
        local rewardConfig = Split1(v,"|")
        local itemId = v2n(rewardConfig[1])
        local itemAmount = v2n(rewardConfig[2])
        local itemConfig = ItemConfig:GetDataByID(itemId)
        local itemData = {
            id = itemId,
            num = itemAmount,
            quality = itemConfig.slg_quality,
        }
        table.insert(dataList, itemData)
    end
    return dataList
end

function UI_SlgDungeonSweepTip:RefreshSweepRewardDrop()
    local dataList = self:GetSweepRewardDropList()
    for i, v in ipairs(dataList) do
        local bagItem = BagItem.new()
        bagItem:Create(self.ui.m_transRewardContent,v)
        bagItem:SetScale(1,1)
    end
end

function UI_SlgDungeonSweepTip:GetSweepRewardDropList()
    local dataList = {}
    --掉落奖励(最多显示前四个掉落奖励)
    local dropId = v2n(self.configData.sweep_drop)
    local dropConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_drop,dropId)
    if dropConfig then
        local temp = Split1(dropConfig.drop_list,";")
        for i, v in ipairs(temp) do
            local rewardConfig = Split1(v,"|")
            local itemId = v2n(rewardConfig[1])
            local itemAmount = v2n(rewardConfig[2])
            local itemConfig = ItemConfig:GetDataByID(itemId)
            local itemData = {
                id = itemId,
                num = itemAmount,
                quality = itemConfig.slg_quality,
            }
            table.insert(dataList, itemData)
        end
    end
    return dataList
end

function UI_SlgDungeonSweepTip:OnRefresh(param)
    
end

function UI_SlgDungeonSweepTip:onDestroy()
    
end

function UI_SlgDungeonSweepTip:GotoSweep(isTen)
    --local maxConfigLevel = table.count(self.allConfigData)
    --if self.curLevelId > maxConfigLevel then
    --    --"您已通关最高关卡"
    --    UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLang(70000015))
    --else
    --    local sweepCount = isTen and 10 or 1
    --    local cost = DungeonManager:GetCostDungeonTick()
    --    local num = BagManager:GetBagItemCount(ItemID.SlgDungeonTickServer)
    --    local needCost = sweepCount * cost
    --    if num < needCost then
    --        UI_SHOW(UIDefine.UI_SlgBuyDungeonTickView)
    --        return
    --    end
    --    DungeonManager:OnReqDungeonSweep(self.curLevelId,sweepCount)
    --end

    local sweepCount = isTen and 10 or 1
    local cost = DungeonManager:GetCostDungeonTick()
    local num = BagManager:GetBagItemCount(ItemID.SlgDungeonTickServer)
    local needCost = sweepCount * cost
    if num < needCost then
        UI_SHOW(UIDefine.UI_SlgBuyDungeonTickView)
        return
    end
    DungeonManager:OnReqDungeonSweep(self.curLevelId,sweepCount)
end

function UI_SlgDungeonSweepTip:onUIEventClick(go,param)
    local name = go.name
    
    if name == "btnReturn" then
        self:Close()
    elseif name == "m_btnSweepFightOnce" then
        self:GotoSweep()
    elseif name == "m_btnSweepFightTen" then
        self:GotoSweep(true)
    end
end

return UI_SlgDungeonSweepTip
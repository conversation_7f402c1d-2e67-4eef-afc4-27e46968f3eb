local TaskFlyItem = {}
local M = TaskFlyItem

local prePath = "Prefab/UI/FlyTask.prefab"
local pre
local duration = 0.7

function M:Create(parent)
    local item = {}
    setmetatable(item,{__index = M})
    ResMgr:LoadAssetWithCache(prePath, AssetDefine.LoadType.Instant, function(prefab)
        pre = prefab
        local newGo = CreateGameObjectWithParent(pre,parent)
        item.go = newGo
        item.trans = newGo.transform
        item:Init()
    end)
    return item
end

function M:Init()
    self.taskImg = GetChild(self.go,"Image",UEUI.Image)
end

function M:SetItem(id,callback,nowNum)
	
end

function M:PlayAddTask(pos,endPos,lastCall)
    self.fly = self.go.transform
    self.fly.position = pos
    self.finishCallBack = lastCall
    endPos = endPos or Vector3.zero
    local function OnReady()
        local startPos = pos
        local def = endPos - startPos
        local angle = Vector3.Angle(Vector3.New(def.x,def.y,0), Vector3.New(1, 0, 0))
        --local xAngleCenter = angle
        local radius =  5
        local x, y = GetXYByAngleRadius(math.floor(angle-30), radius)
        local ref = Vector3.New(x,y,0)
        self.helper = require "Base.BezierHelper".new()
        self.helper:setup(startPos,ref,endPos)
        self.delta = 0
    end
    local ui = DefineFaceList[NetUpdatePlayerData.playerInfo.curMap]
    TimeMgr:CreateTimer(ui,OnReady,0.1,1)
end

function M:ShowActive(isShow)
    SetActive(self.go,isShow)
end

function M:TickUI(delta)
    if self.delta == nil then return end
    self.delta = self.delta + delta
    local time = 0 - math.cos(self.delta / duration * 1.57079637) + 1
    local posNow = self.helper:getPos(time)
    self.fly.position = posNow
    if self.delta >= duration then
        if self.finishCallBack then
            self.finishCallBack()
        end
        UEGO.Destroy(self.go)
        self.delta = nil
    end
end

function M:ClickItem(arg1)

end

function M:Close()
    UEGO.Destroy(self.go)
end

--------------------------

return M
﻿
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;
using UnityEngine.U2D;

public class ETAltlasPack
{
        public static string path_sprite = "Assets/ResPackage/Sprite";
        public static string path_atlas = "Assets/ResPackage/Atlas";

        public static void ClearAtlas()
        {
                string desStr = path_atlas;
                if (!Directory.Exists(desStr))
                {
                        return;
                }
                FileUtility.SafeClearDir(desStr);
        }

        public static void AutoAtlasPack()
        {
                if (!Directory.Exists(path_atlas))
                {
                        Directory.CreateDirectory(path_atlas);
                }
                if (!Directory.Exists(path_sprite))
                {
                        Debug.LogError(path_sprite + " is not Exists");
                        return;
                }

                CreateAtlasFile(false);
                PackAndRefresh();
        }

        public static List<string> GetFileList(string path)
        {

                if (!Directory.Exists(path))
                {
                        return null;
                }

                List<string> file_list = new List<string>();
                string[] files = Directory.GetFiles(path, "*");
                for (int i = 0; i < files.Length; i++)
                {
                        if (files[i].EndsWith(".meta"))
                                continue;
                        file_list.Add(files[i]);
                }

                return file_list;
        }

        public static void CreateAtlasFile(bool isDirMode)
        {
                string[] directoryPath = Directory.GetDirectories(path_sprite, "*", SearchOption.AllDirectories);

                for (int i = 0; i < directoryPath.Length; i++)
                {
                        List<UnityEngine.Object> obj_list = new List<UnityEngine.Object>();

                        string newPath = directoryPath[i].Replace("\\", "/");
                        string atlasName = Path.GetFileName(newPath);

                        if (isDirMode)
                        {
                                UnityEngine.Object obj = AssetDatabase.LoadAssetAtPath(newPath, typeof(UnityEngine.Object));
                                obj_list.Add(obj);
                        }
                        else
                        {
                                List<string> file_list = GetFileList(newPath);
                                if (file_list == null || file_list.Count == 0)
                                        continue;
                                foreach (string str in file_list)
                                {
                                        UnityEngine.Object obj = AssetDatabase.LoadAssetAtPath(str, typeof(UnityEngine.Object));
                                        if (obj)
                                        {
                                                obj_list.Add(obj);
                                        }
                                }
                        }

                        string pathOut = path_atlas + "/" + atlasName + ".spriteatlas";
                        if (!File.Exists(pathOut))
                        {
                                SpriteAtlas atlas = CreateAtlas(atlasName, obj_list.ToArray());
                                AssetDatabase.CreateAsset(atlas, pathOut);
                                AssetDatabase.SaveAssets();
                                Debug.Log("****** Create Atlas:" + pathOut);
                        }
                        else
                        {
                                Debug.Log("****** Atlas exist:" + pathOut);
                        }
                }

                AssetDatabase.Refresh();
        }
        public static void PackAndRefresh()
        {
                SpriteAtlasUtility.PackAllAtlases(BuildTarget.NoTarget);
                AssetDatabase.Refresh();
        }

        public static SpriteAtlas CreateAtlas(string atlasName, UnityEngine.Object[] Assets)
        {
                SpriteAtlas atlas = new SpriteAtlas();

                SpriteAtlasPackingSettings settingPack = new SpriteAtlasPackingSettings();
                settingPack.padding = 2;
                atlas.SetPackingSettings(settingPack);

                SpriteAtlasTextureSettings settingTxt = new SpriteAtlasTextureSettings();
                settingTxt.sRGB = true;
                settingTxt.filterMode = FilterMode.Bilinear;
                //         if(atlasName == "item" || atlasName == "tile")
                //             settingTxt.generateMipMaps = true;
                atlas.SetTextureSettings(settingTxt);

                TextureImporterPlatformSettings settingImport = new TextureImporterPlatformSettings();

                settingImport.maxTextureSize = 2048;
                // #if UNITY_IOS
                //         if(atlasName == "ui" || atlasName == "ui2")
                //         {
                //             settingImport.format = TextureImporterFormat.ASTC_RGB_4x4;
                //         }
                // #endif
                if(atlasName == "bg" || atlasName == "ui_tujian_1" || atlasName == "ui_tujian_2"
                        || atlasName == "ui_tujian_3"   || atlasName == "zoo_wall"   || atlasName == "ui_landtip_0")
                {
                        settingImport.format = TextureImporterFormat.ASTC_8x8;
                }
                else
                {
                        settingImport.format = TextureImporterFormat.ASTC_6x6;
                }
                //settingImport.format = TextureImporterFormat.ASTC_6x6;
                settingImport.textureCompression = TextureImporterCompression.Compressed;
                atlas.SetPlatformSettings(settingImport);

                atlas.SetIncludeInBuild(false);
                atlas.Add(Assets);
                atlas.name = atlasName;

                return atlas;
        }
}

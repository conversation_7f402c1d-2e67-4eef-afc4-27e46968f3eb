local UI_MailConentModel = {}

UI_MailConentModel.config = {["name"] = "UI_MailConent", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_MailConentModel:Init(c)
    c.ui = {}    
    c.ui.m_goEmptyTip2 = GetChild(c.uiGameObject,"bg/content/content/m_goEmptyTip2")
    c.ui.m_goMailInfoTop = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoTop")
    c.ui.m_txtTitle = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoTop/m_txtTitle",UEUI.Text)
    c.ui.m_txtTime = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoTop/m_txtTime",UEUI.Text)
    c.ui.m_btnTranslate = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoTop/m_btnTranslate",UEUI.Button)
    c.ui.m_btnBackTranslate = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoTop/m_btnBackTranslate",UEUI.Button)
    c.ui.m_goMailInfoCenter = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoCenter")
    c.ui.m_goMailInfoBottom = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoBottom")
    c.ui.m_goRewardCell = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoBottom/m_goRewardCell")
    c.ui.m_goGiftviewPort = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoBottom/m_goGiftviewPort")
    c.ui.m_btnClaim = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoBottom/m_btnClaim",UEUI.Button)
    c.ui.m_btnHasClaim = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoBottom/m_btnHasClaim",UEUI.Button)
    c.ui.m_btnDelete = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoBottom/m_btnDelete",UEUI.Button)
    c.ui.m_txtNotice = GetChild(c.uiGameObject,"bg/content/content/m_goMailInfoBottom/m_txtNotice",UEUI.Text)
    c.ui.m_goTemplateText = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateText")
    c.ui.m_goTemplateArenaSettleReward = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateArenaSettleReward")
    c.ui.m_goSpine = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateArenaSettleReward/Viewport/Content/list/bg/jjcRankItem/m_goSpine")
    c.ui.m_goTemplateLeagueBossRank = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateLeagueBossRank")
    c.ui.m_goTemplateUnsupportedType = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateUnsupportedType")
    c.ui.m_goTemplateTrainDepart = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainDepart")
    c.ui.m_goMemberItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainDepart/Scroll View/Viewport/Content/main/prefab/m_goMemberItem")
    c.ui.m_goTemplateTrainArrival = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival")
    c.ui.m_goRewardItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival/Scroll View/Viewport/Content/main/prefab/m_goRewardItem")
    c.ui.m_goMemberItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival/Scroll View/Viewport/Content/main/prefab/m_goMemberItem")
    c.ui.m_goRecordItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival/Scroll View/Viewport/Content/main/prefab/m_goRecordItem")
    c.ui.m_goTemplateTrainReward = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainReward")
    c.ui.m_goTemplateWorlBossRank = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateWorlBossRank")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_MailConentModel
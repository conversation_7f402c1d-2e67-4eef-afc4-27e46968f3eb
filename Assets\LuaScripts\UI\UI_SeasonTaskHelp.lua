local UI_SeasonTaskHelp = Class(BaseHelpView)

function UI_SeasonTaskHelp:OnInit()

end

function UI_SeasonTaskHelp:OnCreate(callback)
    self:OnCreate_Base()
    self.callback = callback
	local root = GetChild(self.ui.m_goConten,"Page1/Content")
	self:ShowSequenceAnim(root)
	local root2 = GetChild(self.ui.m_goConten,"Page2/Content")
	self:ShowSequenceAnim(root2)
end

function UI_SeasonTaskHelp:OnRefresh(param)

end

function UI_SeasonTaskHelp:onDestroy()
    if self.callback then
        self.callback()
        self.callback = nil
    end
end

function UI_SeasonTaskHelp:onUIEventClick(go)
    local name = go.name
    if name == "m_btnClose" then
        self:Close()
    elseif name == "m_btnOK" then
        self:Close()
    end
end

function UI_SeasonTaskHelp:OnMoveStart_Virtual(pageIndex)
    -- 页码边界检查
    if pageIndex > self.ui.m_transDesc.childCount then
        return
    end
    -- 隐藏已显示的标题
    local child
    for i = 0, self.ui.m_transDesc.childCount - 1, 1 do
        child = self.ui.m_transDesc:GetChild(i)
        if child.gameObject.activeSelf then
            child.gameObject:SetActive(false)
            break
        end
    end
	
    -- 显示页码对应的标题
    self.ui.m_transDesc:GetChild(pageIndex - 1).gameObject:SetActive(true)
	
	local child2
	for i = 0, self.ui.m_goConten.transform.childCount - 1, 1 do
		child2 = self.ui.m_goConten.transform:GetChild(i)
		if child2.gameObject.activeSelf then
			child2.gameObject:SetActive(false)
			break
		end
	end
	self.ui.m_goConten.transform:GetChild(pageIndex - 1).gameObject:SetActive(true)
end

function UI_SeasonTaskHelp:ShowSequenceAnim(root)
	local list = {
		GetChild(root,"icon1"),
		GetChild(root,"row1"),
		GetChild(root,"icon2"),
		GetChild(root,"row2"),
		GetChild(root,"icon3"),
	}
	self.sequence = TweenMgr:CreateSequence(UIDefine.UI_BuyPassPortTip, false, nil)
	for i = 1,#list do
		list[i].transform.localScale = Vector3.New(0,0,0)
		if(i > 1) then
			self.sequence:AppendInterval(0.15)
		end
		self.sequence:AppendCallback(function()
				if not self.ui then return end
				self:ShowHelpCell(list[i].transform)
			end)
	end
end

function UI_SeasonTaskHelp:ShowHelpCell(root)
	root.localScale = Vector3.New(0,0,1)
	root:DOScale(Vector3.New(1.2,1.2,1),0.1):OnComplete(function()
			root:DOScale(Vector3.New(0.9,0.9,1),0.1):OnComplete(function()
					root:DOScale(Vector3.New(1.05,1.05,1),0.1):OnComplete(function()
							root:DOScale(Vector3.New(1,1,1),0.1)
						end)
				end)
		end)
end

return UI_SeasonTaskHelp

local UI_CopyMainFace2Model = {}

UI_CopyMainFace2Model.config = {["name"] = "UI_CopyMainFace2", ["layer"] = UILayerType.Normal, ["type"] = UIType.Base, ["isAutoClose"] = false, ["anim"] = 0,["background"] = 0, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_CopyMainFace2Model:Init(c)
    c.ui = {}    
    c.ui.m_goActivityGrop = GetChild(c.uiGameObject,"Top/m_goActivityGrop")
    c.ui.m_goActivityTabRoot = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot")
    c.ui.m_imgActivityTab1 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab1",UEUI.Image)
    c.ui.m_imgActivityTab2 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab2",UEUI.Image)
    c.ui.m_imgActivityTab3 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab3",UEUI.Image)
    c.ui.m_imgActivityTab4 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab4",UEUI.Image)
    c.ui.m_imgActivityTab5 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab5",UEUI.Image)
    c.ui.m_imgActivityTab6 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab6",UEUI.Image)
    c.ui.m_imgActivityTab7 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab7",UEUI.Image)
    c.ui.m_imgActivityTab8 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab8",UEUI.Image)
    c.ui.m_imgActivityTab9 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab9",UEUI.Image)
    c.ui.m_imgActivityTab10 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab10",UEUI.Image)
    c.ui.m_imgActivityTab11 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_goActivityTabRoot/m_imgActivityTab11",UEUI.Image)
    c.ui.m_btnActivityEnter = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_btnActivityEnter",UEUI.Button)
    c.ui.m_scrollviewActivityEnter = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter",UEUI.ScrollRect)
    c.ui.m_goActivityTabGroup1 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1")
    c.ui.m_goNotCompetition = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goNotCompetition")
    c.ui.m_goNotCompetitionHead = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goNotCompetition/m_goNotCompetitionHead")
    c.ui.m_imgNotCompetitionHeadLeft = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goNotCompetition/m_goNotCompetitionHead/Left/m_imgNotCompetitionHeadLeft",UEUI.Image)
    c.ui.m_goCompetition = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition")
    c.ui.m_sliderVS = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_sliderVS",UEUI.Slider)
    c.ui.m_goHead = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goHead")
    c.ui.m_imgHeadLeft = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goHead/Left/m_imgHeadLeft",UEUI.Image)
    c.ui.m_txtNameLeft = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goHead/Left/m_txtNameLeft",UEUI.Text)
    c.ui.m_imgHeadRight = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goHead/Right/m_imgHeadRight",UEUI.Image)
    c.ui.m_txtNameRight = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goHead/Right/m_txtNameRight",UEUI.Text)
    c.ui.m_txtCompetition = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/score/m_txtCompetition",UEUI.Text)
    c.ui.m_txtCompetitionCountDown = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/Time/m_txtCompetitionCountDown",UEUI.Text)
    c.ui.m_goScore = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goScore")
    c.ui.m_txtScoreBlue = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goScore/m_txtScoreBlue",UEUI.Text)
    c.ui.m_txtScoreRed = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goScore/m_txtScoreRed",UEUI.Text)
    c.ui.m_goScoreBlue = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goScore/m_goScoreBlue")
    c.ui.m_txtScoreBlueAdd = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goScore/m_goScoreBlue/m_txtScoreBlueAdd",UEUI.Text)
    c.ui.m_goScoreRed = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goScore/m_goScoreRed")
    c.ui.m_txtScoreRedAdd = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_goScore/m_goScoreRed/m_txtScoreRedAdd",UEUI.Text)
    c.ui.m_transFlyPos = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_transFlyPos",UE.Transform)
    c.ui.m_transFlyPosRobot = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_goCompetition/m_transFlyPosRobot",UE.Transform)
    c.ui.m_btnCompetition = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup1/m_btnCompetition",UEUI.Button)
    c.ui.m_goActivityTabGroup2 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup2")
    c.ui.m_goActivityTabGroup3 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3")
    c.ui.m_goRank = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_goRank")
    c.ui.m_txtRank = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_goRank/m_txtRank",UEUI.Text)
    c.ui.m_goOpen = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_goOpen")
    c.ui.m_goBowlingConditionNO = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_goOpen/tipObj/selectObj/m_goBowlingConditionNO")
    c.ui.m_goBowlingConditionOK = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_goOpen/tipObj/selectObj/m_goBowlingConditionOK")
    c.ui.m_txtBowlingCondition = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_goOpen/tipObj/m_txtBowlingCondition",UEUI.Text)
    c.ui.m_txtDown = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_goOpen/m_txtDown",UEUI.Text)
    c.ui.m_btnBowlingBattle = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup3/m_btnBowlingBattle",UEUI.Button)
    c.ui.m_goActivityTabGroup4 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup4")
    c.ui.m_goActivityTabGroup5 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup5")
    c.ui.m_btnActiveFollow = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup5/m_btnActiveFollow",UEUI.Button)
    c.ui.m_goActivityTabGroup6 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup6")
    c.ui.m_btnSlg = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup6/m_btnSlg",UEUI.Button)
    c.ui.m_imgBoxIcon = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup6/m_imgBoxIcon",UEUI.Image)
    c.ui.m_goFullBoxDot = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup6/m_goFullBoxDot")
    c.ui.m_goActivityTabGroup7 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup7")
    c.ui.m_imgSlgTower = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup7/m_imgSlgTower",UEUI.Image)
    c.ui.m_btnTowerClimbing = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup7/m_btnTowerClimbing",UEUI.Button)
    c.ui.m_goActivityTabGroup8 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup8")
    c.ui.m_btnTradeWagons = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup8/m_btnTradeWagons",UEUI.Button)
    c.ui.m_goTradeWagonsRedPoint = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup8/m_goTradeWagonsRedPoint")
    c.ui.m_goActivityTabGroup9 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup9")
    c.ui.m_btnArena = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup9/m_btnArena",UEUI.Button)
    c.ui.m_txtJJcRank = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup9/GameObject (1)/m_txtJJcRank",UEUI.Text)
    c.ui.m_txtJJcPower = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup9/GameObject/m_txtJJcPower",UEUI.Text)
    c.ui.m_goArenaRedPoint = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup9/m_goArenaRedPoint")
    c.ui.m_goActivityTabGroup10 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup10")
    c.ui.m_goWorldBossRed = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup10/m_goWorldBossRed")
    c.ui.m_goActivityTabGroup11 = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup11")
    c.ui.m_goTopFightRed = GetChild(c.uiGameObject,"Top/m_goActivityGrop/m_scrollviewActivityEnter/Mask/Content/m_goActivityTabGroup11/m_goTopFightRed")
    c.ui.m_goEnergyDay = GetChild(c.uiGameObject,"Top/m_goEnergyDay")
    c.ui.m_goEnemyIcon = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_goEnemyIcon")
    c.ui.m_txtEnergyNumber = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_goEnemyIcon/energynumber_bg/m_txtEnergyNumber",UEUI.Text)
    c.ui.m_txtEnergyBoxTime = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_goEnemyIcon/Image/m_txtEnergyBoxTime",UEUI.Text)
    c.ui.m_sliderBackGround = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_sliderBackGround",UEUI.Slider)
    c.ui.m_goEnergy1 = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy1")
    c.ui.m_txtBox1 = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy1/m_txtBox1",UEUI.Text)
    c.ui.m_goEnergy2 = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy2")
    c.ui.m_txtBox2 = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy2/m_txtBox2",UEUI.Text)
    c.ui.m_goEnergy3 = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy3")
    c.ui.m_txtBox3 = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_sliderBackGround/BoxList/m_goEnergy3/m_txtBox3",UEUI.Text)
    c.ui.m_goShowData = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_goShowData")
    c.ui.m_imgEnergyData = GetChild(c.uiGameObject,"Top/m_goEnergyDay/m_goShowData/Image/m_imgEnergyData",UEUI.Image)
    c.ui.m_goRoleHead = GetChild(c.uiGameObject,"Top/m_goRoleHead")
    c.ui.m_imgHead = GetChild(c.uiGameObject,"Top/m_goRoleHead/Image/m_imgHead",UEUI.Image)
    c.ui.m_goSetlistsPoint = GetChild(c.uiGameObject,"Top/m_goRoleHead/m_goSetlistsPoint")
    c.ui.m_goActCenter = GetChild(c.uiGameObject,"Top/m_goActCenter")
    c.ui.m_btnActCenter = GetChild(c.uiGameObject,"Top/m_goActCenter/m_btnActCenter",UEUI.Button)
    c.ui.m_goActPoint = GetChild(c.uiGameObject,"Top/m_goActCenter/m_btnActCenter/btnBg/m_goActPoint")
    c.ui.m_txtActPointNum = GetChild(c.uiGameObject,"Top/m_goActCenter/m_btnActCenter/btnBg/m_goActPoint/point/m_txtActPointNum",UEUI.Text)
    c.ui.m_txtDiamond = GetChild(c.uiGameObject,"Top/m_goActCenter/m_btnActCenter/btnBg/m_txtDiamond",UEUI.Text)
    c.ui.m_doDiamond = GetChild(c.uiGameObject,"Top/m_goActCenter/m_btnActCenter/btnBg/m_doDiamond",TweenAnim)
    c.ui.m_goArrow = GetChild(c.uiGameObject,"Top/m_goActCenter/m_goArrow")
    c.ui.m_goWorker = GetChild(c.uiGameObject,"LeftTop/m_goWorker")
    c.ui.m_goWork1 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goWork1")
    c.ui.m_doWork1 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goWork1/m_doWork1",TweenAnim)
    c.ui.m_doWorkCount1 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goWork1/m_doWorkCount1",TweenAnim)
    c.ui.m_goWork2 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goWork2")
    c.ui.m_doWork2 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goWork2/m_doWork2",TweenAnim)
    c.ui.m_doWorkCount2 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goWork2/m_doWorkCount2",TweenAnim)
    c.ui.m_goExtraWork = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goExtraWork")
    c.ui.m_goWorkNotBuy = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goExtraWork/m_goWorkNotBuy")
    c.ui.m_doWorkCount3 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goExtraWork/m_goWorkNotBuy/m_doWorkCount3",TweenAnim)
    c.ui.m_doWork3 = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goExtraWork/m_goWorkNotBuy/m_doWork3",TweenAnim)
    c.ui.m_imgWorkSlider = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goExtraWork/m_goWorkNotBuy/Image/m_imgWorkSlider",UEUI.Image)
    c.ui.m_goWorkBuy = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goExtraWork/m_goWorkBuy")
    c.ui.m_goWorkerPoint = GetChild(c.uiGameObject,"LeftTop/m_goWorker/m_goExtraWork/m_goWorkBuy/m_goWorkerPoint")
    c.ui.m_imgGoUpBg = GetChild(c.uiGameObject,"LeftTop/m_imgGoUpBg",UEUI.Image)
    c.ui.m_goActivityList = GetChild(c.uiGameObject,"LeftTop/m_imgGoUpBg/m_goActivityList")
    c.ui.m_goLimit = GetChild(c.uiGameObject,"LeftTop/m_imgGoUpBg/m_goActivityList/Viewport/m_goLimit")
    c.ui.m_goTip = GetChild(c.uiGameObject,"LeftBottom/m_goTip")
    c.ui.m_goRecover = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goRecover")
    c.ui.m_goExpand = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goExpand")
    c.ui.m_goTips = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips")
    c.ui.m_goSelect = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips/m_goSelect")
    c.ui.m_txtTipTitle = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips/m_goSelect/m_txtTipTitle",UEUI.Text)
    c.ui.m_txtTipContent = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips/m_goSelect/m_txtTipContent",UEUI.Text)
    c.ui.m_goDetails = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips/m_goSelect/Common/m_goDetails")
    c.ui.m_goDeleted = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips/m_goSelect/Common/m_goDeleted")
    c.ui.m_goHeroBtn = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips/m_goSelect/m_goHeroBtn")
    c.ui.m_goNoSelect = GetChild(c.uiGameObject,"LeftBottom/m_goTip/m_goTips/m_goNoSelect")
    c.ui.m_goMsBuff = GetChild(c.uiGameObject,"LeftBottom/m_goMsBuff")
    c.ui.m_txtMsBuff = GetChild(c.uiGameObject,"LeftBottom/m_goMsBuff/CountDown/m_txtMsBuff",UEUI.Text)
    c.ui.m_doFinish = GetChild(c.uiGameObject,"LeftBottom/m_doFinish",TweenAnim)
    c.ui.m_btnFinish = GetChild(c.uiGameObject,"LeftBottom/m_doFinish/m_btnFinish",UEUI.Button)
    c.ui.m_imgTask = GetChild(c.uiGameObject,"LeftBottom/m_doFinish/m_btnFinish/btnBg/m_imgTask",UEUI.Image)
    c.ui.m_imgProgress = GetChild(c.uiGameObject,"LeftBottom/m_doFinish/bg/m_imgProgress",UEUI.Image)
    c.ui.m_txtScore = GetChild(c.uiGameObject,"LeftBottom/m_doFinish/bg/m_txtScore",UEUI.Text)
    c.ui.m_goTask = GetChild(c.uiGameObject,"LeftBottom/m_goTask")
    c.ui.m_goUp = GetChild(c.uiGameObject,"LeftBottom/m_goTask/m_goUp")
    c.ui.m_goDown = GetChild(c.uiGameObject,"LeftBottom/m_goTask/m_goDown")
    c.ui.m_goTaskPos = GetChild(c.uiGameObject,"LeftBottom/m_goTask/taskList/Viewport/m_goTaskPos")
    c.ui.m_goTasks = GetChild(c.uiGameObject,"LeftBottom/m_goTask/taskList/Viewport/m_goTasks")
    c.ui.m_goMaxReward = GetChild(c.uiGameObject,"LeftBottom/m_goMaxReward")
    c.ui.m_txtReward = GetChild(c.uiGameObject,"LeftBottom/m_goMaxReward/RewardBG/Image2/m_txtReward",UEUI.Text)
    c.ui.m_goChristamasTaskPoint = GetChild(c.uiGameObject,"LeftBottom/m_goMaxReward/RewardBG/m_goChristamasTaskPoint")
    c.ui.m_goMagic = GetChild(c.uiGameObject,"RightTop/ResourceItems/m_goMagic")
    c.ui.m_goCoin = GetChild(c.uiGameObject,"RightTop/ResourceItems/m_goCoin")
    c.ui.m_imgCoinBuy = GetChild(c.uiGameObject,"RightTop/ResourceItems/m_goCoin/m_imgCoinBuy",UEUI.Image)
    c.ui.m_doCoin = GetChild(c.uiGameObject,"RightTop/ResourceItems/m_goCoin/m_doCoin",TweenAnim)
    c.ui.m_txtCoin = GetChild(c.uiGameObject,"RightTop/ResourceItems/m_goCoin/m_txtCoin",UEUI.Text)
    c.ui.m_scrollviewActivity = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity",UEUI.ScrollRect)
    c.ui.m_goActivity = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity/Viewport/m_goActivity")
    c.ui.m_goFirstPack = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity/Viewport/m_goActivity/m_goFirstPack")
    c.ui.m_txtFirstPack = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity/Viewport/m_goActivity/m_goFirstPack/Image/m_txtFirstPack",UEUI.Text)
    c.ui.m_imgFirstGiftRed = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity/Viewport/m_goActivity/m_goFirstPack/m_imgFirstGiftRed",UEUI.Image)
    c.ui.m_goTriggerGift = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity/Viewport/m_goActivity/m_goTriggerGift")
    c.ui.m_imgTriggerGiftIcon = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity/Viewport/m_goActivity/m_goTriggerGift/empty/m_imgTriggerGiftIcon",UEUI.Image)
    c.ui.m_txtTriggerGift = GetChild(c.uiGameObject,"RightTop/rightVer 1/m_scrollviewActivity/Viewport/m_goActivity/m_goTriggerGift/Image/m_txtTriggerGift",UEUI.Text)
    c.ui.m_transLimitParent = GetChild(c.uiGameObject,"RightTop/m_transLimitParent",UE.Transform)
    c.ui.m_goCarousel = GetChild(c.uiGameObject,"RightTop/m_transLimitParent/m_goCarousel")
    c.ui.m_txtGiftPack = GetChild(c.uiGameObject,"RightTop/m_transLimitParent/m_goCarousel/Image/m_txtGiftPack",UEUI.Text)
    c.ui.m_goGiftDaily = GetChild(c.uiGameObject,"RightTop/m_transLimitParent/m_goGiftDaily")
    c.ui.m_goDailyGiftRed = GetChild(c.uiGameObject,"RightTop/m_transLimitParent/m_goGiftDaily/m_goDailyGiftRed")
    c.ui.m_txtGiftDaily = GetChild(c.uiGameObject,"RightTop/m_transLimitParent/m_goGiftDaily/Image/m_txtGiftDaily",UEUI.Text)
    c.ui.m_goBombShop = GetChild(c.uiGameObject,"RightBottom/m_goBombShop")
    c.ui.m_doBomb = GetChild(c.uiGameObject,"RightBottom/m_goBombShop/m_doBomb",TweenAnim)
    c.ui.m_imgBomb = GetChild(c.uiGameObject,"RightBottom/m_goBombShop/m_doBomb/m_imgBomb",UEUI.Image)
    c.ui.m_imgBomb1 = GetChild(c.uiGameObject,"RightBottom/m_goBombShop/m_doBomb/m_imgBomb1",UEUI.Image)
    c.ui.m_txtBumbNum = GetChild(c.uiGameObject,"RightBottom/m_goBombShop/m_doBomb/m_txtBumbNum",UEUI.Text)
    c.ui.m_imgAdd = GetChild(c.uiGameObject,"RightBottom/m_goBombShop/m_doBomb/m_imgAdd",UEUI.Image)
    c.ui.m_goBombPoint = GetChild(c.uiGameObject,"RightBottom/m_goBombShop/m_goBombPoint")
    c.ui.m_goRightBottomList = GetChild(c.uiGameObject,"RightBottom/m_goRightBottomList")
    c.ui.m_goTaskList = GetChild(c.uiGameObject,"RightBottom/m_goTaskList")
    c.ui.m_goPrivilege = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege")
    c.ui.m_imgMagnet = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege/m_imgMagnet",UEUI.Image)
    c.ui.m_btnMagnet = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege/m_btnMagnet",UEUI.Button)
    c.ui.m_imgPrivilegeHand = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege/m_imgPrivilegeHand",UEUI.Image)
    c.ui.m_imgPrivilegeTime = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege/m_imgPrivilegeTime",UEUI.Image)
    c.ui.m_txtPrivilegeTime = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege/m_imgPrivilegeTime/m_txtPrivilegeTime",UEUI.Text)
    c.ui.m_btnEnergy = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege/m_btnEnergy",UEUI.Button)
    c.ui.m_goPrivilegeRed = GetChild(c.uiGameObject,"RightBottom/m_goPrivilege/m_goPrivilegeRed")
    c.ui.m_imgbg = GetChild(c.uiGameObject,"m_imgbg",UEUI.Image)
    c.ui.m_goEnergy = GetChild(c.uiGameObject,"bottom/m_goEnergy")
    c.ui.m_doEnergy = GetChild(c.uiGameObject,"bottom/m_goEnergy/m_doEnergy",TweenAnim)
    c.ui.m_imgEnergySlider = GetChild(c.uiGameObject,"bottom/m_goEnergy/m_imgEnergySlider",UEUI.Image)
    c.ui.m_goEnergyTime = GetChild(c.uiGameObject,"bottom/m_goEnergy/m_imgEnergySlider/m_goEnergyTime")
    c.ui.m_txtEnergyTime = GetChild(c.uiGameObject,"bottom/m_goEnergy/m_imgEnergySlider/m_goEnergyTime/m_txtEnergyTime",UEUI.Text)
    c.ui.m_imgEnergyBuy = GetChild(c.uiGameObject,"bottom/m_goEnergy/m_imgEnergyBuy",UEUI.Image)
    c.ui.m_txtEnergy = GetChild(c.uiGameObject,"bottom/m_goEnergy/m_txtEnergy",UEUI.Text)
    c.ui.m_goBack = GetChild(c.uiGameObject,"bottom/m_goBack")
    c.ui.m_goBackGIft = GetChild(c.uiGameObject,"bottom/m_goBack/back/m_goBackGIft")
    c.ui.m_goBackEnergy = GetChild(c.uiGameObject,"bottom/m_goBack/back/m_goBackEnergy")
    c.ui.m_goCollect = GetChild(c.uiGameObject,"bottom/m_goCollect")
    c.ui.m_doCollect = GetChild(c.uiGameObject,"bottom/m_goCollect/m_doCollect",TweenAnim)
    c.ui.m_goCollectFinish = GetChild(c.uiGameObject,"bottom/m_goCollect/m_doCollect/m_goCollectFinish")
    c.ui.m_goCollectPoint = GetChild(c.uiGameObject,"bottom/m_goCollect/m_goCollectPoint")
    c.ui.m_goMarket = GetChild(c.uiGameObject,"bottom/m_goMarket")
    c.ui.m_btnShop = GetChild(c.uiGameObject,"bottom/m_goMarket/m_btnShop",UEUI.Button)
    c.ui.m_goMarketPoint = GetChild(c.uiGameObject,"bottom/m_goMarket/m_btnShop/btnBg/m_goMarketPoint")
    c.ui.m_goArrow = GetChild(c.uiGameObject,"bottom/m_goMarket/m_btnShop/m_goArrow")
    c.ui.m_goLimitEnter = GetChild(c.uiGameObject,"bottom/m_goLimitEnter")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_CopyMainFace2Model
local UI_SkiingShopHelp = Class(BaseView)

function UI_SkiingShopHelp:OnInit()
    local config = ConfigMgr:GetDataByKey(ConfigDefine.ID.activity_help,"prefab",UIDefine.UI_SkiingShopHelp,true)
    if not config then
        Log.Error("获取帮助界面配置表错误 error ")
        return
    end
    table.sort(config,function (a,b)
        if a.page < b.page then
            return true
        else
            return false
        end
    end)
    self.describes = {}
    for k, v in ipairs(config) do
        table.insert(self.describes,v.describe)
        local item1 = self.ui.m_scrollview.content:GetChild(k - 1):GetChild(5):GetComponent(typeof(UEUI.Image))
        local item2 = self.ui.m_scrollview.content:GetChild(k - 1):GetChild(6):GetComponent(typeof(UEUI.Image))
        local item3 = self.ui.m_scrollview.content:GetChild(k - 1):GetChild(7):GetComponent(typeof(UEUI.Image))
        local iconArray = v.how_icon:split('|')
        local icon1 = iconArray[1]
        local icon2 = iconArray[2]
        local icon3 = iconArray[3]
        SetImageSync(item1,icon1,false)
        SetImageSync(item2,icon2,false)
        SetImageSync(item3,icon3,false)
    end
    self.toggles = {}
    table.insert(self.toggles,self.ui.m_tog1)
    table.insert(self.toggles,self.ui.m_tog2)
    table.insert(self.toggles,self.ui.m_tog3)
    local roleconfig = RoleSpineConfig:GetDataByID(config[1].spine)
    if roleconfig and roleconfig.img_spine then
        ResMgr:LoadAssetAsync(roleconfig.img_spine,AssetDefine.LoadType.Instant,function(target)
            self.ui.m_spui.skeletonDataAsset = target
            self.ui.m_spui:Initialize(true)
        end)
    end

    for k, v in ipairs(self.toggles) do
        local page = k - 1
        v.onValueChanged:AddListener(function (isOn)
            if isOn then
                self.tween = Tween.To(function(value)
                    self.ui.m_scrollview.horizontalNormalizedPosition = value
                    self.ui.m_scrollview.velocity.x = 0
                end,self.ui.m_scrollview.horizontalNormalizedPosition,page * 0.5,0.3)
            end
        end)
    end


    self.uiDrag = self.ui.m_scrollview:GetComponent(typeof(CS.UIDrag))
    self.page = 0
    local startPosition = 0
    self:SwitchPage(self.page)
    self.uiDrag.m_BeginDrag = function ()
        if self.tween then
            self.tween:Kill()
        end
        startPosition = self.ui.m_scrollview.horizontalNormalizedPosition
    end

    self.uiDrag.m_EndDrag = function ()
        local endPosition = self.ui.m_scrollview.horizontalNormalizedPosition
        if endPosition > startPosition then
            self:MoveNext()
        else
            self:MovePrev()
        end
    end

    self.ui.m_btnPrev.onClick:AddListener(function ()
        self:MovePrev()
    end)
    self.ui.m_btnNext.onClick:AddListener(function ()
        self:MoveNext()
    end)
end
function UI_SkiingShopHelp:MovePrev()
    local lastPage = self.page
    self.page = Mathf.Clamp(self.page - 1,0,2)
    if lastPage == self.page then
        return
    end
    self:SwitchPage(self.page)
end
function UI_SkiingShopHelp:MoveNext()
    local lastPage = self.page
    self.page = Mathf.Clamp(self.page + 1,0,2)
    if lastPage == self.page then
        return
    end
    if self.tween then
        self.tween:Kill()
    end
    self:SwitchPage(self.page)
end
function UI_SkiingShopHelp:SwitchPage(pageIndex)
    self.ui.m_txtDesc.text = LangMgr:GetLang(self.describes[pageIndex + 1])
    self.toggles[pageIndex + 1].isOn = true
end
function UI_SkiingShopHelp:OnCreate(param)

end

function UI_SkiingShopHelp:OnRefresh(param)

end

function UI_SkiingShopHelp:onDestroy()
    self.slider = nil
    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end
    self.uiDrag = nil
    self.toggles = nil
end

function UI_SkiingShopHelp:onUIEventClick(go,param)
    local name = go.name

end

return UI_SkiingShopHelp
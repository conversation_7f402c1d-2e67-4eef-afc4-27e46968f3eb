local UI_TradeWagonsShareConfirmModel = {}

UI_TradeWagonsShareConfirmModel.config = {["name"] = "UI_TradeWagonsShareConfirm", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_TradeWagonsShareConfirmModel:Init(c)
    c.ui = {}    
    c.ui.m_txtTitle = GetChild(c.uiGameObject,"bg/topBg/m_txtTitle",UEUI.Text)
    c.ui.m_btnClose = GetChild(c.uiGameObject,"bg/m_btnClose",UEUI.Button)
    c.ui.m_goShare = GetChild(c.uiGameObject,"bg/container/m_goShare")
    c.ui.m_txtChannel = GetChild(c.uiGameObject,"bg/container/m_goShare/m_txtChannel",UEUI.Text)
    c.ui.m_imgChannelIcon = GetChild(c.uiGameObject,"bg/container/m_goShare/m_txtChannel/m_imgChannelIcon",UEUI.Image)
    c.ui.m_txtChannelExplain = GetChild(c.uiGameObject,"bg/container/m_goShare/m_txtChannelExplain",UEUI.Text)
    c.ui.m_btnCancel = GetChild(c.uiGameObject,"bg/container/m_goShare/m_btnCancel",UEUI.Button)
    c.ui.m_txtCancel = GetChild(c.uiGameObject,"bg/container/m_goShare/m_btnCancel/m_txtCancel",UEUI.Text)
    c.ui.m_btnShare = GetChild(c.uiGameObject,"bg/container/m_goShare/m_btnShare",UEUI.Button)
    c.ui.m_txtShare = GetChild(c.uiGameObject,"bg/container/m_goShare/m_btnShare/m_txtShare",UEUI.Text)
    c.ui.m_goContent = GetChild(c.uiGameObject,"bg/container/m_goContent")
    c.ui.m_txtContent = GetChild(c.uiGameObject,"bg/container/m_goContent/m_txtContent",UEUI.Text)
    c.ui.m_btnConfirm = GetChild(c.uiGameObject,"bg/container/m_goContent/m_btnConfirm",UEUI.Button)
    c.ui.m_txtConfirm = GetChild(c.uiGameObject,"bg/container/m_goContent/m_btnConfirm/m_txtConfirm",UEUI.Text)
    c.ui.m_goContent2 = GetChild(c.uiGameObject,"bg/container/m_goContent2")
    c.ui.m_txtContent2 = GetChild(c.uiGameObject,"bg/container/m_goContent2/m_txtContent2",UEUI.Text)
    c.ui.m_btnConfirm2 = GetChild(c.uiGameObject,"bg/container/m_goContent2/m_btnConfirm2",UEUI.Button)
    c.ui.m_txtConfirm2 = GetChild(c.uiGameObject,"bg/container/m_goContent2/m_btnConfirm2/m_txtConfirm2",UEUI.Text)
    c.ui.m_btnCancel2 = GetChild(c.uiGameObject,"bg/container/m_goContent2/m_btnCancel2",UEUI.Button)
    c.ui.m_txtCancel2 = GetChild(c.uiGameObject,"bg/container/m_goContent2/m_btnCancel2/m_txtCancel2",UEUI.Text)
    c.ui.m_goContent3 = GetChild(c.uiGameObject,"bg/container/m_goContent3")
    c.ui.m_txtContent3 = GetChild(c.uiGameObject,"bg/container/m_goContent3/m_txtContent3",UEUI.Text)
    c.ui.m_btnConfirm3 = GetChild(c.uiGameObject,"bg/container/m_goContent3/m_btnConfirm3",UEUI.Button)
    c.ui.m_txtConfirm3 = GetChild(c.uiGameObject,"bg/container/m_goContent3/m_btnConfirm3/m_txtConfirm3",UEUI.Text)
    c.ui.m_btnCancel3 = GetChild(c.uiGameObject,"bg/container/m_goContent3/m_btnCancel3",UEUI.Button)
    c.ui.m_txtCancel3 = GetChild(c.uiGameObject,"bg/container/m_goContent3/m_btnCancel3/m_txtCancel3",UEUI.Text)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_TradeWagonsShareConfirmModel
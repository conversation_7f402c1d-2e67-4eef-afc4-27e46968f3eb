using UnityEngine;
using System.Collections;
using AssetBundles;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using UnityEngine.U2D;
using System;
using UnityEngine.Events;
using LitJson;

public class StartGame : Singleton<StartGame>
{
        public string GameQ1Id = "2059001";
        public AnimationCurves MonoCurves = null;
        public string StrVerApp = "0.0.0";
        public string StrVerRes = "0";

        private UIRoot m_UIRoot;
        public UIRoot UIRoot { get { return m_UIRoot; } }

        private GameObject m_Entity;
        public GameObject Entity { get { return m_Entity; } }

        public GameObject m_GoDialog = null;
        public GameObject m_GoLogin = null;
        
        private GameObject m_GoLogo = null;
        private GameLanuchState m_GoVer = null;

        private Button noticeBtn;
        public GameObject m_GoNotice = null;
        public string NoticeJsonStr = null;

        //public Vector2 Resolution = new Vector2(1920.0f, 1080.0f);
        public Vector2 Resolution = new Vector2(1080.0f, 1920.0f);

        public bool IsSDK = false;
        public bool IsClearAccount = false;
        public int UrlSelector = 0;//0 is test

        private string m_Token = null;
        private string m_Group = null;

        public bool IsLuaDebug = false;

        public bool IsUseNet = false;
        public bool IsUseTCPNet = true;

        public string m_UserId = "";
        public string m_SKDToken = "";
        public bool m_IsFirst = false;
        public string m_SwitchData = "";
        public bool isWechatModule = false;

        public void InitGame(AssetManager.ResourceMode mode, bool isLocalRes, bool isLocalConfig, bool isLocalScript, bool isSDK, int urlId,bool _isWechatModule)
        {
                IsSDK = isSDK;
                Debug.Log("is_sdk:" + isSDK);
                UrlSelector = urlId;
                isWechatModule = _isWechatModule;
                if (IsUseTCPNet)
                        NetworkEventMgr.Start();
                GameHelper.Init();
                HttpMono.Instance.Init();
                AssetManager.Instance.Init(mode, isLocalRes, isLocalConfig, isLocalScript);
                StorageManager.Instance.Init();
                SpriteAtlasMgr.Instance.Init();
                LanguageManager.Instance.Init();
                ThreadWorker.Instance.Init();
                
                if (m_UIRoot != null && (isWechatModule || GameHelper.IsWebGL()))
                {
                        m_UIRoot.AdaptWechat();
                }
                // InitUI();
                // OpenUISplashLogo();

                SDKLoginModule.Instance.ShowRequestView(m_UIRoot.LockLayer.gameObject,(a) => {

                    int coppa = PlayerPrefs.GetInt("Coppa_Key", 0);
                    if(coppa <= 0 && (GameQ1Id == "2059002" || GameQ1Id == "2059003"))
                    {
                        UnityAction tFunRight = new UnityAction(() =>
                        {
                            StartGame.Instance.CloseMsgBox();
                            OpenUILogin();
                            LoadNoticeJson(false);
                        });
                        UnityAction tFunLeft = new UnityAction(() =>
                        {
                            StartGame.Instance.CloseMsgBox();
                            OpenUILogin();
                            LoadNoticeJson(false);
                            //adjust
                        });
                        PlayerPrefs.SetInt("Coppa_Key", 1);
                        StartGame.Instance.ShowMsgBox(LanguageManager.Instance.GetLang(1036), 1019, 1018, tFunLeft, tFunRight);
                    }
                    else
                    {
                        OpenUILogin();
                        LoadNoticeJson(false);
                    }
                });
#if UNITY_EDITOR
                OpenUIDebug();
#endif
        }

        public void InitUI()
        {
                GameObject goEntity = GameObject.Find("Entity");
                GameObject.DontDestroyOnLoad(goEntity);
                m_Entity = goEntity;

                UIRoot prefab = Resources.Load<UIRoot>("Prefabs/UIRoot");
                UIRoot goRoot = GameObject.Instantiate(prefab);
                goRoot.InitRoot();
                goRoot.Root.SetParent(goEntity.transform);
                InitCanvasScaler(goRoot);
                m_UIRoot = goRoot;
                if(Application.systemLanguage == SystemLanguage.Japanese)
                {
                        OpenUISplashLogoJapan(); 
                }
                else{
                        OpenUISplashLogo();
                }
        }

        public void DoUpdate()
        {
                CloseUILogin();
                OpenUIVersionUpdate();
        }

        private void InitCanvasScaler(UIRoot rootGo)
        {
                float w = Resolution.x;
                float h = Resolution.y;
                float aspect = w / h;

                float device_width = Screen.width;
                float device_height = Screen.height;
                float device_aspect = device_width / device_height;


                bool isOnWidth = false;
                float changeX = w;
                float changeY = h;

                if (device_aspect < aspect)
                {
                        changeY = device_height / (device_width / w);
                        Resolution.y = changeY;
                        isOnWidth = true;
                }
                else if (device_aspect > aspect)
                {
                        changeX = device_width / (device_height / h);
                        Resolution.x = changeX;
                }

                foreach (CanvasScaler scaler in rootGo.UIScaler)
                {
                        if (isOnWidth)
                        {
                                scaler.matchWidthOrHeight = 0;
                        }
                        else
                        {
                                scaler.matchWidthOrHeight = 1;
                        }
                        scaler.referenceResolution = new Vector2(changeX, changeY);
                }
        }

        public void OpenUILogin()
        {
                GameObject prefab = Resources.Load<GameObject>("prefabs/UI_PageLogin");
                GameObject goLogin = GameObject.Instantiate(prefab);
                goLogin.transform.SetParent(m_UIRoot.TopLayer.transform, false);
                m_GoLogin = goLogin;
        }

        private void CloseUILogin()
        {
                if (m_GoLogin)
                {
                        GameObject.Destroy(m_GoLogin);
                        m_GoLogin = null;
                }
        }

        public void OpenUIVersionUpdate()
        {
                if (m_GoVer == null)
                {
                        GameLanuchState goVer = GameLanuchState.CreateUI();
                        goVer.transform.SetParent(m_UIRoot.TopLayer.transform, false);
                        m_GoVer = goVer;
                }
        }

        public void CloseUIVersionUpdate()
        {
                if (m_GoVer)
                {
                        GameObject.Destroy(m_GoVer.gameObject);
                        m_GoVer = null;
                }
        }

        public void OpenUISplashLogo()
        {
                if (m_GoLogo == null)
                {
                        GameObject goLogo = GameLanuchState.CreateUILogo();
                        goLogo.transform.SetParent(m_UIRoot.EffectLayer.transform, false);
                        goLogo.transform.SetAsFirstSibling();
                        m_GoLogo = goLogo;
                        Button m_Close = goLogo.transform.Find("FAQ").GetComponent<Button>();
                        Canvas canvas = m_Close.transform.GetComponent<Canvas>();
                        if (canvas)
                        {
                                canvas.sortingLayerName = "UI_TOP";
                        }
                        if (m_Close)
                        {
                            m_Close.onClick.AddListener(() =>
                           {
                               Application.OpenURL("https://www.facebook.com/mergetopia");
                           });
                        }

                        noticeBtn = goLogo.transform.Find("noticeBtn").GetComponent<Button>();
                        if (noticeBtn)
                        {
                            if (StartGame.Instance.NoticeJsonStr == "")
                                noticeBtn.gameObject.SetActive(false);

                            noticeBtn.onClick.AddListener(() =>
                            {
                                if (string.IsNullOrEmpty(StartGame.Instance.NoticeJsonStr))
                                {
                                    LoadNoticeJson(true);
                                }
                                else
                                {
                                    StartGame.Instance.OpenUINotice();
                                }
                            });
                        }
                }
        }
        public void OpenUISplashLogoJapan()
        {
                GameObject goLogo = GameLanuchState.CreateUIJapan();
                goLogo.transform.SetParent(m_UIRoot.TopLayer.transform, false);
                Canvas canvas = goLogo.transform.GetComponent<Canvas>();
                if (canvas)
                {
                        canvas.sortingLayerName = "UI_TOP";
                }
                GameObject.Destroy(goLogo.gameObject,2f);
        }

        public void CloseUISplashLogo()
        {
                if (m_GoLogo)
                {
                        GameObject.Destroy(m_GoLogo.gameObject);
                        m_GoLogo = null;
                        noticeBtn = null;
                }
        }

        public void OpenUIDebug()
        {
                GameObject goDebug = GameObject.Find("IngameDebugConsole");
                if (goDebug)
                {
                        Canvas canvas = goDebug.GetComponent<Canvas>();
                        if (canvas)
                        {
                                canvas.enabled = true;
                                System.Action<string, string, string> tFun = (string p0, string p1, string p2) =>
                                {
                                        string strExe = string.Format("DebugConsole(\"{0}\", \"{1}\", \"{2}\")", p0, p1, p2);
                                        LuaManager.Instance.CallLuaDoString(strExe);
                                };
                                IngameDebugConsole.DebugLogConsole.AddCommand("cheat", "set some functions", tFun);
                        }
                }
        }

        public void ShowMsgBox(string strDes, int langLeft, int langRight, UnityAction tFunLeft, UnityAction tFunRight)
        {
                if (m_GoDialog == null)
                {
                        GameObject prefab = Resources.Load<GameObject>("Prefabs/UI_Dialog");
                        m_GoDialog = GameObject.Instantiate(prefab);
                        m_GoDialog.transform.SetParent(m_UIRoot.TopLayer, false);
                }
                Transform transPa = m_GoDialog.transform;

                Button btnLeft = transPa.Find("horizontal/btn_left").GetComponent<Button>();
                Button btnRight = transPa.Find("horizontal/btn_right").GetComponent<Button>();
                Text txtBtnLeft = transPa.Find("horizontal/btn_left/Text").GetComponent<Text>();
                Text txtBtnRight = transPa.Find("horizontal/btn_right/Text").GetComponent<Text>();
                Text txtDes = transPa.Find("txtDes").GetComponent<Text>();
                Text txtTitle = transPa.Find("txtTitle").GetComponent<Text>();

                txtTitle.text = LanguageManager.Instance.GetLang(1022);
                txtDes.text = strDes;

                if (langLeft > 0)
                {
                        txtBtnLeft.text = LanguageManager.Instance.GetLang(langLeft);

                        if (tFunLeft != null)
                        {
                                btnLeft.onClick.RemoveAllListeners();
                                btnLeft.onClick.AddListener(tFunLeft);
                        }
                        btnLeft.gameObject.SetActive(true);
                }
                else
                {
                        btnLeft.gameObject.SetActive(false);
                }

                if (langRight > 0)
                {
                        txtBtnRight.text = LanguageManager.Instance.GetLang(langRight);
                        if (tFunRight != null)
                        {
                                btnRight.onClick.RemoveAllListeners();
                                btnRight.onClick.AddListener(tFunRight);
                        }
                        btnRight.gameObject.SetActive(true);
                }
                else
                {
                        btnRight.gameObject.SetActive(false);
                }
        }

        public void CloseMsgBox()
        {
                if (m_GoDialog == null)
                        return;
                GameObject.Destroy(m_GoDialog);
                m_GoDialog = null;
        }

    public void OpenUINotice()
    {
        if (m_GoNotice == null)
        {
            GameObject prefab = Resources.Load<GameObject>("prefabs/UI_GameNotice");
            GameObject goNotice = GameObject.Instantiate(prefab);
            goNotice.transform.SetParent(m_UIRoot.LockLayer.transform, false);
            m_GoNotice = goNotice;
        }
    }

    public void CloseUINotice()
    {
        if (m_GoNotice)
        {
            GameObject.Destroy(m_GoNotice);
            m_GoNotice = null;
        }
    }


        public void SetVer(string appVer, string resVer)
        {
                if (!string.IsNullOrEmpty(appVer))
                {
                        StrVerApp = appVer;
                        //ThingkingAnalyticsMgr.Instance.UpdateSuperProperties(ThinkingKey.versioncode, StrVerApp);

                }
                if (!string.IsNullOrEmpty(resVer))
                {
                        StrVerRes = resVer;
                        // ThingkingAnalyticsMgr.Instance.UpdateSuperProperties(ThinkingKey.hallcode, StrVerRes);
                }
        }
        public void SetUIVer(Text txt)
        {
                if (txt != null)
                {
                        txt.text = string.Format("{0}_{1}_{2}", StrVerApp, StrVerRes, GetGroup());
                }
        }
        public bool IsWechatGame()
        {
                return isWechatModule || GameHelper.IsWebGL();
        }
        public void SetGroup(string g)
        {
                m_Group = g;
        }

        public string GetGroup()
        {
                if (string.IsNullOrEmpty(m_Group))
                        return "1";
                return m_Group;
        }

        public void SetToken(string v)
        {
                m_Token = v;
        }

        public string GetToken()
        {
                return m_Token;
        }
        public void SetUserId(string v)
        {
                m_UserId = v;
        }

        public string GetUserId()
        {
                return m_UserId;
        }
        public void SetSDKToken(string v)
        {
                m_SKDToken = v;
        }

        public string GetSDKToken()
        {
                return m_SKDToken;
        }

        public void SetFirst(string v)
        {
                m_IsFirst = v.Equals("1");
        }

        public bool GetFirst()
        {
                return m_IsFirst;
        }

        public string GetServerSwitch()
        {
            return m_SwitchData;
    }

        public void SetServerSwitch(string data)
        {
            m_SwitchData = data;
        }

    public string GetFTPNoticePath()
    {
        return GameVersion.FTP_URL + "/" + Application.version + "/" + "notice.bytes";
    }
    private void LoadNoticeJson(bool isOpen)
    {
        if (StartGame.Instance.NoticeJsonStr != null)
            return;

        HttpMono.Instance.HTTPGet(GetFTPNoticePath(), (int error, long code, string text) =>
        {
            if (error != 0 || string.IsNullOrEmpty(text))
            {
                CloseNoticeBtn();
                return;
            }

            JsonData data = JsonMapper.ToObject(text);
            if (data == null || data.Count <= 0)
            {
                CloseNoticeBtn();
                return;
            }

            bool push = false;
            bool isShow = false;
            JsonData[] jdList = JsonMapper.ToObject<JsonData[]>(text);
            if (jdList != null)
            {
                for (int i = 0; i < jdList.Length; i++)
                {
                    data = jdList[i];
                    if (data.ContainsKey("id") && data.ContainsKey("push"))
                    {
                        if (data["id"].ToString() == "1" && data["push"].ToString() == "1")
                        {
                            push = true;
                        }
                    }
                    if (data.ContainsKey("is_show"))
                    {
                        if (data["is_show"].ToString() == "1")
                        {
                            isShow = true;
                        }
                    }
                    if (push == true && isShow == true)
                    {
                        break;
                    }
                }
            }

            if (!isShow)
            {
                CloseNoticeBtn();
            }
            else
            {
                StartGame.Instance.NoticeJsonStr = text;
                if (isOpen || push)
                {
                    StartGame.Instance.OpenUINotice();
                }
            }
        });
    }
    private void CloseNoticeBtn()
    {
        StartGame.Instance.NoticeJsonStr = "";
        if (noticeBtn != null)
            noticeBtn.gameObject.SetActive(false);
    }
}
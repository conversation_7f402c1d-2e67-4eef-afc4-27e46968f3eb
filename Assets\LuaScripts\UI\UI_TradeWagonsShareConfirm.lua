local UI_TradeWagonsShareConfirm = Class(BaseView)

function UI_TradeWagonsShareConfirm:OnInit()

end

----------------------------------------- 使用案例 -----------------------------------------
-- local params = {
--     title = 8121,
--     content = 8123,
--     confirmCallBack = function ()
--
--     end,
-- }
-- UI_SHOW(UIDefine.UI_TradeWagonsShareConfirm, 1, params)

---------------------------------------- 华丽的分隔线 ----------------------------------------

--- 创建界面
--- @param viewType number 视图类型
--- @param params table 参数列表
function UI_TradeWagonsShareConfirm:OnCreate(viewType, params)

    if IsTableEmpty(params) then
        return
    end

    self.params = params

    -- 全部隐藏
    SetActive(self.ui.m_goShare, false)
    SetActive(self.ui.m_goContent, false)
    SetActive(self.ui.m_goContent2, false)
    SetActive(self.ui.m_goContent3, false)

    -- 普通提示
    if viewType == TRADE_WAGONS_TIP_VIEW.NormalTip then
        SetActive(self.ui.m_goContent, true)

        self.ui.m_txtConfirm.text = LangMgr:GetLang(85)
    -- 分享到频道
    elseif viewType == TRADE_WAGONS_TIP_VIEW.ShareChannel then
        SetActive(self.ui.m_goShare, true)

        self.ui.m_txtShare.text = LangMgr:GetLang(40019004)
        self.ui.m_txtCancel.text = LangMgr:GetLang(86)

        self.ui.m_txtChannel.text = params.content
        self.ui.m_txtChannelExplain.text = LangMgr:GetLang(70000486)
        SetUIImage(self.ui.m_imgChannelIcon, params.icon, false)
    -- 确认提示
    elseif viewType == TRADE_WAGONS_TIP_VIEW.ConfirmTip then
        SetActive(self.ui.m_goContent2, true)

        self.ui.m_txtConfirm2.text = LangMgr:GetLang(85)
        self.ui.m_txtCancel2.text = LangMgr:GetLang(86)
    -- 确认提示
    elseif viewType == TRADE_WAGONS_TIP_VIEW.ConfirmTip2 then
        SetActive(self.ui.m_goContent3, true)

        self.ui.m_txtConfirm3.text = LangMgr:GetLang(85)
        self.ui.m_txtCancel3.text = LangMgr:GetLang(86)
    end

    -- 设置标题
    if type(params.title) == "number" then
        local titleLangID = params.title or 8121
        self.ui.m_txtTitle.text = LangMgr:GetLang(titleLangID)
    elseif type(params.title) == "string" then
        self.ui.m_txtTitle.text = params.title
    end

    -- 设置内容
    if type(params.content) == "number" then
        local contentLangID = params.content or 8121
        self.ui.m_txtContent.text = LangMgr:GetLang(contentLangID)
        self.ui.m_txtContent2.text = LangMgr:GetLang(contentLangID)
        self.ui.m_txtContent3.text = LangMgr:GetLang(contentLangID)
    elseif type(params.content) == "string" then
        self.ui.m_txtContent.text = params.content
        self.ui.m_txtContent2.text = params.content
        self.ui.m_txtContent3.text = params.content
    end
end

function UI_TradeWagonsShareConfirm:OnRefresh(param)

end

function UI_TradeWagonsShareConfirm:onDestroy()

end

function UI_TradeWagonsShareConfirm:onUIEventClick(go,param)
    local name = go.name
    -- 关闭按钮
    if name == "m_btnClose" then
        self:Close()
    -- 确定按钮
    elseif name == "m_btnConfirm" then
        self:OnClickConfirm()
    -- 分享按钮
    elseif name == "m_btnShare" then
        self:OnClickShare()
    -- 取消按钮
    elseif name == "m_btnCancel" then
        self:OnClickCancel()
    -- 确定按钮
    elseif name == "m_btnConfirm2" then
        self:OnClickConfirm2()
    -- 取消按钮
    elseif name == "m_btnCancel2" then
        self:OnClickCancel2()
    end
end

function UI_TradeWagonsShareConfirm:OnClickConfirm()
    local params = self.params
    if IsTableEmpty(params) then
        return
    end

    if params.confirmCallBack then
        params.confirmCallBack()
    end

    self:Close()
end

function UI_TradeWagonsShareConfirm:OnClickShare()
    local params = self.params
    if IsTableEmpty(params) then
        return
    end

    if params.shareCallBack then
        params.shareCallBack()
    end

    self:Close()
end

function UI_TradeWagonsShareConfirm:OnClickCancel()
    local params = self.params
    if IsTableEmpty(params) then
        return
    end

    self:Close()
end

function UI_TradeWagonsShareConfirm:OnClickConfirm2()
    local params = self.params
    if IsTableEmpty(params) then
        return
    end

    if params.confirmCallBack then
        params.confirmCallBack()
    end

    self:Close()
end

function UI_TradeWagonsShareConfirm:OnClickCancel2()
    local params = self.params
    if IsTableEmpty(params) then
        return
    end
    if params.closeCallBack then
        params.closeCallBack()
    end
    self:Close()
end

return UI_TradeWagonsShareConfirm
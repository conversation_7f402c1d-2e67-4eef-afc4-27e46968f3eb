local UI_DailyTarget = Class(BaseView)

local ItemBase = require("UI.Common.BaseSlideItem")
local DailyRankItem = Class(ItemBase)

local BubbleItem = require("UI.BubbleItem");

local ItemShowNums = 9
local LineItemNums = 3
function UI_DailyTarget:OnInit()
    self.isCheckClose = false;
    
    CreateCommonHead(GetChild(self.ui.m_goItemRank, "headNode",UE.Transform),0.4)
    self.myHeadNode = CreateCommonHead(GetChild(self.ui.m_goSelfRank, "bg/headNode",UE.Transform),0.5)
end

function UI_DailyTarget:OnCreate(param,isPush)
    self:SetIsUpdateTick(true)
    self.itemList = {}
    self.pathwayList = {}
    self.m_rewardCellList = {}
    self.rankSettlementSign = false  --结算标识
    self.itemConfig = DailyTargetManager:GetTargetRewardConfig()
    self.pathwayConfig = DailyTargetManager:GetTargetPathwayConfig()
    self.sliderTarget = self.ui.m_sliderDailyTarget
    self:LoadRewardItem()
    --self:LoadPathWayItem()
    self:refreshTargetProgress()
    self.showIndex = 0
    self.airIndex = 1
    self.isPush = isPush
    local defaltOpen = 1
    if param then
        defaltOpen = param.tabIndex
        self.rankKey = param.rankKey
    end
    
    local transform = self.uiGameObject.transform
    self.slider = require("UI.Common.SlideRect").new()
    self.slider:Init(transform:Find("root/m_goRankView/m_goRankScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)
    self.playerList = {}
    for i = 1, ItemShowNums do
        self.playerList[i] = DailyRankItem.new()
        self.playerList[i]:Init(UEGO.Instantiate(self.ui.m_goItemRank.transform))
    end
    self.slider:SetItems(self.playerList,10,Vector2.New(0,0))
    local rankPoint = DailyTargetManager:GetRankPoint() or 1
    local content = LangMgr:GetLangFormat(90210001,rankPoint)
    local contentStr = content:gsub("\\n", "\n")
    self.ui.m_txtNoRank.text = contentStr
    self:OnClickTab(defaltOpen)

    local todayItemID = DailyTargetManager:GetTodayObjItemId()
    SetImageSprite(self.ui.m_imgCurDailyIcon,ItemConfig:GetIcon(todayItemID),false)
    --local function onEventUISHOW(uiName, view)
    --    local is_guide = NetDailyTargetData:GetDataByKey("guide")
    --    if not is_guide then
    --        NetDailyTargetData:SetDataByKey("guide",true)
    --        self:FirstGuid()
    --    end
    --end
    --EventMgr:Add(EventID.UI_SHOWED, onEventUISHOW)

    self.bubbleItem = BubbleItem.new("UI_DailyTarget");
    self.bubbleItem:Init(self.uiGameObject, {x = -480, y = 410}, function()
        self:Close();
    end);

    -- 检查商店刷新
    NetDailyTargetData:RefreshWeekZero();

    SetUIForceRebuildLayout(self.ui.m_goResource);
    local pos = UIMgr:GetUIPosByWorld(self.ui.m_doMedal.transform.position);
    MapController:SetUIResourcePos(ItemID.MEIRIXUNZHANG, pos.x, pos.y);
    self.ui.m_txtMedal.text = NetUpdatePlayerData:GetResourceNumByID(ItemID.MEIRIXUNZHANG);

    EventMgr:Add(EventID.CHANGE_RESOURCE, self.ChangeResource, self);
end

function UI_DailyTarget:FirstGuid()
    UI_SHOW(UIDefine.UI_GuideMask, {
        {3,280,160},
        {-720,-50},
        {1,1},
        0.5,
        function()
            self:OnClickTab(2)
            UI_CLOSE(UIDefine.UI_GuideMask)
        end,
        {-7,1},
        {0,-3,20010},
        "Sprite/new_hero/headFrame_1.png"
    })
end

function UI_DailyTarget:OnRefresh(param)
    if param == 1 then
        --结算奖励
        SetActive(self.ui.m_goTarget,true)
        self:FlyMidToAir()
    elseif param == 2 then
        self:RefresShop();
    end
end

function UI_DailyTarget:LoadRewardItem()
    if not self.itemConfig then
        return
    end
 

    local itemCount  = table.count(self.itemConfig)
    for i, v in ipairs(self.itemConfig) do
        local go = UEGO.Instantiate(self.ui.m_goDailyTargetItem)
        if go then
            AddChild(self.ui.m_panelContent,go)
            local item = self:SetItem(go,v)
            table.insert(self.itemList,item)
        end
    end
    
    self:refreshItemList()
end

function UI_DailyTarget:SetItem(go,config)
    local item = {}
    item.go  = go
    item.config  = config
    item.bg_light  = GetChild(go, "Bg/bg_light", UEUI.Image)
    item.itemIcon  = GetChild(go, "Bg/light/itemIcon", UEUI.Image)
    item.btnItemIcon  = GetChild(go, "Bg/light/itemIcon", UEUI.Button)
    item.count  = GetChild(go, "Bg/count", UEUI.Text)
    item.stage  = GetChild(go, "Bg/stage", UEUI.Text)
    item.stage_light  = GetChild(go, "Bg/bg_light/stage_light", UEUI.Text)
    item.bg_get  = GetChild(go, "Bg/bg_get", UEUI.Image)
    item.txt_get  = GetChild(go, "Bg/bg_get/txt_get", UEUI.Text)
    item.txt_score  = GetChild(go, "Bg/bg_score/txt_score", UEUI.Text)
    item.bg_score_light  = GetChild(go, "Bg/bg_score/bg_score_light", UEUI.Image)
    item.scoreIcon  = GetChild(go, "Bg/bg_score/scoreIcon", UEUI.Image)
    item.count_outline = GetChild(go,"Bg/count", UEUI.Outline)
    item.txt_score_outline = GetChild(go,"Bg/bg_score/txt_score", UEUI.Outline)
    item.rare  = GetChild(go, "Bg/rare", UEUI.Image)
    item.rare_light  = GetChild(go, "Bg/bg_light/rare_light", UEUI.Image)
    return item
end

function UI_DailyTarget:refreshItemList()
    if IsTableEmpty(self.itemList) then
        return
    end
    local playerLevel = NetDailyTargetData:GetDataByKey("playerLevel")
    local lastRewardID = DailyTargetManager:GetNotGetTargetRewardID()
    local total = 0
    local moveIndex = 0
    for i, item in ipairs(self.itemList) do
        local config = item.config
        local rewardConfig = NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter) and config.reward_1 or config.reward
        if playerLevel >= 9 then
            rewardConfig = NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter) and config.reward_2 or config.reward_lv9
        end
        local itemScale = v2n(config.scale)
        item.go.transform:SetLocalScale(itemScale,itemScale,itemScale)
        local rewardArr = Split1(rewardConfig,"|")
        local rewardId = v2n(NetSeasonActivity:GetChangeItemId(v2n(rewardArr[1])))
        local rewardNum = v2n(rewardArr[2])
        SetImageSprite(item.itemIcon,ItemConfig:GetIcon(rewardId),false)
        local todayItemID = DailyTargetManager:GetTodayObjItemId()
        SetImageSprite(item.scoreIcon,ItemConfig:GetIcon(todayItemID),false)
        item.count.text = "x"..NumToGameString(rewardNum)
        local state = NetDailyTargetData:GetRewardState(config.id)
        SetActive(item.bg_get,state == DailyTargetRewardState.REWARDED)
        item.txt_get.text = LangMgr:GetLang(9010)
        total = config.need_point--total + config.need_point
        item.txt_score.text = total
        item.stage.text = config.id
        item.stage_light.text = config.id

        SetActive(item.rare,config.is_rare == 1)
        SetActive(item.rare_light,config.is_rare == 1)
        local curStage = NetDailyTargetData:GetDataByKey("curStage")
        if curStage == config.id then    --config.is_rare == 1
            SetActive(item.bg_light,true)
            SetActive(item.bg_score_light,true)
            SetActive(item.stage,false)
            --item.count_outline.effectColor = Color.New(194/255, 25/255, 219/255, 1)
            --item.txt_score_outline.effectColor = Color.New(194/255, 25/255, 219/255, 1)
            item.txt_score.color = Color.HexToRGB("df761f")
            UnifyOutline(item.count.gameObject, "a44b00")
            
        else
            SetActive(item.bg_light,false)
            SetActive(item.bg_score_light,false)
            SetActive(item.stage,true)
            --item.count_outline.effectColor = Color.New(0/255, 110/255, 204/255, 1)
            --item.txt_score_outline.effectColor = Color.New(0/255, 110/255, 204/255, 1)
            item.txt_score.color = Color.HexToRGB("906EEE")
            UnifyOutline(item.count.gameObject, "8730D5")
        end
        if lastRewardID == config.id then
            moveIndex = i
        end
        RemoveUIComponentEventCallback(item.btnItemIcon,UEUI.Button)
        AddUIComponentEventCallback(item.btnItemIcon,UEUI.Button,function (go,param)
            self:onUIEventClick(go,rewardId)
        end)
    end
    self.ui.m_scrollview.verticalNormalizedPosition = 1
    if moveIndex then
        local item = self.itemList[moveIndex]
        local moveItem = item.go:GetComponent(typeof(UE.RectTransform))
        local content = self.ui.m_scrollview.content
        local viewport = self.ui.m_scrollview.viewport
        UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(content)
        local movePosition = (moveItem.rect.height) / (content.rect.height - viewport.rect.height)
        movePosition = Mathf.Clamp01((moveIndex - 1) * movePosition) 
        self.tween = Tween.To(function (value)
            self.ui.m_scrollview.verticalNormalizedPosition = 1 - value
        end,0,movePosition,0.5):SetDelay(0.3)
    end
end

function UI_DailyTarget:refreshTargetProgress()
    local curStage = NetDailyTargetData:GetDataByKey("curStage")
    local curScore = NetDailyTargetData:GetDataByKey("targetScore")
    curScore = math.floor(curScore)
    local nextStage = curStage + 1
    local isMaxStage = DailyTargetManager:GetIsMaxStage(nextStage)
    local curStageMaxScore = DailyTargetManager:GetStageTargetNeedScore(curStage)
    if curStageMaxScore then
        self.ui.m_txtTargetProgress.text = curScore .. "/" .. curStageMaxScore
    end
    local stageRewardId,num = DailyTargetManager:GetStageTargetReward(curStage)
    if stageRewardId then
        SetImageSprite(self.ui.m_imgDailyTargetIcon,ItemConfig:GetIcon(stageRewardId),false)
        self.ui.m_txtTarget.text = "X" .. num
    end
    if curStageMaxScore then
        self.ui.m_txtTargetProgress.text = curScore .. "/" .. curStageMaxScore
        self.sliderTarget.value = curScore / curStageMaxScore
    end
    if isMaxStage and curScore > curStageMaxScore then
        self.sliderTarget.value = 1
        self.ui.m_txtTargetProgress.text = LangMgr:GetLang(5003)
    end
end

function UI_DailyTarget:LoadPathWayItem()
    if not self.pathwayConfig then
        return
    end
    local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_TargetPathwayItem")
    local itemListRes = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)

    for i, v in ipairs(self.pathwayConfig) do
        local go = UEGO.Instantiate(itemListRes)
        if itemListRes then
            AddChild(self.ui.m_panelPathwayContent,go)
            local item = self:SetPathwayItem(go,v)
            table.insert(self.pathwayList,item)
        end
    end

    self:refreshPathwayist()
end

function UI_DailyTarget:SetPathwayItem(go,config)
    local item = {}
    item.go  = go
    item.config  = config
    item.icon  = GetChild(go, "Bg/icon", UEUI.Image)
    item.name  = GetChild(go, "Bg/name", UEUI.Text)
    item.content  = GetChild(go, "Bg/content", UEUI.Text)

    return item
end

function UI_DailyTarget:refreshPathwayist()
    if IsTableEmpty(self.pathwayList) then
        return
    end
    for i, item in ipairs(self.pathwayList) do
        local config = item.config
        SetImageSprite(item.icon,config.icon,false)
        item.name.text = LangMgr:GetLang(config.way_name)
        item.content.text = LangMgr:GetLang(config.way_describe)
    end
end

function UI_DailyTarget:TickUI(delta)
    local isOpen = DailyTargetManager:IsCanShowDailyTarget()
    if not isOpen then
        return
    end
    local endTime = TimeZoneMgr:GetServerClockStampByNextDay()
    local nowTime = TimeMgr:GetServerTime()
    local time = endTime - nowTime
    if time < 0 then
        self:refreshItemList()
        self:refreshPathwayist()
    else
        self.ui.m_txtTime.text = TimeMgr:CheckHMSNotEmpty(time)
    end
end

function UI_DailyTarget:onDestroy()
    EventMgr:Remove(EventID.CHANGE_RESOURCE, self.ChangeResource, self);
    self.myHeadNode = nil
    -- 排行榜滑动动画
    Tween.Kill("AutoMoveFunc")
    self.itemList = nil
    self.pathwayList = nil
    self.slider = nil
    for i=1,#self.playerList do
        self.playerList[i]:onDestroy()
    end
    self.playerList = {}
    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end   
    if self.numberTween then
        self.numberTween:Kill()
        self.numberTween = nil
    end
    if not IsTableEmpty(self.m_rewardCellList) then
        for i, v in ipairs(self.m_rewardCellList) do
            if v.obj then
                UEGO.Destroy(v.obj)
            end
        end
        self.m_rewardCellList = {}
    end
    if self.isPush then
        if self.rankKey and self.myselfData then
            self.rankKey = nil
            local myRank = v2n(self.myselfData.ranking)
            local rankConfig = DailyTargetManager:GetRankRewardConfig(myRank)
            if rankConfig then
                local rankReward = rankConfig.reward
                local level = LevelConfig:GetLevel()
                local upLevel = 9
                if level >= upLevel then
                    rankReward = rankConfig.reward_1
                end

                local rewardList = string.split(rankReward, ";");
                local rewardStr;
                local arr;
                local itemId;
                for i = 1, #rewardList do
                    arr = string.split(rewardList[i], "|");
                    itemId = v2n(arr[1]);
                    if itemId < ItemID._RESOURCE_MAX then
                        NetUpdatePlayerData:AddResource(PlayerDefine[itemId], v2n(arr[2]), nil, nil, "UI_DailyTarget");
                    else
                        if rewardStr == nil then
                            rewardStr = arr[1] .. "|" .. arr[2];
                        else
                            rewardStr = rewardStr .. ";" .. arr[1] .. "|" .. arr[2];
                        end
                    end
                end
                NetGlobalData:GetRewardToMap(rewardStr,"UI_DailyTarget")
            end
        end
        self.isPush = nil
        NetPushViewData:RemoveAllByIndex(PushDefine.UI_DailyTargetRank)
        NetPushViewData:CheckOtherView(true)
    end

    if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
        self.isCheckClose = true;
        self.bubbleItem:SetCloseCall(nil);
        self.bubbleItem:CloseImmediately();
    end
end

function UI_DailyTarget:CheckClose()
    if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
        self.isCheckClose = true;
        self.bubbleItem:Close();
    else
        self.isCheckClose = true;
        self:Close();
    end
end

function UI_DailyTarget:onUIEventClick(go,param)
    local name = go.name
    -- 排行榜滑动动画
    Tween.Kill("AutoMoveFunc")
    if name == "m_btnClose" then
        self:CheckClose()
    elseif name == "m_btnTabScoreReward" then
        self:OnClickTab(1)
    elseif name == "m_btnTabScoreGet" then
        self:OnClickTab(2)
    elseif name == "m_btnTips" then
        UI_SHOW(UIDefine.UI_DailyTargetHelp)
    elseif name == "itemIcon" then
        local itemId = param
        if ItemConfig:GetTypeUse(itemId) == ItemUseType.ObjItemChooseBox then
            UI_SHOW(UIDefine.UI_ItemChooseBox,{m_Id = itemId},true)
        else
            UI_SHOW(UIDefine.UI_ItemTips,itemId)
        end
    elseif name == "m_btnTabRank" then
        self:OnClickTab(3)
    elseif name == "m_imgCurDailyIcon" then
        local itemId = DailyTargetManager:GetTodayObjItemId()
        UI_SHOW(UIDefine.UI_ItemTips,itemId)
    elseif name == "m_imgDailyTargetIcon" then
        local curStage = NetDailyTargetData:GetDataByKey("curStage")
        local stageRewardId = DailyTargetManager:GetStageTargetReward(curStage)
        if stageRewardId then
            UI_SHOW(UIDefine.UI_ItemTips,stageRewardId)
        end
    elseif name == "m_btnTabShop" then
        self:OnClickTab(4);
    end
end

function UI_DailyTarget:OnClickTab(index)
    if self.showIndex == index then
        return
    end
    if self.showIndex ~= index then
        self.showIndex = index
    end
    SetActive(self.ui.m_imgScoreRewardLight,index == 1)
    SetActive(self.ui.m_imgScoreGetLight,index == 2)
    SetActive(self.ui.m_imgRankLight,index == 3)
    SetActive(self.ui.m_goRewardView,index == 1)
    SetActive(self.ui.m_goPathwayView,index == 2)
    SetActive(self.ui.m_goRankView,false)
    SetActive(self.ui.m_imgShopLight, index == 4);
    SetActive(self.ui.m_goShopView, index == 4);
    SetActive(self.ui.m_goClock, index ~= 4);

    if index == 3 then
        self:OpenRank()
    else
        self:CheckCloseRank()
    end

    if index == 4 then
        self:OpenShop();
    end
end

-- 如果是push结算排行榜的话，切换页签就结算发奖励
function UI_DailyTarget:CheckCloseRank()
    if self.rankKey then
        if self.myselfData then
            local ranking = v2n(self.myselfData.ranking)  -- ranking 是活动结束后的结算排名
            if ranking and ranking > 0 then
                local myRank = v2n(self.myselfData.ranking)
                local rankConfig = DailyTargetManager:GetRankRewardConfig(myRank)
                if rankConfig then
                    local rankReward = rankConfig.reward
                    local level = LevelConfig:GetLevel()
                    local upLevel = 9
                    if level >= upLevel then
                        rankReward = rankConfig.reward_1
                    end

                    local rewardList = string.split(rankReward, ";");
                    local rewardStr;
                    local arr;
                    local itemId;
                    for i = 1, #rewardList do
                        arr = string.split(rewardList[i], "|");
                        itemId = v2n(arr[1]);
                        if itemId < ItemID._RESOURCE_MAX then
                            NetUpdatePlayerData:AddResource(PlayerDefine[itemId], v2n(arr[2]), nil, nil, "UI_DailyTarget");
                        else
                            if rewardStr == nil then
                                rewardStr = arr[1] .. "|" .. arr[2];
                            else
                                rewardStr = rewardStr .. ";" .. arr[1] .. "|" .. arr[2];
                            end
                        end
                    end
                    NetGlobalData:GetRewardToMap(rewardStr,"UI_DailyTarget")
                end
                SetActive(self.ui.m_goTarget,false)
                self.rankKey = nil
            end
        end
    end
end

--- 显示排行榜
function UI_DailyTarget:OpenRank()
    SetActive(self.ui.m_goRankScroll,false)
    
    local function rankCallBack(data)
        if self.ui then
            SetActive(self.ui.m_goRankScroll,true)
            self:RefreshRank(data)
        end
    end
    NetDailyTargetData:RequestRankData(rankCallBack,self.rankKey)
end

function UI_DailyTarget:RefreshRank(serverData)
    self.playerListData = serverData.ranking
    self.myselfData = serverData.player       -- 玩家自身数据
    SetActive(self.ui.m_goRankView,true)
    local todayItemID = DailyTargetManager:GetTodayObjItemId()
    SetImageSprite(self.ui.m_imgSelfScoreIcon,ItemConfig:GetIcon(todayItemID),false)
    if IsTableEmpty(self.playerListData) then
        SetActive(self.ui.m_goNoRank,true)
        SetActive(self.ui.m_goRankScroll,false)
        SetActive(self.ui.m_goSelfRank,false)
    else
        SetActive(self.ui.m_goNoRank,false)
        SetActive(self.ui.m_goRankScroll,true)
        SetActive(self.ui.m_goSelfRank,true)
        self.slider:SetData(self.playerListData,self.myselfData.rank + 20)
        self.slider:MoveToIndex(self.myselfData.rank,2)
        self:CloseRank()
        self:SetMyself()
    end
end

function UI_DailyTarget:CloseRank()
    local ranking = v2n(self.myselfData.ranking)  -- ranking 是活动结束后的结算排名
    if ranking and ranking > 0 then
        local myRank = v2n(self.myselfData.ranking)
        local rankConfig = DailyTargetManager:GetRankRewardConfig(myRank)
        local rankReward = rankConfig.reward
        local level = LevelConfig:GetLevel()
        local upLevel = 9
        if level >= upLevel then
            rankReward = rankConfig.reward_1
        end
        local rewardT = {}
        rankReward = string.split(rankReward,";")
        for k, v in pairs(rankReward) do
            local t = {}
            local arr = string.split(v,"|")
            t[1] = v2n(arr[1])
            t[2] = v2n(arr[2])
            table.insert(rewardT,t)
        end
        UI_SHOW(UIDefine.UI_ItemRankFinishReward, rewardT,7)
    end
end

function UI_DailyTarget:SetMyself()
    local rank = v2n(self.myselfData.rank)
    if self.isPush and self.rankKey then
        rank = v2n(self.myselfData.ranking)
    end
    if rank <= 3 then
        local rankSprite
        if rank == 1 then
            rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number1.png"
        elseif rank == 2 then
            rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number2.png"
        elseif rank == 3 then
            rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number3.png"
        end
        if rankSprite then
            SetUIImage(self.ui.m_imgSelfRank,rankSprite,false)
            SetActive(self.ui.m_imgSelfRank.gameObject,true)
        end
    else
        SetActive(self.ui.m_imgSelfRank.gameObject,false)
    end
    self.ui.m_txtSelfrank.text = rank
    self.ui.m_txtSelfScore.text = self.myselfData.point

    local rankConfig = DailyTargetManager:GetRankRewardConfig(rank)
    local rankReward = rankConfig.reward
    local level = LevelConfig:GetLevel()
    local upLevel = 9
    if level >= upLevel then
        rankReward = rankConfig.reward_1
    end
    local rewardT = string.split(rankReward,";")
    local index = table.count(rewardT)
    
    for i, v in ipairs(rewardT) do
        if self.m_rewardCellList[i] == nil then
            self.m_rewardCellList[i] = {}
            local obj =	UEGO.Instantiate(self.ui.m_goReward,self.ui.m_goRewardList.transform)
            self.m_rewardCellList[i].obj = obj
            local icon = GetChild(obj,"icon",UEUI.Image)
            local num = GetChild(obj,"num",UEUI.Text)
            self.m_rewardCellList[i].icon = icon
            self.m_rewardCellList[i].num = num
        end
    end

    for k, v in pairs(self.m_rewardCellList) do
        if index >= k then
            local config = rewardT[k]
            local arr = string.split(config,"|")
            local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
            local num = v2n(arr[2])
            SetActive(v.obj,true)

            SetUIImage(v.icon, ItemConfig:GetIcon(itemId), false)
            v.num.text = "x"..num

            local btn = v.icon.gameObject.transform:GetComponent(typeof(UEUI.Button))
            RemoveUIComponentEventCallback(btn,UEUI.Button)
            AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
                if itemId > 0  then
                    UI_SHOW(UIDefine.UI_ItemTips,itemId)
                end
            end)
        else
            SetActive(v.obj,false)
        end
    end

    --local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --if headConfig then
    --    SetUIImage(self.ui.m_imgSelfHead, headConfig.icon, false)
    --end
    
    SetMyHeadAndBorderByGo(self.myHeadNode)
    self.myHeadNode.transform.localPosition = Vector3.zero
    
    self.ui.m_txtSelfName.text = NetUpdatePlayerData:GetPlayerInfo().name
end

function UI_DailyTarget:FlyMidToAir(callBack)
    --self.myselfData.ranking
    if nil == self.myselfData.ranking then
        return
    end
    local myRank = v2n(self.myselfData.ranking)
    local rankConfig = DailyTargetManager:GetRankRewardConfig(myRank)
    local rankReward = rankConfig.reward
    local level = LevelConfig:GetLevel()
    local upLevel = 9
    if level >= upLevel then
        rankReward = rankConfig.reward_1
    end
    local strArr = string.split(rankReward,";")
    for k, v in pairs(strArr) do
        local arr = string.split(v,"|")
        local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
        local num = v2n(arr[2])

        if itemId < ItemID._RESOURCE_MAX then
            -- NetUpdatePlayerData:AddResource(PlayerDefine[itemId], num, nil, nil, "UI_DailyTarget");
            MapController:AddResourceBoomAnim(0, 0, itemId, num);

            if itemId == ItemID.MEIRIXUNZHANG then
                local count = NetUpdatePlayerData:GetResourceNumByID(ItemID.MEIRIXUNZHANG);
                self:ChangeResource(PlayerDefine.Meirixunzhang, count + num, num);
            end
        else
            if self.airIndex <= 5 then
                local rewardGo      = GetChild(self.ui.m_goPanel,"pos" .. self.airIndex ,UEUI.Image)
                self.airIndex = self.airIndex + 1

                SetActive(rewardGo,true)
                SetImageSprite(rewardGo,ItemConfig:GetIcon(itemId),false)
            end
            
            local assetPath     = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "COM_FlyItem")
 
            local function loadCallBack(asset)
                local go = UEGO.Instantiate(asset)
                if go then
                    local goIcon = GetComponent(go,UEUI.Image)
                    go.transform:SetParent(self.ui.m_goMidPlant.transform)
                    go.transform.localPosition = Vector3(0,0,0)
                    go.transform.localScale = Vector3.New(1, 1, 1)
                    SetImageSprite(goIcon,ItemConfig:GetIcon(itemId),false)
                    SetUISize(go,100,100)
                    go.transform:SetParent(self.ui.m_goPanel.transform)
                    DOLocalMove(go.transform,Vector3(0,0,0),k*0.4,function ()
                        UEGO.Destroy(go)
                        if callBack then
                            callBack()
                        end
                        callBack = nil
                    end)
                end
            end
            ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,loadCallBack)
        end
    end
end

function UI_DailyTarget:OpenShop()
    self.shopList = {};
    local config = ConfigMgr:GetData(ConfigDefine.ID.activate_shop);
    local curLv = NetUpdatePlayerData:GetLevel();
    local level;
    for i = 1, #config do
        --level = config[i].open_level;
        --if not level or curLv >= level then
        --    table.insert(self.shopList, config[i]);
        --end
        local isOpen = DailyTargetManager:CheckShopItemIsOpen(config[i])
        if isOpen then
            table.insert(self.shopList, config[i])
        end
    end

    local count = #self.shopList;
    self.ui.m_TableViewVShop.GetItemCount = function(num)
        local dataCount = count
        local lineCount = Mathf.Ceil(dataCount / LineItemNums)
        return lineCount
    end

    self.ui.m_TableViewVShop.GetItemGo = function(obj)
        return self.ui.m_goShopItemLine;
    end

    self.ui.m_TableViewVShop.UpdateItemCell = function(idx, obj)
        self:LoadLineCellData(idx, obj)
    end

    self.ui.m_TableViewVShop:InitTableViewByIndex(0);
end

function UI_DailyTarget:LoadLineCellData(idx, obj)
    local count = #self.shopList;
    for i = 1, LineItemNums do
        local dataIndex = idx * LineItemNums + i
        local itemTrans = GetChild(obj, "shopItemList", UE.RectTransform)
        local lineItemObj
        if itemTrans then
            if itemTrans.transform.childCount < i then
                lineItemObj = UEGO.Instantiate(self.ui.m_goShopItem.transform,itemTrans)
            else
                lineItemObj = itemTrans.transform:GetChild(i - 1)
            end
        end
        if lineItemObj then
            if dataIndex <= count then
                SetActive(lineItemObj,true)
                self:LoadShopCellData(dataIndex,lineItemObj)
            else
                SetActive(lineItemObj,false)
            end
        end
    end
end

function UI_DailyTarget:LoadShopCellData(idx, obj)
    local bg = GetChild(obj, "bg", UEUI.Image);
    local nameTxt = GetChild(obj, "nameTxt", UEUI.Text);
    local remainTxt = GetChild(obj, "remainTxt", UEUI.Text);
    local tipBtn = GetChild(obj, "tipBtn");
    local icon = GetChild(obj, "icon", UEUI.Image);
    local numTxt = GetChild(obj, "numTxt", UEUI.Text);
    local buyBtn = GetChild(obj, "buyBtn");
    local buyIcon = GetChild(obj, "buyBtn/buyIcon", UEUI.Image);
    local buyTxt = GetChild(obj, "buyBtn/buyTxt", UEUI.Text);
    local soldOutBg = GetChild(obj, "soldOutBg");
    local isHaveBg = GetChild(obj, "isHaveBg");

    SetActive(isHaveBg,false)
    local data = self.shopList[idx];
    local itemDic = string.split(data.item, "|");
    local itemId = NetSeasonActivity:GetChangeItemId(itemDic[1]);
    local itemCfg = ItemConfig:GetDataByID(v2n(itemId));
    nameTxt.text = LangMgr:GetLang(itemCfg.id_lang);
    numTxt.text = "x" .. itemDic[2];
    SetUIImage(icon, itemCfg.icon_b, false);

    if data.style == 1 then
        UnifyOutline(nameTxt.gameObject, "2b721e");
    elseif data.style == 2 then
        UnifyOutline(nameTxt.gameObject, "cc2e2e");
    elseif data.style == 3 then
        UnifyOutline(nameTxt.gameObject, "ae4a00");
    end
    SetUIImage(bg, "Sprite/ui_meirimubiao/meirimubiao_win3_dikuang" .. data.style .. ".png", false);

    local remainNum = data.limit_num - NetDailyTargetData:GetShopBuyCout(data.id);
    remainTxt.text = string.format(LangMgr:GetLang(84), remainNum);

    local costDic = string.split(data.cost, "|");
    local costId = v2n(costDic[1]);
    local price = v2n(costDic[2]);
    buyTxt.text = price;
    SetUIImage(buyIcon, ItemConfig:GetIcon(costId), false);

    RemoveUIComponentEventCallback(tipBtn, UEUI.Button)
    AddUIComponentEventCallback(tipBtn, UEUI.Button, function(arg1, arg2)
        if itemCfg.type_use == ItemUseType.ObjItemChooseBox then
            UI_SHOW(UIDefine.UI_ItemChooseBox, {m_Id = itemCfg.id}, true);
        elseif itemCfg.type_name == 5 then
            if ItemConfig:IsShowUIBoxProbShow(itemCfg.id) then
                UI_SHOW(UIDefine.UI_BoxProbShow, itemCfg)
            else
                UI_SHOW(UIDefine.UI_MarketBox, itemCfg)
            end 
        else
            UI_SHOW(UIDefine.UI_ItemTips, itemCfg.id)
        end
    end);

    RemoveUIComponentEventCallback(buyBtn, UEUI.Button)
    AddUIComponentEventCallback(buyBtn, UEUI.Button, function(arg1, arg2)
        local buyCount = NetDailyTargetData:GetShopBuyCout(data.id);
        if data.limit_num > buyCount then
            local money = NetUpdatePlayerData:GetResourceNumByID(costId);
            if money >= price then
                NetDailyTargetData:SetShopBuyCout(data.id, buyCount + 1);
                NetUpdatePlayerData:AddResource(PlayerDefine[costId], -price, nil, nil, "UI_DailyTarget");

                self.bubbleItem:FlyItem(v2n(itemId), v2n(itemDic[2]), icon.transform.position);
                self:LoadShopCellData(idx, obj);
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(5016));
            end
        end
    end);

    local itemType = ItemConfig:GetTypeUse(itemId)
    local idUse = ItemConfig:GetIDUse(v2n(itemId))
    local isFind = NetHeadSeasonVip:FindVipHead(idUse)
    
    SetActive(numTxt, remainNum > 0);
    SetActive(soldOutBg, remainNum <= 0);
    
    if itemType == ItemUseType.ObjItemHeadBorder or itemType == ItemUseType.ObjItemChatBorder then
        SetActive(buyBtn, remainNum > 0 and not isFind);
        SetActive(isHaveBg,isFind)
    else
        SetActive(buyBtn, remainNum > 0);
    end
end

function UI_DailyTarget:RefresShop()
    if self.showIndex == 4 then
        self.ui.m_TableViewHShop:ReloadData();
    end
end

function UI_DailyTarget:ChangeResource(type, num, changeValue, isTop)
    if type == PlayerDefine.Meirixunzhang then
        self.ui.m_doMedal:DORestart();
        if self.numberTween then
            self.numberTween:Kill()
        end
        self.numberTween = AddDOTweenNumberDelay(num - changeValue, num, 0.6, 0.8, function(value)
            self.ui.m_txtMedal.text = math.floor(value);
        end)
    end
end






------------- DailyRankItem -----------------------------
function DailyRankItem:OnInit(transform)
    local obj = transform
    self.trans				= transform
    self.bgImg				= GetChild(obj, "bg", UEUI.Image)
    self.img_rank			= GetChild(obj, "img_rank", UEUI.Image)
    self.scoreBg			= GetChild(obj, "scoreBg", UEUI.Image)
    self.img_head			= GetChild(obj, "head/img_head", UEUI.Image)
    self.img_icon			= GetChild(obj, "img_icon", UEUI.Image)
    self.txt_name			= GetChild(obj, "txt_name", UEUI.Text)
    self.txt_score			= GetChild(obj, "txt_score", UEUI.Text)
    self.txt_rank			= GetChild(obj, "txt_rank", UEUI.Text)

    self.rewardCellList = {}
    self.rewardGo = GetChild(obj, "goReward", UE.RectTransform)
    self.rewardListRoot = GetChild(obj, "rewardList/Viewport/Content")
    self.customHeadObj      = GetChild(obj,"headNode/CustomHead")
end

function DailyRankItem:UpdateData(data,index)
    if not data then return end

    if data.rank <= 3 then
        local rankSprite
        if data.rank == 1 then
            rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number1.png"
        elseif data.rank == 2 then
            rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number2.png"
        elseif data.rank == 3 then
            rankSprite = "Sprite/ui_huodongjingsai_1V1duijue/duijue_paihang_number3.png"
        end
        if rankSprite then
            SetUIImage(self.img_rank,rankSprite,false)
            SetActive(self.img_rank.gameObject,true)
        end
    else
        SetActive(self.img_rank.gameObject,false)
    end
    local todayItemID = DailyTargetManager:GetTodayObjItemId()
    SetImageSprite(self.img_icon,ItemConfig:GetIcon(todayItemID),false)
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, data.icon)
    --if headConfig then
    --    SetUIImage(self.img_head, headConfig.icon, false)
    --end
    SetHeadAndBorderByGo(self.customHeadObj,data.icon,data.border)
    self.customHeadObj.transform.localPosition = Vector3.zero
    
    local rankConfig = DailyTargetManager:GetRankRewardConfig(data.rank)
    local rankReward = rankConfig.reward
    local level = LevelConfig:GetLevel()
    local upLevel = 9
    if level >= upLevel then
        rankReward = rankConfig.reward_1
    end
    local rewardT = string.split(rankReward,";")
    local count = table.count(rewardT)
    for k, v in ipairs(rewardT) do
        if self.rewardCellList[k] == nil then
            local obj =	UEGO.Instantiate(self.rewardGo,self.rewardListRoot.transform)
            self.rewardCellList[k] = {}
            self.rewardCellList[k].obj = obj
            local icon = GetChild(obj,"icon",UEUI.Image)
            local num = GetChild(obj,"num",UEUI.Text)
            self.rewardCellList[k].icon = icon
            self.rewardCellList[k].num = num
        end
    end
    for k, v in pairs(self.rewardCellList) do
        if count >= k then
            local config = rewardT[k]
            local rewardGo = v
            local arr = string.split(config,"|")
            local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
            local num = v2n(arr[2])
            SetActive(v.obj,true)
            SetUIImage(rewardGo.icon, ItemConfig:GetIcon(itemId), false)
            rewardGo.num.text = "x"..num
            if data.playerId then
                --rewardGo.num.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("144691")
                UnifyOutline(rewardGo.num.gameObject,"AB4810")
            else
                --rewardGo.num.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("B6451D")
                UnifyOutline(rewardGo.num.gameObject,"2C62AB")
            end
            local btn = rewardGo.icon.gameObject.transform:GetComponent(typeof(UEUI.Button))
            RemoveUIComponentEventCallback(btn,UEUI.Button)
            AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
                if itemId > 0  then
                    UI_SHOW(UIDefine.UI_ItemTips,itemId)
                end
            end)
        else
            SetActive(v.obj,false)
        end
    end
    self.txt_name.text = data.name
    if data.playerId then
        --棕色
        SetUIImage(self.bgImg,"Sprite/ui_meirimubiao/meirimubiao_win2_dilan2.png",false)
        SetUIImage(self.scoreBg,"Sprite/ui_meirimubiao/meirimubiao_win2_dilan2_1.png",false)

        self.txt_rank.text = string.format("<color=#b95c24>%s</color>", data.rank)
        --self.txt_name.text = string.format("<color=#144691>%s</color>", data.name)
        self.txt_score.text = string.format("<color=#C44C0A>%s</color>", data.point)
        --self.txt_score.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("144691")
        UnifyOutline(self.txt_name.gameObject,"C65200")
    else
        --蓝色
        self.txt_rank.text = string.format("<color=#2A62AC>%s</color>", data.rank)
        --self.txt_name.text = string.format("<color=#933f1f>%s</color>", data.name)
        self.txt_score.text = string.format("<color=#336EBD>%s</color>", data.point)
        SetUIImage(self.bgImg,"Sprite/ui_meirimubiao/meirimubiao_win2_dilan1.png",false)
        SetUIImage(self.scoreBg,"Sprite/ui_meirimubiao/meirimubiao_win2_dilan1_1.png",false)

        self.txt_score.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("B6451D")
        UnifyOutline(self.txt_name.gameObject,"336EBD")
    end
end

function DailyRankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function DailyRankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function DailyRankItem:onDestroy()
    UEGO.Destroy(self.transform.gameObject)
end

return UI_DailyTarget
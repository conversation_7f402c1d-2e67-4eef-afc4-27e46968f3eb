local UI_RankHelpModel = {}

UI_RankHelpModel.config = {["name"] = "UI_RankHelp", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1,["onEscape"] = false}

function UI_RankHelpModel:Init(c)
    c.ui = {}    
    c.ui.m_btnPrev = GetChild(c.uiGameObject,"root/bg/PageUI/m_btnPrev",UEUI.Button)
    c.ui.m_togG = GetChild(c.uiGameObject,"root/bg/PageUI/m_togG",UEUI.ToggleGroup)
    c.ui.m_goTog = GetChild(c.uiGameObject,"root/bg/PageUI/m_togG/m_goTog")
    c.ui.m_btnNext = GetChild(c.uiGameObject,"root/bg/PageUI/m_btnNext",UEUI.Button)
    c.ui.m_transDesc = GetChild(c.uiGameObject,"root/bg/Context/m_transDesc",UE.Transform)
    c.ui.m_txtDescribe = GetChild(c.uiGameObject,"root/bg/Context/m_transDesc/m_txtDescribe",UEUI.Text)
    c.ui.m_scrollview = GetChild(c.uiGameObject,"root/m_scrollview",UEUI.ScrollRect)
    c.ui.m_imgIcon1 = GetChild(c.uiGameObject,"root/m_scrollview/Mask/Content/Page1/Content/m_imgIcon1",UEUI.Image)
    c.ui.m_imgIcon2 = GetChild(c.uiGameObject,"root/m_scrollview/Mask/Content/Page1/Content/m_imgIcon2",UEUI.Image)
    c.ui.m_imgIcon3 = GetChild(c.uiGameObject,"root/m_scrollview/Mask/Content/Page1/Content/m_imgIcon3",UEUI.Image)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_RankHelpModel
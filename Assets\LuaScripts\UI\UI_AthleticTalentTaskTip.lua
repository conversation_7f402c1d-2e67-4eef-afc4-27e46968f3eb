local UI_AthleticTalentTaskTip = Class(BaseView)

local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local ScoreItem = Class(ItemBase)

function UI_AthleticTalentTaskTip:OnInit()
    
end

function UI_AthleticTalentTaskTip:OnCreate(param)
    self:GenerateScore()
end

function UI_AthleticTalentTaskTip:OnRefresh(param)
    
end

function UI_AthleticTalentTaskTip:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnClose" then
        self:Close()
        
    end
end

function UI_AthleticTalentTaskTip:onDestroy()
    if self.scoreItemList then
        for i = 1, #self.scoreItemList do
            self.scoreItemList[i]:onDestroy()
        end
    end
    self.scoreItemList = nil
end

function UI_AthleticTalentTaskTip:GenerateScore()
    self.scoreSlideRect = SlideRect.new()
    self.scoreSlideRect:Init(self.ui.m_scrollviewScore, 2)
    local item = self.ui.m_goCell.transform
    local scoreItemList = {}
    for i = 1, 16, 1 do
        scoreItemList[i] = ScoreItem.new()
        scoreItemList[i]:Init(UEGO.Instantiate(item))
    end
    self.scoreSlideRect:SetItems(scoreItemList, 0, Vector2.New(0, 0))
    self.scoreItemList = scoreItemList

    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local scoreData = AthleticTalentManager:GetScoreConfigByDay(currentDay)
    if scoreData then
        self.scoreSlideRect:SetData(scoreData)
    end
end

------------------------------ ScoreItem --------------------------------------
function ScoreItem:OnInit(transform)
    self.transform = transform.transform
    self.bg1 = GetChild(transform, "bg1")
    self.bg2 = GetChild(transform, "bg2")
    self.textDescribe = GetChild(transform, "describe", UEUI.Text)
    self.textPoints = GetChild(transform, "goRewardCell/points", UEUI.Text)
    self.icon = GetChild(transform, "Image", UEUI.Image)
end

function ScoreItem:UpdateData(data, index)
    self.data = data
    local showBg1 = index % 2 ~= 0
    SetActive(self.bg1, showBg1)
    self.textDescribe.text = LangMgr:GetLang(data.lang_id)
    self.textPoints.text = "+" .. data.score
    -- SetUIImage(self.icon, data.icon, false)
end

function ScoreItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function ScoreItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function ScoreItem:onDestroy()
    UEGO.Destroy(self.transform.gameObject)
    self.transform = nil
    self.bg1 = nil
    self.bg2 = nil
    self.textDescribe = nil
    self.textPoints = nil
    self.icon = nil
end


return UI_AthleticTalentTaskTip
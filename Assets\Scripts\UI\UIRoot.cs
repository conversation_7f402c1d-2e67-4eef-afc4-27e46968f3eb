using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

#pragma warning disable
public sealed class UIRoot : MonoBehaviour
{
        //[SerializeField]
        //private UIMask m_Mask;
        [SerializeField]
        private UICapture m_Capture;
        [SerializeField]
        private RectTransform m_CanvasLayer;
        [SerializeField]
        private RectTransform m_BackgroundLayer;
        [SerializeField]
        private RectTransform m_NormalLayer;
        [SerializeField]
        private RectTransform m_TopLayer;        
        [SerializeField]
        private RectTransform m_EffLayer;
        [SerializeField]
        private RectTransform m_LockLayer;
        [SerializeField]
        private EventSystem m_EventSystem;
        [SerializeField]
        private Camera m_UICamera;
        [SerializeField]
        private Canvas[] m_Canvas;
        [SerializeField]
        private CanvasScaler[] m_CanvasScaler;
        [SerializeField]
        private UIBloom m_Bloom;
        public RectTransform m_canvasLayer => m_CanvasLayer;
    public RectTransform BackgroundLayer => m_BackgroundLayer;
        public RectTransform NormalLayer => m_NormalLayer;
        public RectTransform TopLayer => m_TopLayer;
        public RectTransform LockLayer => m_LockLayer;
        
        public RectTransform EffectLayer => m_EffLayer;
        public EventSystem UIEventSystem => m_EventSystem;
        public Camera UICamera => m_UICamera;
        public Canvas[] UICanvas => m_Canvas;
        public CanvasScaler[] UIScaler => m_CanvasScaler;
        public Transform Root => transform;
        //public UIMask Mask => m_Mask;

        //public bool MaskVisable
        //{
        //    get => m_Mask.Visable;
        //    set => m_Mask.Visable = value;
        //}

        //public bool IsMask
        //{
        //    get => m_Mask.IsRaycast;
        //    set => m_Mask.IsRaycast = value;
        //}

        public void InitRoot()
        {
                this.name = "UIRoot";
                m_Capture.Init();
        }
        public void AdaptWechat()
        {
                float wechatTop = 550f;
#if UNITY_WEBGL && !UNITY_EDITOR
                var winInfo = WeChatWASM.WX.GetWindowInfo();
                var rect = WeChatWASM.WX.GetMenuButtonBoundingClientRect();
                wechatTop = (float)(winInfo.pixelRatio * Mathf.Max((float)rect.bottom, (float)winInfo.safeArea.top));
#endif 
                GameHelper.SafeAreaTop = wechatTop;
                float height = Screen.height;

                m_NormalLayer.anchorMin = new Vector2(0f, 0f);
                m_NormalLayer.anchorMax = new Vector2(1f, 1 - wechatTop / height);

                m_TopLayer.anchorMin = new Vector2(0f, 0f);
                m_TopLayer.anchorMax = new Vector2(1f, 1 - wechatTop / height);
                
                m_LockLayer.anchorMin = new Vector2(0f, 0f);
                m_LockLayer.anchorMax = new Vector2(1f, 1 - wechatTop / height);
        }

        public UICapture GetCapture()
        {
                return m_Capture;
        }
        public void SetBloom(bool isBloom)
        {
            m_Bloom.SetBloom(isBloom);
        }
}


local UI_SettingAccount = Class(BaseView)

function UI_SettingAccount:OnInit()
    
end

function UI_SettingAccount:OnCreate(param)
    self.bindBtn = GET_UI(self.uiGameObject, "bindBtn", TP(UEUI.Button));
    self.bindBtnImg = GET_UI(self.uiGameObject, "bindBtn", TP(UEUI.Image));

    self:BindStatus();

    if param == 1 then
        self:ShowBind()
    end
end

function UI_SettingAccount:OnRefresh(param)
    
end

function UI_SettingAccount:onDestroy()
    
end

function UI_SettingAccount:onUIEventClick(go,param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif name == "bindBtn" then
        self:ShowBind()
    elseif name == "changeBtn" then
        self:onBtnLogin();
    elseif name == "deleteBtn" then
        --local function onOk()
        --NetContactData:RequestInfomList(self.versionStr)
        --SetActive(self.contactPoint, false)
        --end
        --UI_SHOW(UIDefine.UI_DeleteAccountTips, onOk)

        self:onBtnDelData();
    end
end

function UI_SettingAccount:BindStatus()
    local isBind = SdkHelper:IsBinding()
    Log.Info("seting_ isBind" .. isBind)

    --检查绑定账号任务
    if isBind > 0 then
        local progress = NetTaskData:GetRealProgress(SpecialId.BindingAccountTaskId)
        if progress and progress < 1 then
            NetTaskData:CheckTaskAdd(TaskFinishType.BindingAccount, SpecialId.BindingAccountTaskId)
        end
    end

    if isBind <= 0 then
        self.ui.m_txtShow.text = LangMgr:GetLang(1062);
        SetUIColor(self.ui.m_txtShow, 215 / 255, 128 / 255, 64 / 255, 1); -- D78040

        self.ui.m_txtBind.text = LangMgr:GetLang(1058);
        SetUIImage(self.bindBtnImg, "Sprite/ui_setting/zhanghao_button_blue.png", false);

        SetUIBtnEnable(self.bindBtn, true);
        SetActive(self.ui.m_goBind, false);
    else
        self.ui.m_txtShow.text = LangMgr:GetLang(1061);
        SetUIColor(self.ui.m_txtShow, 67 / 255, 169 / 255, 62 / 255, 1); -- 43A93E

        self.ui.m_txtBind.text = "";
        SetUIImage(self.bindBtnImg, "Sprite/ui_setting/zhanghao_button_gray.png", false);

        local spritePath;
        if isBind == 1 then
            spritePath = "Sprite/ui_setting/iconbutton2_Facebook.png";
        elseif isBind == 2 then
            spritePath = "Sprite/ui_setting/iconbutton2_Google.png";
        elseif isBind == 7 then
            spritePath = "Sprite/ui_setting/iconbutton2_Apple.png";
        else
            spritePath = "Sprite/ui_setting/iconbutton2_Twitter.png";
        end
        SetUIImage(self.ui.m_imgBind, spritePath, false);

        SetUIBtnEnable(self.bindBtn, false);
        SetActive(self.ui.m_goBind, true);
    end
end

function UI_SettingAccount:ShowBind()
    if Game.IsUseSDK then
        local isBind = SdkHelper:IsBinding()
        if isBind > 0 then
            -- SdkHelper:ChangeLogin(function(state)
            --     if state == 1 then
            --         UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1042))
            --         --self:BindStatus()
            --         local function onOk()
            --             --GameUtil:QuitGame()
            --             SceneMgr:SwitchScene(UISceneDefine.LoginScene,{0,function ()
            --                 _G.CS.StartGame.Instance:OpenUILogin()
            --             end})
            --             --SceneMgr:SwitchScene(UISceneDefine.LoginScene)
            --         end

            --         UI_SHOW(UIDefine.UI_TipsTop, "Account is changed, ReLogin the Game?", onOk, nil, 2)
            --     end
            -- end)
            return
        end

		SdkHelper:Binding(function(state)
			if state ~= 1 then

			else
				UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1042))
				self:BindStatus()
				self:Close()
			end
		end)
    else
        SceneMgr:SwitchScene(UISceneDefine.LoginScene)
    end
end

function UI_SettingAccount:onBtnLogin()
    local function onOk()
        SdkHelper:ChangeLogin(function(state)
            if state == 1 then
               UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1042))
                    --self:BindStatus()
                    local function onOk()
                        SceneMgr:SwitchScene(UISceneDefine.LoginScene,{0,function ()
                           _G.CS.StartGame.Instance:OpenUILogin()
                    end})
                    --SceneMgr:SwitchScene(UISceneDefine.LoginScene)
                end

                UI_SHOW(UIDefine.UI_TipsTop, "Account is changed, ReLogin the Game?", onOk, nil, 2)
            end
        end)
    end

    UI_SHOW(UIDefine.UI_Reconfirm, LangMgr:GetLang(8701), LangMgr:GetLang(8702),
        onOk, nil, 10
    )
end

function UI_SettingAccount:onBtnDelData()
    local function onOk()
        SceneMgr:SwitchScene(UISceneDefine.LoginScene,{0,function ()
            local thinkTable = {["delete"] = 1}
            SdkHelper:ThinkingTrackEvent(ThinkingKey.delete_account,thinkTable)
            StorageMgr:ClearGameData()
            LoginModule:HttpRequestUserInfoPackage()
        end,["ignoreSaveOnLeave"] = true})
    end


    UI_SHOW(UIDefine.UI_Reconfirm, LangMgr:GetLang(8196), LangMgr:GetLang(8193),
        function ()
            UI_CLOSE(UIDefine.UI_Reconfirm)
            UI_SHOW(UIDefine.UI_ReconfirmInput,LangMgr:GetLang(8196),LangMgr:GetLang(195),function ()
                    onOk()
                end)
        end, nil, 10
    )
end

return UI_SettingAccount
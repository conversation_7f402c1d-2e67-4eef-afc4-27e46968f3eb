--- @class UI_DailyTargetHelp:BaseHelpView
local UI_DailyTargetHelp = Class(BaseHelpView)

function UI_DailyTargetHelp:OnInit()

end

function UI_DailyTargetHelp:OnCreate(callback)
    self:OnCreate_Base()
    self.callback = callback
    self.config = ConfigMgr:GetDataByKey(ConfigDefine.ID.activity_help, "prefab", UIDefine.UI_DailyTargetHelp, true)
    local config = self.config
    if not config then
        Log.Error("获取帮助界面配置表错误 error ")
        return
    end
    for pageIndex, value in pairs(config) do
        local page = GetChild(self.ui.m_scrollview.content, "Page" .. value.page)
        -- 图标列表
        local iconList = string.split(value.how_icon, "|")
        for index, icon in ipairs(iconList) do
            local path = string.format("Content/item%d", index, index)
            local img = GetChild(page, path, UEUI.Image)
            SetActive(img,true)
            if (pageIndex == 1 and index == 2) or (pageIndex == 2 and index == 1) then
                local todayItemID = DailyTargetManager:GetTodayObjItemId()
                icon = ItemConfig:GetIcon(todayItemID)
            end
            SetUIImage(img, icon, false)
        end
    end
    -- 标题
    self.ui.m_txtDesc.text = LangMgr:GetLang(config[1].describe)
    --local roleconfig = RoleSpineConfig:GetDataByID(config[1].spine)
    --if roleconfig and roleconfig.img_spine then
    --    self.ui.m_spui.skeletonDataAsset = ResMgr:LoadAssetSync(roleconfig.img_spine,AssetDefine.LoadType.Instant)
    --    self.ui.m_spui:Initialize(true)
    --end
end

function UI_DailyTargetHelp:OnRefresh(param)

end

function UI_DailyTargetHelp:onDestroy()
    if self.callback then
        self.callback()
        self.callback = nil
    end
end

function UI_DailyTargetHelp:onUIEventClick(go,param)
    local name = go.name

end

function UI_DailyTargetHelp:OnMoveStart_Virtual(pageIndex)
    if not self.config then
        return
    end

    local config = self.config[pageIndex]
    if not config then
        return
    end
    -- 标题
    self.ui.m_txtDesc.text = LangMgr:GetLang(config.describe)
end

return UI_DailyTargetHelp
local UI_SeasonTaskHelpModel = {}

UI_SeasonTaskHelpModel.config = {["name"] = "UI_SeasonTaskHelp", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 0,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_SeasonTaskHelpModel:Init(c)
    c.ui = {}    
    c.ui.m_spui = GetChild(c.uiGameObject,"spine/m_spui",CS.Spine.Unity.SkeletonGraphic)
    c.ui.m_btnPrev = GetChild(c.uiGameObject,"PageUI/m_btnPrev",UEUI.Button)
    c.ui.m_togG = GetChild(c.uiGameObject,"PageUI/m_togG",UEUI.ToggleGroup)
    c.ui.m_goTog = GetChild(c.uiGameObject,"PageUI/m_togG/m_goTog")
    c.ui.m_btnNext = GetChild(c.uiGameObject,"PageUI/m_btnNext",UEUI.Button)
    c.ui.m_transDesc = GetChild(c.uiGameObject,"m_transDesc",UE.Transform)
    c.ui.m_scrollview = GetChild(c.uiGameObject,"bg/m_scrollview",UEUI.ScrollRect)
    c.ui.m_goConten = GetChild(c.uiGameObject,"bg/m_scrollview/Mask/m_goConten")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_SeasonTaskHelpModel
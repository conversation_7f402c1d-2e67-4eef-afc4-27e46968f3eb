local UI_Mail = Class(BaseView)
local M = UI_Mail

local MailTemplate_LeagueBossKillReward = require "UI.Mail.MailTemplate_LeagueBossKillReward"
local MailTemplate_LeagueBossRankReward = require "UI.Mail.MailTemplate_LeagueBossRankReward"
local MailTemplate_ArenaSettleReward = require "UI.Mail.MailTemplate_ArenaSettleReward"
local MailTemplate_TrainDepart = require "UI.Mail.MailTemplate_TrainDepart"
local MailTemplate_TrainArrival = require "UI.Mail.MailTemplate_TrainArrival"
local MailTemplate_TrainReward = require "UI.Mail.MailTemplate_TrainReward"
local MailTemplate_WorldBossRankReward = require "UI.Mail.MailTemplate_WorldBossRankReward"

local ItemBase = require("UI.Common.BaseSlideItem")
local Item = Class(ItemBase)
local ItemReward = Class(ItemBase)

function M:OnInit()
	self.curMailType = nil
	self.curMailIndex = 1
	self.curMailId = 0

	self.mailList = {}
	self.mailTemplate = nil

	self.tabData = {
		{["name"] = 70000578,["type"] = MAIL_TYPE.SYSTEM,["redId"] = ServerDefine.marge_topia_redpoint_RedPointType.RedPointType_MailSystem,["icon"] = "Sprite/ui_mail/youjian_icon_xitong.png"},
		{["name"] = 70000579,["type"] = MAIL_TYPE.LEAGUE,["redId"] = ServerDefine.marge_topia_redpoint_RedPointType.RedPointType_MailLeague,["icon"] = "Sprite/ui_mail/youjian_icon_tuandui.png"},
		{["name"] = 70000580,["type"] = MAIL_TYPE.REPORT,["redId"] = ServerDefine.marge_topia_redpoint_RedPointType.RedPointType_MailReport,["icon"] = "Sprite/ui_mail/youjian_icon_zhanbao.png"},
		{["name"] = 70000581,["type"] = MAIL_TYPE.SEARCH,["redId"] = ServerDefine.marge_topia_redpoint_RedPointType.RedPointType_MailSearch,["icon"] = "Sprite/ui_mail/youjian_icon_tanshuo.png"},
	}

	self.slider = require("UI.Common.SlideRect").new()
	self.sliderReward = require("UI.Common.SlideRect").new()
end

function M:OnCreate(param)

	MailManager:MailFilter()

	MailManager:SetSortDirty(MAIL_TYPE.SYSTEM,true)
	MailManager:SetSortDirty(MAIL_TYPE.LEAGUE,true)
	MailManager:SetSortDirty(MAIL_TYPE.REPORT,true)
	MailManager:SetSortDirty(MAIL_TYPE.SEARCH,true)

	--底部描述
	local day = MailManager:GetMailDeleteRentionDay()
	day = GetStrRichColor(day,"009126")
	self.ui.m_txtNotice.text = LangMgr:GetLangFormat(70000586,day)

	--资源栏
	local info = NetUpdatePlayerData:GetPlayerInfo()
	--初始化的时候type为空
	self.ui.m_txtDiamond.text = math.floor(info["diamond"])
	self.ui.m_txtCoin.text = math.floor(info["coin"])
	self.ui.m_txtMagic.text = info["magic"]
	EventMgr:Add(EventID.CHANGE_RESOURCE,  self.ChangeResource, self)

	--邮件列表
	local mailCell = self.ui.m_goCell
	SetActive(mailCell,false)
	self.slider:Init(GetComponent(self.ui.m_goSlideRect,UEUI.ScrollRect),2)
	local _items = {}
	for i = 1, 10 do
		_items[i] = Item.new()
		_items[i]:Init(UEGO.Instantiate(mailCell.transform))
		_items[i].panel = self
	end
	self.slider:SetItems(_items,10,Vector2.New(0,0))

	--附件
	local rewardCell = self.ui.m_goRewardCell
	SetActive(rewardCell,false)
	self.sliderReward:Init(GetComponent(self.ui.m_goGiftviewPort,UEUI.ScrollRect),1)
	local _items = {}
	for i = 1, 10 do
		_items[i] = ItemReward.new()
		_items[i]:Init(UEGO.Instantiate(rewardCell.transform))
		_items[i].panel = self
	end
	self.sliderReward:SetItems(_items,5,Vector2.New(0,0))

	self:InitTab()
	self:SwitchTab(MAIL_TYPE.SYSTEM)

	EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)

	self:SetIsUpdateTick(true)
end

function M:TickUI(delta)
	if self.slider and self.slider.viewportHeight <= 0 then
		self.slider.viewportHeight = self.slider.scroll.viewport.rect.height
		self:RefreshMailList(self.curMailType,true,false)
	end
end

function M:OnRedPointDirty(dirtyIdSet)
	for index, data in ipairs(self.tabData) do
		if dirtyIdSet[data["redId"]] then
			SetActive(data.goRed,RedPointMgr:IsRed(data["redId"]))
			data.txtRedCnt.text = RedPointMgr:GetRedCount(data["redId"])
		end
	end
end

function M:InitTab()
	for index, data in ipairs(self.tabData) do
		local item = UEGO.Instantiate(self.ui.m_togCell,self.ui.m_togCell.transform.parent)
		self:SetTabBtn(index,data,item)
	end
	SetActive(self.ui.m_togCell,false)
end

function M:SetTabBtn(index,data,tabBtn)

	local name = LangMgr:GetLang(data["name"])
	local type = data["type"]

	local txtName1 = GetChild(tabBtn, "Background/txtName1",UEUI.Text)
	local txtName2 = GetChild(tabBtn, "Background/Checkmark/txtName2",UEUI.Text)
	txtName1.text = name
	txtName2.text = name

	local imgIcon = GetChild(tabBtn, "Background/icon",UEUI.Image)
	SetImageSprite(imgIcon,data["icon"],false)

	data.goRed = GetChild(tabBtn, "Background/goRed")
	data.txtRedCnt = GetChild(tabBtn, "Background/goRed/txtRedCnt",UEUI.Text)

	SetActive(data.goRed,RedPointMgr:IsRed(data["redId"]))
	data.txtRedCnt.text = RedPointMgr:GetRedCount(data["redId"])

	SetActive(tabBtn.graphic.gameObject,data.type == MAIL_TYPE.SYSTEM)
	RemoveUIComponentEventCallback(tabBtn, UEUI.Toggle)
	AddUIComponentEventCallback(tabBtn, UEUI.Toggle, function(arg1,arg2)
		SetActive(tabBtn.graphic.gameObject,arg2)
		if arg2 == true then
			self:SwitchTab(type)
		end
	end)

	SetActive(tabBtn,self:IsTabOpen(type))
end

function M:IsTabOpen(mailType)
	if mailType == MAIL_TYPE.SYSTEM then
		return true
	elseif mailType == MAIL_TYPE.LEAGUE then
		return true
	elseif mailType == MAIL_TYPE.REPORT then
		return false
	elseif mailType == MAIL_TYPE.SEARCH then
		return false
	end
	return false
end

function M:OnRefresh(type,param)
	--1刷新当前 邮件列表 2已读邮件刷新 3领取附件 4删除邮件
	if type == 1 or type ==2 or type == 3 or type == 4 then
		local mailType = param.mailType
		local force = param.force
		local resetPos = param.resetPos
		if self.curMailType == mailType then
			self:RefreshMailList(self.curMailType,force,resetPos)
		end
	end
end

function M:SwitchTab(mailType)
	self:RefreshMailList(mailType,true,true,1)
end

function M:RefreshMailList(mailType,force,resetPos,newIndex)
	if not mailType then
		mailType = MAIL_TYPE.SYSTEM
	end
	if self.curMailType == mailType and not force then
		return
	end

	self.curMailType = mailType
	self.mailList = MailManager:GetMailByType(self.curMailType)

	if MailManager:IsSortDirty(mailType) then
		MailManager:SetSortDirty(mailType,false)
		table.sort(self.mailList,function(a,b)
			if a.create_at ~= b.create_at then
				return a.create_at > b.create_at
			else
				local aP = MailManager:GetSortPriority(a)
				local bP = MailManager:GetSortPriority(b)
				if aP ~= bP then
					return aP > bP
				else
					return a.id > b.id
				end
			end
		end)
	end

	--尝试重置index
	if not resetPos and not newIndex and self.curMailId ~= nil then
		local foundIndex = nil
		for i, mailDataTemp in ipairs(self.mailList) do
			if mailDataTemp.id == self.curMailId then
				foundIndex = i
				break
			end
		end
		if foundIndex then
			newIndex = foundIndex
		end
	end
	
	if newIndex then
		self.curMailIndex = newIndex
	end
	
	--检查index 是否越界
	local mailCnt = #self.mailList
	if self.curMailIndex > mailCnt then
		self.curMailIndex = mailCnt
	end
	if self.curMailIndex <= 0 then
		self.curMailIndex = 1
	end
	
	if self.slider then
		if resetPos then
			self.slider:SetData(self.mailList,1)
		else
			local oldSize = self.slider.contentHeight
			
			local pos = self.slider:GetCurrentPostion()
			local index = self.slider.movedIndex + 1
			self.slider:SetData(self.mailList,index)

			if self.slider.contentHeight == oldSize then
				self.slider:MoveToPosition(self.slider:GetCurrentPostion(),pos,0)
			end
		end
	end

	--邮件数量
	self.ui.m_txtMailCnt.text = GetStrRichColor(#self.mailList,"fff36b") .."/"..MailManager:GetMailMaxCntLimit(mailType)

	if #self.mailList <= 0 then
		SetActive(self.ui.m_goEmptyTip,true) 
		SetActive(self.ui.m_goEmptyTip2,true) 
		SetActive(self.ui.m_btnReceiveAll,false) 
		SetActive(self.ui.m_btnDeleteAll,false)
	else
		SetActive(self.ui.m_goEmptyTip,false) 
		SetActive(self.ui.m_goEmptyTip2,false) 
		SetActive(self.ui.m_btnReceiveAll,true) 
		SetActive(self.ui.m_btnDeleteAll,true) 
	end

	--self:RefreshMailPanel(force)
end

function M:OnMailIndexChange(index)
	local mailData = self.mailList[index]
	if mailData then
		UI_SHOW(UIDefine.UI_MailConent,mailData)
	end
	if self.curMailIndex == index then return end
	self:RefreshMailList(self.curMailType,true,false,index)
end

function M:RefreshMailPanel(force)
	
	local mailData = self.mailList[self.curMailIndex]
	if mailData then
		local tempTemplate = self:GetMailContentPanelByType(mailData.content.type)
		--发送已读
		if not MailManager:HasRead(mailData) then
			MailManager:ReadMail(mailData.type,mailData.id)
		end
		SetActive(self.ui.m_goMailInfoCenter,true)
		--top 标题和日期
		if tempTemplate then
			self.ui.m_txtTitle.text = MailManager:GetMailTitle(mailData)
		else
			self.ui.m_txtTitle.text = LangMgr:GetLang(7071)--未知邮件
		end
		SetActive(self.ui.m_goMailInfoTop,true)
		--按钮
		SetActive(self.ui.m_btnDelete,true)
		if mailData.content.type == MAIL_CONTENT_TYPE.TEXT then
			local td = MailManager:GetMailTranslateData(mailData.id)
			if td and td.inTranslate then
				SetActive(self.ui.m_btnTranslate,false)
				SetActive(self.ui.m_btnBackTranslate,true)
			else
				SetActive(self.ui.m_btnTranslate,true)
				SetActive(self.ui.m_btnBackTranslate,false)
			end
		else
			SetActive(self.ui.m_btnTranslate,false)
			SetActive(self.ui.m_btnBackTranslate,false)
		end

		--邮件剩余时间
		local rentionDay = MailManager:GetMailDeleteRentionDay()
		local nowTime = TimeMgr:GetServerTime()
		local remainDay = rentionDay - Mathf.Floor((nowTime - mailData.create_at)/(60*60*24))
		if remainDay <= 0 then
			self.ui.m_txtTime.text = TimeMgr:StampToDateDes(mailData.create_at)..GetStrRichColor(LangMgr:GetLang(70000589),"ff7787")
		else
			self.ui.m_txtTime.text = TimeMgr:StampToDateDes(mailData.create_at)..GetStrRichColor(LangMgr:GetLangFormat(70000588,remainDay),"ff7787")
		end

		-- bottom 附件列表
		if mailData.rewards and next(mailData.rewards) then
			--数据二次封装
			local data = {}
			local hasClaimed = MailManager:HasClaimed(mailData)
			for k,v in pairs(mailData.rewards) do
				local temp = {}
				temp.code = v.code
				temp.amount = v.amount
				temp.hasClaimed = hasClaimed
				table.insert(data,temp)
			end

			self.sliderReward:SetData(data,1)
			SetActive(self.ui.m_goMailInfoBottom,true)

			local notClaimed = not MailManager:HasClaimed(mailData)
			if notClaimed then
				SetActive(self.ui.m_btnClaim,true)
				SetActive(self.ui.m_btnHasClaim,false)
			else
				SetActive(self.ui.m_btnClaim,false)
				SetActive(self.ui.m_btnHasClaim,true)
			end
		else
			SetActive(self.ui.m_goMailInfoBottom,false)
		end

		-- center 邮件内容
		if self.curMailId ~= mailData.id or force then
			self.curMailId = mailData.id
		
			--邮件内容
			local parent = self.ui.m_goMailInfoCenter

			if self.mailTemplate then
				UEGO.Destroy(self.mailTemplate)
				self.mailTemplate = nil
			end
			local tempTemplate = self:GetMailContentPanelByType(mailData.content.type)
			if tempTemplate then
				self.mailTemplate = CreateGameObjectWithParent(tempTemplate,parent)
				self:FillTemplate(mailData,self.mailTemplate)
				-- local rectTrans = GetComponent(self.mailTemplate, UE.RectTransform)
				-- UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(rectTrans)
				-- parent:SetInsetAndSizeFromParentEdge(2,0,rectTrans.sizeDelta.y)
			else
				self.mailTemplate = CreateGameObjectWithParent(self.ui.m_goTemplateUnsupportedType,parent)
			end

		end
	else
		SetActive(self.ui.m_goMailInfoTop,false)
		SetActive(self.ui.m_goMailInfoCenter,false)
		SetActive(self.ui.m_goMailInfoBottom,false)
	end
end

function M:onDestroy()
    self.curMailType = nil
	EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
	EventMgr:Remove(EventID.CHANGE_RESOURCE, self.ChangeResource, self)
end

--region 事件
function M:onUIEventClick(go,param)
    local name = go.name
	if name == "btnClose" then
		self:Close()
	elseif name == "m_btnClaim" then
		if self.curMailId then
			Log.Info("领取附件")
			local mailData = self.mailList[self.curMailIndex]
			if mailData then
				MailManager:ClaimMail(mailData.type,mailData.id)
			end
		end
	elseif name == "m_btnDelete" then
		

		if self.curMailId then
			local mailData = self.mailList[self.curMailIndex]
			if mailData then

				if MailManager:HasReward(mailData) and not MailManager:HasClaimed(mailData) then
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000668))
					return
				end

				Log.Info("删除指定邮件"..mailData.id)
				UI_SHOW(UIDefine.UI_Reconfirm, LangMgr:GetLang(89), LangMgr:GetLang(70000656),
                function ()
                    UI_CLOSE(UIDefine.UI_Reconfirm)
					MailManager:DeleteMail(mailData.type,mailData.id)
                end,
                nil)
			end
		end
	elseif name == "m_btnTranslate" then
		if self.curMailId then
			local mailData = self.mailList[self.curMailIndex]
			if mailData then

				local td = MailManager:GetMailTranslateData(mailData.id)
				if not td.title then
					local title = MailManager:GetMailTitle(mailData)
					MailManager:SendTranslateInfo(mailData.id,1,title,function (data)
						-- self:RefreshMailPanel()
						self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
					end)
				end

				if not td.content then
					local content = MailManager:GetMailContentText(mailData)
					MailManager:SendTranslateInfo(mailData.id,2,content,function (data)
						-- self:RefreshMailPanel(true)
						self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
					end)
				end

				if not td.subTitle then
					local subTitle = MailManager:GetMailSubTitle(mailData)
					MailManager:SendTranslateInfo(mailData.id,3,subTitle,function (data)
						-- self:RefreshMailPanel(true)
						self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
					end)
				end

				td.inTranslate = true
				if td.title or td.content or not td.subTitle then
					-- local force = td.content~=nil
					-- self:RefreshMailPanel(force)
					self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
				end

				
			end
		end
	elseif name == "m_btnBackTranslate" then
		if self.curMailId then
			local mailData = self.mailList[self.curMailIndex]
			if mailData then
				local td = MailManager:GetMailTranslateData(mailData.id)
				td.inTranslate = false
				-- self:RefreshMailPanel(true)
				self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
			end
		end
	elseif name == "m_btnDeleteAll" then
		Log.Info("删除所有邮件")
		UI_SHOW(UIDefine.UI_Reconfirm, LangMgr:GetLang(89), LangMgr:GetLang(70000681),
			function ()
				UI_CLOSE(UIDefine.UI_Reconfirm)
				MailManager:DeleteMail(self.curMailType,0)
			end,
		nil)
	elseif name == "m_btnReceiveAll" then
		Log.Info("领取和已读所有邮件")
		MailManager:ClaimMail(self.curMailType,0)--领取必定已读
	end
end
--endregion

function M:GetMailContentPanelByType(type)
	local template = nil
	if type == MAIL_CONTENT_TYPE.TEXT or type == MAIL_CONTENT_TYPE.WORLD_BOSS_JOIN_REWARD or type == MAIL_CONTENT_TYPE.TEXT_LANG then
		template = self.ui.m_goTemplateText
	elseif type == MAIL_CONTENT_TYPE.TEST then
	elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_KILL_REWARD then
		template = self.ui.m_goTemplateLeagueBossRank
	elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_RANK_REWARD then
		template = self.ui.m_goTemplateLeagueBossRank
	elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_DEPART then
		template = self.ui.m_goTemplateTrainDepart
	elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_ARRIVAL then
		template = self.ui.m_goTemplateTrainArrival
	elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_REWARD then
		template = self.ui.m_goTemplateTrainReward
	elseif type == MAIL_CONTENT_TYPE.ARENA_SETTLE_REWARD then
		template = self.ui.m_goTemplateArenaSettleReward
	elseif type == MAIL_CONTENT_TYPE.WORLD_BOSS_RANK_REWARD then
		template = self.ui.m_goTemplateWorlBossRank
	end
	return template
end

function M:FillTemplate(mailData,template)
	local content = mailData.content
	local type = content.type
	local bytes = content.content

	if type == MAIL_CONTENT_TYPE.TEXT then
		self:FillTemplate_Text(bytes,template,mailData)
	elseif type == MAIL_CONTENT_TYPE.TEST then
	elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_KILL_REWARD then
		if not self.mtLeagueBossRankReward then
			self.mtLeagueBossRankReward = MailTemplate_LeagueBossRankReward.new()
		end
		self.mtLeagueBossRankReward:FillTemplate(1,bytes,template,mailData)
	elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_RANK_REWARD then
		if not self.mtLeagueBossRankReward then
			self.mtLeagueBossRankReward = MailTemplate_LeagueBossRankReward.new()
		end
		self.mtLeagueBossRankReward:FillTemplate(2,bytes,template,mailData)
	elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_DEPART then
		--贸易货车，火车发车邮件
		if not self.mtTrainDepart then
			self.mtTrainDepart = MailTemplate_TrainDepart.new()
		end
		self.mtTrainDepart:FillTemplate(bytes,template,mailData)
	elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_ARRIVAL then
		--贸易货车，火车到达邮件
		if not self.mtTrainArrival then
			self.mtTrainArrival = MailTemplate_TrainArrival.new()
		end
		self.mtTrainArrival:FillTemplate(bytes,template,mailData)
	elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_REWARD then
		--贸易货车，火车奖励邮件
		if not self.mtTrainReward then
			self.mtTrainReward = MailTemplate_TrainReward.new()
		end
		self.mtTrainReward:FillTemplate(bytes,template,mailData)
	elseif type == MAIL_CONTENT_TYPE.ARENA_SETTLE_REWARD then
		if not self.mtArenaSettleReward then
			self.mtArenaSettleReward = MailTemplate_ArenaSettleReward.new()
		end
		self.mtArenaSettleReward:FillTemplate(bytes, template, mailData)
	elseif type == MAIL_CONTENT_TYPE.WORLD_BOSS_RANK_REWARD then
		if not self.mtWorldBossRankReward then
			self.mtWorldBossRankReward = MailTemplate_WorldBossRankReward.new()
		end
		self.mtWorldBossRankReward:FillTemplate(bytes, template, mailData)
	elseif type == MAIL_CONTENT_TYPE.WORLD_BOSS_JOIN_REWARD then
		self:FillTemplate_WorldBossJoinText(bytes, template, mailData);
	elseif type == MAIL_CONTENT_TYPE.TEXT_LANG then
		self:FillTemplate_LangText(bytes, template, mailData);
	end
end

function M:FillTemplate_Text(bytes,template,mailData)
	local textGo = GetChild(template, "Viewport/Content/text",UEUI.Text)
	if textGo then
		local contentText = MailManager:GetMailContentText(mailData)
		textGo.OnClick = function(url)
			url = string.gsub(url, "%s+", "")
			url = string.gsub(url, "\"", "")
			url = string.gsub(url, "'", "")
			CS.UnityEngine.Application.OpenURL(url);
		end	
		textGo.text = contentText
	end
end

function M:FillTemplate_WorldBossJoinText(bytes, template, mailData)
	local textGo = GetChild(template, "Viewport/Content/text", UEUI.Text)
	if textGo then
		local data = ProtocManager:Decode("marge.topia.mail.ContentWorldBossJoinReward", bytes)
		if not data then
			Log.Error("protocManager decode error")
			return
		end

		local content_id = data.content_id;
		local boss_id = data.boss_id;
		local join_times = data.join_times;
		textGo.text = LangMgr:GetLangFormat(content_id, WorldBossManager:GetMonsterName(boss_id), join_times);
	end
end

function M:FillTemplate_LangText(bytes, template, mailData)
	local textGo = GetChild(template, "Viewport/Content/text", UEUI.Text)
	if textGo then
		local data = ProtocManager:Decode("marge.topia.mail.ContextLang", bytes)
		if not data then
			Log.Error("protocManager decode error")
			return
		end

		local content_id = data.content_id;
		local args = data.args;
		if content_id and args then
			local dic = {};
			for i = 1, #args do
				local langStr = LangMgr:GetLang(args[i]);
				table.insert(dic, langStr)
			end
			textGo.text = LangMgr:GetLangFormat(content_id, table.SafeUnpack(dic))
		end
	end
end

function M:ChangeResource(type, num, changeValue,isTop)
	local info = NetUpdatePlayerData:GetPlayerInfo()
	
	if type == PlayerDefine.Magic then
		local magic = info["magic"]
		self.ui.m_doMagic:DORestart()
		AddDOTweenNumberDelay(magic - changeValue, magic, 0.3, 0.8, function(value)

				self.ui.m_txtMagic.text = math.floor(value)
			end)
	elseif type == PlayerDefine.Diamond then
		local diamond = info["diamond"]
		self.ui.m_doDiamond:DORestart()
		AddDOTweenNumberDelay(diamond - changeValue, diamond, 0.3, 0.8, function(value)

				self.ui.m_txtDiamond.text = math.floor(value)
			end,function() FinishOrder(PlayerDefine.Diamond) end)
	elseif type == PlayerDefine.Coin then
		local coin = info["coin"]
		self.ui.m_doCoin:DORestart()
		AddDOTweenNumberDelay(coin - changeValue, coin, 0.3, 0.8, function(value)

				self.ui.m_txtCoin.text = math.floor(value)
			end,function() FinishOrder(PlayerDefine.Coin) end)
	end
end


--region Item
function Item:UpdateData(data,index)
	self.index = index
	self.id = data.id
	Log.Info("Item:UpdateData")
	
	local isUnknownMail = false
	local template = self.panel:GetMailContentPanelByType(data.content.type)
	isUnknownMail = not template
	if isUnknownMail then
		self.txtTitle.text = LangMgr:GetLang(7071)--未知邮件
		self.txtSubTitle.text = ""
	else
		self.txtTitle.text = MailManager:GetMailTitle(data)
		self.txtSubTitle.text = MailManager:GetMailSubTitle(data)
	end
	self.txtTime.text = TimeMgr:StampToDateDes(data.create_at)

	local inSelect = self.panel.curMailIndex == index
	self.hasRead = inSelect or MailManager:HasRead(data)
	SetActive(self.bgInSelect,inSelect)
	if inSelect then
		self.txtTitle.color = Color.New(218/255,81/255,33/255,1)
		self.txtSubTitle.color = Color.New(228/255,144/255,0/255,1)
		self.txtTime.color = Color.New(228/255,144/255,0/255,1)
		SetActive(self.bgHasRead,false)
	else
		if self.hasRead then
			self.txtTitle.color = Color.New(71/255,79/255,138/255,1)
			self.txtSubTitle.color = Color.New(114/255,117/255,172/255,1)
			self.txtTime.color = Color.New(114/255,117/255,172/255,1)
		else
			self.txtTitle.color = Color.New(34/255,72/255,147/255,1)
			self.txtSubTitle.color = Color.New(99/255,122/255,167/255,1)
			self.txtTime.color = Color.New(99/255,122/255,168/255,1)
		end
		SetActive(self.bgHasRead,self.hasRead)
	end

	SetActive(self.imgRed,not self.hasRead)
	SetActive(self.imgMailIcon,not self.hasRead)
	SetActive(self.imgMailIconRead,self.hasRead)

	--设置已读icon
	local newIconId = data.new_icon or 101
	local readIconId = data.read_icon or 102

	--local mailSettingCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.mail_setting,newIconId)
	--if mailSettingCfg and mailSettingCfg["value"] then
	--	SetImageSprite(self.imgMailIcon,mailSettingCfg["value"],true)
	--end
	--local mailSettingCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.mail_setting,readIconId)
	--if mailSettingCfg and mailSettingCfg["value"] then
	--	SetImageSprite(self.imgMailIconRead,mailSettingCfg["value"],true)
	--end

	if data.rewards and next(data.rewards) then
		--有附件
		SetActive(self.imgReward,true)
		self.hasClaimed = MailManager:HasClaimed(data)
		SetActive(self.imgRewardReceived,self.hasClaimed)
		if self.hasClaimed then
			self.imgReward.color = Color.New(1,1,1,0.6)
		else
			self.imgReward.color = Color.New(1,1,1,1)
		end
	else
		--没有附件
		SetActive(self.imgReward,false)
	end

end

function Item:OnClick()
	Log.Info("click"..self.index)
	
	self.panel:OnMailIndexChange(self.index)
end

function Item:OnInit(transform)
	self.transform = transform.transform

	self.bgHasRead = GetChild(transform, "bgHasRead")
	self.bgInSelect = GetChild(transform, "bgInSelect")

	self.txtTitle = GetChild(transform, "left/txtTitle",UEUI.Text)
	self.txtSubTitle = GetChild(transform, "left/txtSubTitle",UEUI.Text)
	self.txtTime = GetChild(transform, "left/txtTime",UEUI.Text)
	self.imgMailIcon = GetChild(transform, "left/imgMailIcon",UEUI.Image)
	self.imgMailIconRead = GetChild(transform, "left/imgMailIconRead",UEUI.Image)

	self.imgReward = GetChild(transform, "right/imgReward",UEUI.Image)
	self.imgRewardReceived = GetChild(transform, "right/imgReward/imgRewardReceived",UEUI.Image)
	self.imgRed = GetChild(transform, "right/imgRed",UEUI.Image)

	self.btn =  GetChild(transform, "btnClick",UEUI.Button)
	self.btn.onClick:RemoveAllListeners()
	local function onStepClick()
		self:OnClick()
	end
	self.btn.onClick:AddListener(onStepClick)

	self.tempV2_1 = Vector2.New(0,0)
	self.tempV2_2 = Vector2.New(0,0)
end

function Item:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
	self.tempV2_1:Set(0, self.rectTrans.offsetMin.y)
	self.tempV2_2:Set(0, self.rectTrans.offsetMax.y)
	self.rectTrans.offsetMin = self.tempV2_1
	self.rectTrans.offsetMax = self.tempV2_2
end

function Item:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end
--endregion

--region ItemReward
function ItemReward:UpdateData(data,index)
	local itemId = data.code
	local count = data.amount
	local hasClaimed = data.hasClaimed
	self.itemId = itemId
	SetImageSprite(self.imgItemIcon,ItemConfig:GetIcon(itemId),false)
	self.txtItemCnt.text = "x"..NumToGameString(count)
	SetActive(self.goHasClaim,hasClaimed)
end

function ItemReward:OnClick()
	if self.itemId then
		-- Log.Info("click"..self.itemId)
		UI_SHOW(UIDefine.UI_ItemTips,self.itemId)
	end
end

function ItemReward:OnInit(transform)
	self.transform = transform.transform
	self.imgItemIcon = GetChild(transform, "imgIcon",UEUI.Image)
	self.txtItemCnt = GetChild(transform, "txtCnt",UEUI.Text)
	self.goHasClaim = GetChild(transform, "hasClaim")

	self.btn =  GetComponent(transform,UEUI.Button)
	self.btn.onClick:RemoveAllListeners()
	local function onStepClick()
		self:OnClick()
	end
	self.btn.onClick:AddListener(onStepClick)
end

function ItemReward:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function ItemReward:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end
--endregion

return UI_Mail
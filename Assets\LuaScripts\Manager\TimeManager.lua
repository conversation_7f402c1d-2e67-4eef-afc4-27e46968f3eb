local TimeObject = require("Manager.TimeManagerObject")
local TimeManager = Class()

OneDaySeconds = 24 * 3600
local oneMinute = 60
local oneHour = 3600

TIME_WDAY_DEFINE = {
    [1] = 1,
    [2] = 7,
    [3] = 6,
    [4] = 5,
    [5] = 4,
    [6] = 3,
    [7] = 2,

}
TIME_MDAY_DEFINE = {
	[1] = 2,
	[2] = 3,
	[3] = 4,
	[4] = 5,
	[5] = 6,
	[6] = 7,
	[7] = 8,
	[8] = 9,
	[9] = 10,
	[10] = 11,
	[11] = 12,
	[12] = 1,
}
function TimeManager:ctor()
    self.CS_UE_TIME = UE.Time

    self.m_ServerTimeStampLast = 0
    self.m_ServerTimeStamp = 0
    self.m_LocalStamp = 0
    self.m_TickFrequency = 0
    --Timer
    self.m_MapTimers = {}
end

function TimeManager:Cleanup()
    self.m_ServerTimeStampLast = 0
    self.m_ServerTimeStamp = 0
    self.m_LocalStamp = 0
    self.m_TickFrequency = 0
    --Timer
    self.m_MapTimers = {}
end

function TimeManager:Initialize()
    self:ResetTimeStamp(os.time())
end

function TimeManager:Tick(deltaTime)
    self:TickTimer(deltaTime)

    if self.m_TickFrequency >= 0.2 then
        self:UpdateServerStamp()
        self.m_TickFrequency = 0.0
    end
    self.m_TickFrequency = self.m_TickFrequency + deltaTime
end

function TimeManager:ResetTimeStamp(serverStamp)
    serverStamp = math.floor(serverStamp)
    self.m_ServerTimeStamp = serverStamp
    self.m_ServerTimeStampLast = serverStamp
    self.m_TickFrequency = 0
    self.m_LocalStamp = self.CS_UE_TIME.realtimeSinceStartup
end

function TimeManager:GetServerTime()
    return self.m_ServerTimeStamp
end
--当前的时间戳
function TimeManager:GetServerTimestamp()
    return self.m_ServerTimeStamp
end

function TimeManager:SetServerTimestampAndZone(serverStamp, serverZone)
    TimeZoneMgr:SetupServerStampAndZone(serverStamp, serverZone)
    --TimeZoneMgr:Test()
    self:ResetTimeStamp(serverStamp)
end

function TimeManager:UpdateServerStamp()
    local diffFromLast = self.CS_UE_TIME.realtimeSinceStartup - self.m_LocalStamp
    self.m_ServerTimeStamp = self.m_ServerTimeStampLast + math.floor(diffFromLast)
    TimeZoneMgr:SetupServerStampAndZone(self.m_ServerTimeStamp)
    TimeZoneMgr:CheckZeroClock()
end

--两个时间戳相差几天
function TimeManager:DiffDay(stampA, stampB)
	assert(stampA ~= nil, "error:stampA=nil")
	assert(stampB ~= nil, "error:stampB=nil")
	--local dateA = os.date("!*t", stampA)
	--local dateB = os.date("!*t", stampB)
	local diff = stampA - stampB -- os.difftime(os.time(dateA), os.time(dateB))
	local diff_days = diff / OneDaySeconds
	return math.floor(diff_days)
	--return dateA.yday - dateB.yday
end

function TimeManager:GetDateNum(timeNow, timeNext)
	
	
	local ret = 0
	if timeNow and timeNext then
		local now = os.date("*t", timeNow)
		local next = os.date("*t", timeNext)

		if now and next then
			local num1 = os.time({ year = now.year, month=now.month, day=now.day })
			local num2 = os.time({ year = next.year, month=next.month, day=next.day })
			if num1 and num2 then
				ret =  math.abs(num1 - num2) / (3600*24)
			end
		end
	end
	return math.modf(ret + 1)
end

------------------------------------------------------------------------------------------------------------------------------------------
----Time Tools
----
function TimeManager:intervalToTime(seconds)
    if seconds < 0 then
        seconds = 0
    end
    local sVal = 0
    local day = math.floor(math.abs(seconds / (24 * 3600)))
    sVal = seconds % (24 * 3600)
    local hour = math.floor(math.abs(sVal / 3600))
    sVal = sVal % 3600
    local minute = math.floor(math.abs(sVal / 60))
    sVal = sVal % 60
    local second = sVal
    local buff = ""

    if day > 0 then
        buff = string.format("%02dd%02dh%02dm%02ds", day, hour, minute, second)
    elseif hour > 0 then
        buff = string.format("%02dh%02dm%02ds", hour, minute, second)
    else
        if minute > 0 then
            buff = string.format("%02dm%02ds", minute, second)
        elseif second > 0 then
            buff = string.format("%02ds", second)
        else
            buff = "00s"
        end
    end
    return buff
end

function TimeManager:StampToDateDes(timeStamp)
    local date = TimeZoneMgr:GetServerDate(timeStamp)
    return string.format("%d-%02d-%02d %02d:%02d:%02d", date.year, date.month, date.day, date.hour, date.min, date.sec)
end

function TimeManager:DateDesToStamp(strDate)
    if IsNilOrEmpty(strDate) then
        return
    end
    local _, _, y, m, d, _hour, _min, _sec = string.find(strDate, "(%d+)/(%d+)/(%d+)%s*(%d+):(%d+):(%d+)")

    --local timeStamp = os.time({year=y, month = m, day = d, hour = _hour, min = _min, sec = _sec})
    local timeStamp = TimeZoneMgr:GetServerClockStampByDate({ year = y, month = m, day = d, hour = _hour, min = _min, sec = _sec, isdst = false })
    return timeStamp
end

---------------------------------------------------- Zhai

function TimeManager:TimeToMS(time)
    if time >= 0 then
        return os.date("%M:%S", time)
    else
        Log.Info("TimeToMS : " .. time)
    end
end

function TimeManager:TimeToHMS(timeStamp)
    timeStamp = timeStamp or self.m_ServerTimeStamp
    local timeData = os.date("%H:%M:%S", timeStamp)
    return timeData
end

function TimeManager:GetHMS(timeStamp)
    if timeStamp >= oneHour then
        local hour = math.floor(timeStamp / oneHour)
        timeStamp = timeStamp - hour * oneHour
        local minute = math.floor(timeStamp / oneMinute)
        timeStamp = math.floor(timeStamp - minute * oneMinute)
        return string.format("%d:%02d:%02d", hour, minute, timeStamp)
    else
        local minute = math.floor(timeStamp / oneMinute)
        timeStamp = math.floor(timeStamp - minute * oneMinute)
        return string.format("%02d:%02d", minute, timeStamp)
    end
end

function TimeManager:CheckHMS(timeValue)
    if timeValue >= OneDaySeconds then
        local day = math.floor(timeValue / OneDaySeconds)
        timeValue = timeValue - day * OneDaySeconds
        local hour = math.floor(timeValue / oneHour)
        return day .. LangMgr:GetLang(2040) .. " " .. hour .. LangMgr:GetLang(2041)
    elseif timeValue > oneHour then
        local hour = math.floor(timeValue / oneHour)
        timeValue = timeValue - hour * oneHour
        local minute = math.floor(timeValue / oneMinute)
        return string.format("%d%s%02d%s", hour, LangMgr:GetLang(2041), minute, LangMgr:GetLang(2042))
    else
        local minute = math.floor(timeValue / oneMinute)
        timeValue = timeValue - minute * oneMinute
        return string.format("%02d%s%02d%s", minute, LangMgr:GetLang(2042), timeValue, LangMgr:GetLang(2043))
    end
end

function TimeManager:CheckHMSNotEmpty(timeValue)
    timeValue = timeValue < 0 and 0 or timeValue
    if timeValue >= OneDaySeconds then
        local day = math.floor(timeValue / OneDaySeconds)
        timeValue = timeValue - day * OneDaySeconds
        local hour = math.floor(timeValue / oneHour)
        return day .. LangMgr:GetLang(2040) .. hour .. LangMgr:GetLang(2041)
    elseif timeValue > oneHour then
        local hour = math.floor(timeValue / oneHour)
        timeValue = timeValue - hour * oneHour
        local minute = math.floor(timeValue / oneMinute)
        return string.format("%d%s%02d%s", hour, LangMgr:GetLang(2041), minute, LangMgr:GetLang(2042))
    else
        local minute = math.floor(timeValue / oneMinute)
        timeValue = timeValue - minute * oneMinute
        return string.format("%02d%s%02d%s", minute, LangMgr:GetLang(2042), timeValue, LangMgr:GetLang(2043))
    end
end

function TimeManager:CheckHMSNotEmptyFriend(timeValue) --9005 刚刚
	timeValue = timeValue < 0 and 0 or timeValue
	if timeValue >= OneDaySeconds then
		local day = math.floor(timeValue / OneDaySeconds)
        day = math.min(day, 100)
		return LangMgr:GetLangFormat(9007, day)
	elseif timeValue > oneHour then
		local hour = math.floor(timeValue / oneHour)
		return LangMgr:GetLangFormat(9006, hour)
	else
		local minute = math.floor(timeValue / oneMinute)
		--timeValue = timeValue - minute * oneMinute
		return LangMgr:GetLang(9005)
	end
end

function TimeManager:CutBuyWorkTime(timeValue)
    if timeValue < 0 then
        timeValue = 0
    end
    if timeValue >= OneDaySeconds then
        local day = math.floor(timeValue / OneDaySeconds)
        timeValue = timeValue - day * OneDaySeconds
        local hour = math.floor(timeValue / oneHour)
        timeValue = timeValue - hour * oneHour
        local minute = math.floor(timeValue / oneMinute)
        return day .. LangMgr:GetLang(2040) .. hour .. LangMgr:GetLang(2041) .. string.format("%02d%s", minute, LangMgr:GetLang(2042))
    else
        local hour = math.floor(timeValue / oneHour)
        timeValue = timeValue - hour * oneHour
        local minute = math.floor(timeValue / oneMinute)
        timeValue = timeValue - minute * oneMinute
        return string.format("%d%s%02d%s%02d%s", hour, LangMgr:GetLang(2041), minute, LangMgr:GetLang(2042), timeValue, LangMgr:GetLang(2043))
    end
end

function TimeManager:ConverSecondToString(cdTime)
    --秒数=>格式(0天或00:00:00)
    if tonumber(cdTime) <= 0 then
        return "00:00:00"
    end

    local day = math.floor(cdTime / 86400)
    local hour = math.floor(cdTime % 86400 / 3600)
    local min = math.floor(cdTime % 3600 / 60)
    local second = math.floor(cdTime % 60)
    return string.format("%02d:%02d:%02d", math.floor(cdTime / 3600), min, second)
end

function TimeManager:IsSameDay(stampA, stampB)
    assert(stampA ~= nil, "error:stampA=nil")
    assert(stampB ~= nil, "error:stampB=nil")
    local dateA = os.date("*t", stampA)
    local dateB = os.date("*t", stampB)

    return dateA.day == dateB.day and dateA.month == dateB.month and dateA.year == dateB.year
end

function TimeManager:GetSpecialMS(timeStamp)
    if timeStamp <= 0 then
        timeStamp = 0
    end
    if timeStamp >= oneHour then
        local hour = math.floor(timeStamp / oneHour)
        timeStamp = timeStamp - hour * oneHour
        local minute = math.floor(timeStamp / oneMinute)
        return string.format("%dh%dm", hour, minute)
    else
        local minute = math.floor(timeStamp / oneMinute)
        timeStamp = math.floor(timeStamp - minute * oneMinute)
        return string.format("%dm%ds", minute, timeStamp)
    end
end

------------------
--- TIMER PART
-------------------------------------------------------------------------------------------------------------------
function TimeManager:CreateTimerSingle(uid, callBack, intervalTime, doTimes, ...)
    local mapKey = tostring(uid)
    if self.m_MapTimers[mapKey] ~= nil then
        return self.m_MapTimers[mapKey].timerId, false
    end
    return self:CreateTimer(uid, callBack, intervalTime, doTimes, ...), true
end

function TimeManager:CreateTimerSingleOne(uid, callBack, intervalTime, doTimes, ...)
    local mapKey = tostring(uid)
    local arr = self.m_MapTimers[mapKey]
    if arr ~= nil then
        if arr.timerData[arr.timerId] then
            return arr.timerId, false
        end
    end
    return self:CreateTimer(uid, callBack, intervalTime, doTimes, ...), true
end

function TimeManager:CreateTimer(uid, callBack, intervalTime, doTimes, ...)
    return self:CreateTimerT(uid, nil, callBack, intervalTime, doTimes, ...), true
end

function TimeManager:CreateTimerT(uid, target, callBack, intervalTime, doTimes, ...)
    doTimes = doTimes or -1

    local mapKey = tostring(uid)
    local arr = self.m_MapTimers[mapKey]
    if arr == nil then
        arr = {}
        arr.timerId = 0
        arr.timerData = {}
    end

    local newId = arr.timerId + 1
    local timerKey = tostring(newId)
    arr.timerId = newId

    local timeObj = TimeObject.new()
    timeObj._owner = uid
    timeObj._intervalTime = intervalTime
    timeObj._doTimes = doTimes
    timeObj._tickFunc = callBack
    timeObj._target = target

    local p = { ... }
    if table.count(p) > 0 then
        timeObj._param = p
    end

    if doTimes < 0 then
        timeObj:DoFun()
    end

    arr.timerData[timerKey] = timeObj

    self.m_MapTimers[mapKey] = arr
    return newId
end

function TimeManager:DestroyTimerByFun(uid, fun)
    local mapKey = tostring(uid)
    if not fun then
        Log.Error("###### DestroyTimerByFun:", uid, fun)
        return
    end
    local timerMap = self.m_MapTimers[mapKey]
    if timerMap then
        for k, v in pairs(timerMap.timerData) do
            if v._tickFunc == fun then
                v._needDestroy = true
                break
            end
        end
    end
end

function TimeManager:DestroyTimer(uid, timerId)
    local mapKey = tostring(uid)
    local timerMap = self.m_MapTimers[mapKey]
    if timerMap then
        for k, v in pairs(timerMap.timerData) do
            if timerId == nil or tostring(timerId) == k then
                v._needDestroy = true
            end
        end
    end
end


function TimeManager:IsHasTimer(uid)
	local mapKey = tostring(uid)
	 
 	if self.m_MapTimers[mapKey] then
	 	return true
	 end 
  
	return false
end
function TimeManager:DeleteTimer(uid, timerId)
    --   timerId = timerId or nil
    local mapKey = tostring(uid)
    if not timerId then
        self.m_MapTimers[mapKey] = nil
        return
    end

    timerId = tostring(timerId)
    local timerMap = self.m_MapTimers[mapKey]
    if timerMap then
        if timerMap.timerData[timerId] then
            timerMap.timerData[timerId] = nil
        end
        if table.count(timerMap.timerData) == 0 then
            self.m_MapTimers[mapKey] = nil
        end
    end
end

function TimeManager:TickTimer(deltaTime)
    if not self.m_MapTimers then
        return
    end

    local tbDel
    for uid, inf in pairs(self.m_MapTimers) do
        for timeId, time in pairs(inf.timerData) do
            if time:IsFinished() then
                tbDel = tbDel or {}
                table.insert(tbDel, { uid, timeId })
            else
                time:Tick(deltaTime)
            end
        end
    end

    if tbDel then
        for k, v in pairs(tbDel) do
            self:DeleteTimer(v[1], v[2])
        end
    end
    tbDel = nil
end

--------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---
---
local MINUTE_TIME_UNIT = 60
local HOUR_TIME_UNIT = 3600
local DAY_TIME_UNIT = 86400
local TIME_MODE_TYPE = {
    THREE_TIME_MODE = 1, -- hour , minute , second
    FOUR_TIME_MODE = 2, -- day  , hour , minute , second
    SPACE_TIME_MODE = 3     -- day  , hour , minute , second , space
}

function TimeManager:GetTimeFormatTable(time, timeMode)
    timeMode = timeMode or TIME_MODE_TYPE.THREE_TIME_MODE
    local d = math.floor(time / DAY_TIME_UNIT)
    local h = math.floor(time % DAY_TIME_UNIT / HOUR_TIME_UNIT)
    local m = math.floor(time % DAY_TIME_UNIT % HOUR_TIME_UNIT / MINUTE_TIME_UNIT)
    local s = time % MINUTE_TIME_UNIT

    local function formatTime(langId, isSpace)
        if isSpace then
            return "%02d" .. LangMgr:GetLang(langId) .. " "
        end

        return "%02d" .. LangMgr:GetLang(langId)
    end

    local timeT = { d, h, m, s }
    if timeMode == TIME_MODE_TYPE.THREE_TIME_MODE then
        local matchT = {}
        for i = 1, 3 do
            table.insert(matchT, formatTime(2040 + i))
        end
        return { h, m, s }, matchT
    end

    if timeMode == TIME_MODE_TYPE.SPACE_TIME_MODE then
        local matchT = {}
        for i = 1, 4 do
            table.insert(matchT, formatTime(2039 + i, i < 4))
        end
        return timeT, matchT
    end

    local mt = {}
    for i = 1, 4 do
        table.insert(mt, formatTime(2039 + i))
    end
    return timeT, mt
end

---@time 时间差
---@sc  显示多少个时间单位 ps: sc = 2, 01d02h
function TimeManager:BaseTime(time, sc, timeMode)
    local timeT, matchT = self:GetTimeFormatTable(time, timeMode)
    local count = GetTableLength(timeT)
    local timeBuff = ""
    local timeIndex = 0
    for i = 1, count do
        if timeT[i] > 0 or timeIndex > 0 then
            timeBuff = timeBuff .. string.format(matchT[i], timeT[i])
            timeIndex = timeIndex + 1
        end
        if timeIndex > sc - 1 then
            break
        end
    end
    return timeBuff
end

---@time 时间差
---@sc  显示多少个时间单位 ps: sc = 2, 01d02h
function TimeManager:CutDownTime(time, sc)
    local timeBuff = self:BaseTime(time, sc, TIME_MODE_TYPE.FOUR_TIME_MODE)
    return timeBuff
end

---@time 时间差
---@sc  显示多少个时间单位 ps: sc = 2, 01d02h
function TimeManager:CutDownTimeSpace(time, sc)
    local timeBuff = self:BaseTime(time, sc, TIME_MODE_TYPE.SPACE_TIME_MODE)
    return timeBuff
end

return TimeManager
local UI_Guide = Class(BaseView)

function UI_Guide:OnInit()
    self.m_PosTipFromY = self.ui.m_rtransDes.anchoredPosition.y
    self.m_PosTipToY = self.m_PosTipFromY + self.ui.m_rtransDes.rect.height
end

function UI_Guide:OnCreate(guideController, isFromStart, param1, param2)
    if guideController then
        guideController:AfterUIOpen(self, isFromStart, param1, param2)
    end
end

function UI_Guide:onDestroy()
end

function UI_Guide:OnRefresh(eType, inf)
    if eType == 0 then

    elseif eType == 1 then--tip
        self:SetupTip(inf.tipInf)

    elseif eType == 2 then--finger
        if inf then
            local fInfo = inf.fingerInfo
            if fInfo then
                local posFrom = GuideController:GetCacheItemPosV3(fInfo.from_id)
                if posFrom then
                    fInfo.pos_from = posFrom
                end
                local posTo = GuideController:GetCacheItemPosV3(fInfo.to_id)
                if posTo then
                    fInfo.pos_to = posTo
                end
            end
        end
        self:SetupHandAction(inf.fingerInfo, inf.cameraPos, inf.zoom)

    elseif eType == 3 then--arrow
        self:SetupArrow(inf)

    elseif eType == 4 then--hint
        self:SetupHint(inf.hintInf)
    elseif eType == 5 then
        self:SetupMask(inf)

    elseif eType == 6 then
        if inf.isUITop == 1 then
            UIMgr:SetViewToForemost(self)
        end

    elseif eType == 7 then
        if inf.skipInf then
            local trans = self.ui.m_btnSkip.transform:GetComponent(typeof(UE.RectTransform))
            trans.anchoredPosition = inf.skipInf.skipPos
            SetActive(self.ui.m_btnSkip, true)
        else
            SetActive(self.ui.m_btnSkip, false)
        end
    elseif eType == 10 then
        self:SetupHandAction()
	elseif eType == 11 then
		local obj = self.ui[inf.path].transform.gameObject
		SetActive(obj, true)
		local pos = MapController:GetUIPosByGrid(inf.x,inf.y)
		SetUIPos(obj, pos.x, pos.y)
		if inf.type == 5 then
			obj.transform:SetLocalScale(0.4,0.4,0.4)
			DOScale(obj.transform, Vector3.New(1.1,1.1,1.1), 0.3, function ()
					DOScale(obj.transform, Vector3.New(1,1,1), 0.1, nil, Ease.OutBack)
				end, Ease.OutBack)
		end
        UIMgr:SetUILock(false,"UI_Guide")

        --MapController:SetTouchLockState(0)
		
	elseif eType == 12 then
		self:SetupUICircleMask(inf)
	elseif eType == 13 then
		self:SetGirdDlg(inf)
	elseif eType == 14 then
		self:SetDynamic(inf)
    end
end

function UI_Guide:SetDynamic(inf)

	local curMap = v2s(MapController.m_MapId)

	inf.dynamicX = nil
	inf.dynamicY = nil
	if inf.dynamic and inf.dynamic[curMap] then
		local path = inf.dynamic[curMap]
		local data = Split1(path,"|")
		if data[1] and data[2] then
			local view = UIMgr:GetUIItem(data[1])
			if view then
				local targetGo = GetChild(view.uiGameObject,data[2],UE.RectTransform)
				if targetGo then
					inf.dynamicX = targetGo.transform.position.x
					inf.dynamicY = targetGo.transform.position.y
				end
			end
		end
	end
end

function UI_Guide:SetupHandAction(inf, caPos, zoom)
    local transHand = self.ui.m_rtransHandGuide

    local scale = 1
    if not zoom or zoom == 0 then
		if not GuideController:IsTinyGameNotMap(inf) then
     	 	scale = MapController:GetCameraScaleFromNormal()
		end
    else
        scale = MapController:GetCameraScaleFromNormal(zoom)
    end
    transHand.transform:SetLocalScale(scale, scale, 1)

    if self.m_TweenIdHand then
        TweenMgr:DOKill(self.uiName, self.m_TweenIdHand)
    end

    if not inf then
        SetActive(transHand, false)
    else
        SetActive(transHand, true)
        local isUIPos = (inf.id < 0)
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, math.abs(inf.id))
        if config then
            local function onPivot(sp)
                if sp then
                    local x = sp.pivot.x - sp.rect.width / 2
                    local y = sp.pivot.y - sp.rect.height / 2 + 100
                    if self.ui then
                        self.ui.m_imgHandItemIcon.rectTransform.anchoredPosition = Vector2(-x, -y)
                    end
                end
            end

            SetUIImage(self.ui.m_imgHandItemIcon, config["img"], true, onPivot)
            local vtScale = GetVector3ByStr(config["scale"])
            if vtScale then
                self.ui.m_imgHandItemIcon.transform:SetLocalScale(vtScale.x, vtScale.y, vtScale.z)
            end
        end

        local pStart = nil
        local pFrom = nil
        local pTo = nil
        if isUIPos then
            local anchor = tonumber(inf.anchor)
            pStart = GuideConfig:ConvertPos(inf.pos_start, anchor)
            pFrom = GuideConfig:ConvertPos(inf.pos_from, anchor)
            pTo = GuideConfig:ConvertPos(inf.pos_to, anchor)

        else
            pStart = self:TransformVt3ToLocal(inf.pos_start, caPos)
            pFrom = self:TransformVt3ToLocal(inf.pos_from, caPos)
            pTo = self:TransformVt3ToLocal(inf.pos_to, caPos)
        end
        transHand.anchoredPosition = pStart

        local tween, tId = TweenMgr:CreateSequence(UIDefine.UI_Guide, false, nil)

        tween:Append(transHand:DOAnchorPos(pFrom, GlobalConfig.TIME_GUIDE_HAND_0))
        tween:Append(transHand:DOAnchorPos(pTo, GlobalConfig.TIME_GUIDE_HAND_2):SetDelay(GlobalConfig.TIME_GUIDE_HAND_1))
        tween:AppendInterval(GlobalConfig.TIME_GUIDE_HAND_3)

        tween:SetLoops(-1, LoopType.Restart)

        self.m_TweenIdHand = tId
    end
end

function UI_Guide:SetupArrow(infoBase)
	local inf = nil
	if infoBase then
		inf = infoBase.arrowInf
	end
    local arrowTrans = self.ui.m_rtransArrowPos
    if not inf then
        SetActive(arrowTrans, false)
	 	if not GuideController:IsTinyGameNotMap(inf) then
	        MapController:ForceDisappearMarkArrow("for_tutorial")
		end
        self.ui.m_rtransArrowAct:DOKill()
    else
        local type = inf.type or 0
        local dir = inf.dir or 2
        local pos = inf.pos

        if dir > 8 or dir < 0 then
            self:SetupArrow(nil)
            Log.Error("###### arrow dir error, check excel !!!")
            return
        end

        --dir:1上,2下,3左,4右,5左上,6左下,7右上,8右下
        local arrAngles = { -180, 0, -90, 90, -135, -45, 135, 45 }
        local angle = arrAngles[dir]

        if type == 0 then
            MapController:ForceDisappearMarkArrow("for_tutorial")
            MapController:MarkArrowOnGround("for_tutorial", Vector3.New(pos.x, pos.y, 0), angle, true)
        else

			if infoBase.dynamicX and infoBase.dynamicY then
				arrowTrans.transform.position =  Vector3.New(infoBase.dynamicX,infoBase.dynamicY,arrowTrans.transform.position.z)
				if not infoBase.dynamicAnchoredX then infoBase.dynamicAnchoredX = arrowTrans.anchoredPosition.x end
				if not infoBase.dynamicAnchoredY then infoBase.dynamicAnchoredY = arrowTrans.anchoredPosition.y end
				pos.x = pos.x + arrowTrans.anchoredPosition.x
				pos.y = pos.y + arrowTrans.anchoredPosition.y
			end

            arrowTrans.anchoredPosition = pos
            arrowTrans.localEulerAngles = Vector3.New(0, 0, angle)
			
			self.ui.m_rtransArrowAct:DOKill()
			self.ui.m_rtransArrowAct.anchoredPosition = Vector2.New(24,0)
            self.ui.m_rtransArrowAct:DOAnchorPos(Vector2.New(24, 20), 0.5):SetLoops(-1, LoopType.Yoyo)
            SetActive(arrowTrans, true)
        end
    end
end

function UI_Guide:SetupTip(inf, isAnim)
    if inf == nil or inf.type == 0 then
        local function onCloseDes()
            if self.isShow then
                SetActive(self.ui.m_rtransDes, false)
                SetActive(self.ui.m_imgDesWall, false)
            end
        end
        if inf then
            onCloseDes()
        else
            --local fPos = Vector2.New(self.ui.m_rtransDes.anchoredPosition.x, self.m_PosTipFromY)
            --local tPos = Vector2.New(self.ui.m_rtransDes.anchoredPosition.x, self.m_PosTipToY)
            --self.ui.m_rtransDes.anchoredPosition = tPos
            --self.ui.m_rtransDes:DOAnchorPos(fPos, 0.5):SetEase(Ease.OutBack):OnComplete(onCloseDes)
			onCloseDes()
        end
        return
    end
    if inf.type == 3 then
        UI_SHOW(UIDefine.UI_GuideDes, inf.param)
        return
    end
    SetActive(self.ui.m_rtransDes, true)
    SetActive(self.ui.m_imgDesWall, true)

    --local fPos = Vector2.New(inf.x, self.m_PosTipFromY)
    local tPos = Vector2.New(inf.x, inf.y)
	--Log.Error("tPos",tPos)
    self.ui.m_rtransDes.anchoredPosition = tPos
    --self.ui.m_rtransDes:DOAnchorPos(tPos, 0.5):SetEase(Ease.OutBack)

    self.ui.m_txtTip.text = LangMgr:GetLang(inf.param)
    if inf.type == 2 then
        SetActive(self.ui.m_btnTip, true)
        SetUIImgEnable(self.ui.m_imgDesWall, true)
    else
        SetActive(self.ui.m_btnTip, false)
        SetUIImgEnable(self.ui.m_imgDesWall, false)
    end

    local iconIndex = inf.iconIndex or 0
    local childCount = self.ui.m_goHeads.transform.childCount
	for i = 0, childCount - 1 do
		local child =  GET_UI_CHILD(self.ui.m_goHeads.transform , i , "RectTransform")
		SetActive(child , i == iconIndex)
	end

end

function UI_Guide:SetupHint(inf)
    if not inf then
        self.ui.m_rtransHint:DOKill()
        SetActive(self.ui.m_rtransHint, false)
        return
    else
        SetActive(self.ui.m_rtransHint, true)
        self.ui.m_rtransHint.transform:SetLocalScale(1, 1, 1)
        self.ui.m_rtransHint:DOKill()
        self.ui.m_rtransHint:DOScale(1.05, 1):SetLoops(-1, LoopType.Yoyo)
    end
 	local config1 = ItemConfig:GetDataByID(inf.id1)
 	local config2 = ItemConfig:GetDataByID(inf.id2)
	if not config1 then 
		self.ui.m_txtHint.text = LangMgr:GetLangFormat(inf.str)
	elseif not config2 then 
    	self.ui.m_txtHint.text = LangMgr:GetLangFormat(inf.str, LangMgr:GetLang(config1["id_lang"]))   
	else
	 	self.ui.m_txtHint.text = LangMgr:GetLangFormat(inf.str, LangMgr:GetLang(config1["id_lang"]),LangMgr:GetLang(config2["id_lang"]))
	end
    --local config1 = ItemConfig:GetDataByID(inf.id1)
    --if config1 then
        --local function enableMe()
            --SetActive(self.ui.m_imgHint1, true)
        --end
        --SetActive(self.ui.m_imgHint1, false)
        --SetUIImage(self.ui.m_imgHint1, config1["icon_b"], false, enableMe)
        --SetUIPosByVector2(self.ui.m_imgHint1, inf.pos1)
    --else
        --SetActive(self.ui.m_imgHint1, false)
    --end
	
	SetActive(self.ui.m_imgHint1, false)

    --local config2 = ItemConfig:GetDataByID(inf.id2)
    --if config2 then
        --local function enableMe()
            --SetActive(self.ui.m_imgHint2, true)
        --end
        --SetActive(self.ui.m_imgHint2, false)
        --SetUIImage(self.ui.m_imgHint2, config2["icon_b"], false, enableMe)
        --SetUIPosByVector2(self.ui.m_imgHint2, inf.pos2)
    --else
        --SetActive(self.ui.m_imgHint2, false)
    --end
	
	SetActive(self.ui.m_imgHint2, false)
    UIRefreshLayout(self.ui.m_rtransHint)
end

function UI_Guide:SetupMask(infoBase)
	local inf = infoBase.maskInf
    --(inf.pos, inf.w, inf.h)
    if not inf then
        SetActive(self.ui.m_graphicMask, false)
        return
    end
    SetActive(self.ui.m_graphicMask, true)

    self.ui.m_graphicMask.raycastTarget = true
    self.ui.m_btnGuide.interactable = true
    local trans = self.ui.m_btnGuide.transform:GetComponent(typeof(UE.RectTransform))

	local pos = inf.pos
	
	if infoBase.dynamicX and infoBase.dynamicY then
		trans.transform.position = Vector3.New(infoBase.dynamicX,infoBase.dynamicY,trans.transform.position.z)
		if not infoBase.dynamicAnchoredX then infoBase.dynamicAnchoredX = trans.anchoredPosition.x end
		if not infoBase.dynamicAnchoredY then infoBase.dynamicAnchoredY = trans.anchoredPosition.y end
		
		pos.x = trans.anchoredPosition.x
		pos.y = trans.anchoredPosition.y
	end

    trans.anchoredPosition = pos
    trans.sizeDelta = inf.size

    self.ui.m_graphicMask.color = Color.New(0, 0, 0, inf.opacity)
end


function UI_Guide:TransformVt3ToLocal(vt3, caPos)
    local scrPos = UE.RectTransformUtility.WorldToScreenPoint(MapController.m_Camera, vt3)
    local _, pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.uiRectTransform, scrPos, UIMgr.uiCamera)
    return pos
end

function UI_Guide:onUIEventClick(go, param)
    local name = go.name
    if name == "m_btnTip" then
        self:SetupTip(nil)

        local function onDoneGuideClose()
            EventMgr:Dispatch(EventID.EVENT_GUIDE, 3)
        end
        TimeMgr:CreateTimer(self, onDoneGuideClose, 0.5, 1)

    elseif name == "m_btnGuide" then
		local isMask = false
		local curGuide = GuideController:GetCurGuideInf()
		if curGuide and curGuide.stepInf and (curGuide.stepInf.typeId == 102 or curGuide.stepInf.typeId == 9) then
			--临时处理-- 修改102为不能放开遮罩
			isMask = true
		end
        self.ui.m_btnGuide.interactable = isMask
        self.ui.m_graphicMask.raycastTarget = isMask

        local pos = self.ui.m_btnGuide.transform.position
        local scrPos = UE.RectTransformUtility.WorldToScreenPoint(UIMgr.uiCamera, pos)
        local res = TouchMonoMgr:SimulationTouch(scrPos.x, scrPos.y)
        --maybe ui is nil, because touch is closing ui
        Log.Info("******* simulation : ", res, pos.x, pos.y, scrPos.x, scrPos.y)
        EventMgr:Dispatch(EventID.EVENT_GUIDE, 5)

    elseif name == "m_btnSkip" then
        SetActive(self.ui.m_btnSkip, false)
        GuideController:SkipGuide()
	elseif name == "Build" then
		GuideController:StepGuide(GuideController.m_GuideInfNow.id)
		SetActive(self.ui["m_rtransBuild"], false)
	elseif name == "m_btnIcon" then
		SetActive(self.ui["m_rtransIcon"], false)
		SetActive(self.ui["m_rtransArrowPos"],false)
		--GuideController:StepGuide(GuideController.m_GuideInfNow.id)
		TinyGameMgr:SetStageID(5)
		TinyGameMgr:EnterTgStageInMainScene(true)
    end
end

function UI_Guide:SetupUICircleMask(inf)
	SetActive(self.ui.m_imgMask,false)
	if not inf then
		return
	end
	if not inf.ui_circle then
		return 
	end
	local circle = inf.ui_circle
	local mask = inf.maskInf
	local centerX = circle.x
	local centerY = circle.y
	if mask then
		centerX = mask.pos.x
		centerY = mask.pos.y
        if Game.IsWechatGame() then
            local t =  (UIHeight - GameUtil.SafeAreaTop)  / UIHeight
            centerY  = centerY  - GameUtil.SafeAreaTop/2 *  t
        end
	end
	if inf.dynamicAnchoredX and inf.dynamicAnchoredY then
		centerX = inf.dynamicAnchoredX
		centerY = inf.dynamicAnchoredY
	end


	SetActive(self.ui.m_imgMask,true)
	self.m_material = self.ui.m_imgMask.material
	self.m_material:SetVector(UE.Shader.PropertyToID("_Rect"), Vector4.New())
	self.m_material:SetVector(UE.Shader.PropertyToID("_Center"), Vector4(
			centerX or 0,
			centerY or 0,
			0, 0))
	
	if circle.type == 1 then
		--self.m_material:SetFloat(UE.Shader.PropertyToID("_Slider"), inf.slider)
	--elseif inf.type == 2 then
		self.m_material:SetFloat(UE.Shader.PropertyToID("_Slider"), circle.slider+600)
		function OnScale(value)
			self.m_material:SetFloat(UE.Shader.PropertyToID("_Slider"), value)
		end
		Tween.Kill("UI_Guide")
		local dotween = Tween.To(OnScale, circle.slider+600, circle.slider, 0.5):SetId("UI_Guide")
	end
end

function UI_Guide:SetGirdDlg(inf)
	if not inf then
		SetActive(self.ui.m_imgGridDlgWall, false)
		return
	end
	
	local tPos = Vector2.New(inf.x, inf.y)
	self.ui.m_rtransGridDlg.anchoredPosition = tPos
	
	self.ui.m_txtGridDlgTip.text = LangMgr:GetLang(inf.langID)
	
	local imgHead = GetChild(self.ui.m_goGridDlgHeads, "Image", UEUI.Image)
	local strHead = ""
	if inf.headID then
		local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, inf.headID)
		if headConfig then
			strHead = headConfig.icon
		end
	end
	if not IsNilOrEmpty(inf.strHeadPath) then
		strHead = inf.strHeadPath
	end
	SetUIImage(imgHead, strHead, false)

	
	SetActive(self.ui.m_imgGridDlgWall, true)
end

return UI_Guide
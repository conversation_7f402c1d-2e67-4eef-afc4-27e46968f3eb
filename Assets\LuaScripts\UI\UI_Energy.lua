local UI_Energy = Class(BaseView)

function UI_Energy:OnInit()	
	self.shopList = nil
	
	--刮刮卡的轮询
	self.mCardLoopId = 0
	
	--全局界面的轮询
	self.mGLoopId = 0
	
	--可以处理的Item数量最大者
	self.mItemCount = 3
	
	--小元素的轮询
	self.ItemLoop = {}

	self.notEnoughNum = nil
end


function UI_Energy:OnCreate(param,isFromFind,notEnoughNum)
	if notEnoughNum then
		self.notEnoughNum = notEnoughNum
	end
	--更新运行时的状态数据
	NetEnergyLotteryData:OnClearComeType()
	NetEnergyLotteryData:OnResetComeType(param)
	--这里是更新主界面 按钮的状态
	--local cur_energy = EnergyLotteryModule:GetCurScopeEnergyType()
	UI_UPDATE(UIMgr:GetNowMainUI(),12,{isShow = true,list={PlayerDefine.Coin,PlayerDefine.Energy}})
	
	NetNotification:NotifyNormal(NotifyDefine.OpenEnergy)
	
	--高级礼包 的价格刷新的剩余时间
	--self.refreshTime = GetChild(self.uiGameObject, "m_imgRefreshTime/m_txtRefreshTime", UEUI.Text)
	--SetActive(self.refreshTime , false)
	--SetActive(self.ui.m_imgRefreshTime , false)
	--免费所在的箭头
	self.showRow = GetChild(self.uiGameObject, "bg/showRow", UEUI.Image)
	SetActive(self.showRow , false)
	
	--先关闭ScrollView的功能
	--self.ui.m_scrollview.enabled = false
	--自动扩容的部分
	self.contentSizeFitter = GET_UI(self.ui.m_scrollview, "m_Content", "ContentSizeFitter")
	self.contentRect = GET_UI(self.ui.m_scrollview, "m_Content", "RectTransform")
	--self.contentSizeFitter.enabled = false
	--Mask
	--self.viewMask = GET_UI(self.ui.m_scrollview, "Viewport", "Mask")
	--self.viewMask.enabled = false
	--ContentSize
	self.m_contentSize = GET_UI(self.ui.m_scrollview, "m_contentSize", "RectTransform")
	
	
	
	--预制体
	self.shopPrefab = GetChild(self.uiGameObject,"bg/shop_preab")
	self.specialObj = GetChild(self.uiGameObject,"bg/specialObj")
	self.specialBtn = GetChild(self.specialObj,"btnObj")
	SetActive(self.shopPrefab , false)
	--实例化的父节点
	self.shopParent = GetChild(self.uiGameObject,"bg/TbObj/m_scrollview/Viewport/m_Content")
	
	--全局更新使用
	self.m_txtRefreshTime = self.ui.m_txtRefreshTime--GetChild(self.uiGameObject,"GameObject/bg/m_imgRefreshTime/m_txtRefreshTime" , UEUI.Text)
	self.txtLang = LangMgr:GetLang(43)
	
	self.shopList = nil
	self.shopList = {}
		
	EventMgr:Add(EventID.AD_STATE, self.OnAdStateChange, self)
	
	if self.mGLoopId > 0 then
		TimeMgr:DestroyTimer(UIDefine.UI_Energy , self.mGLoopId)
		self.mGLoopId = 0
	end
	for key, value in pairs(self.ItemLoop) do
		if value > 0 then
			TimeMgr:DestroyTimer(UIDefine.UI_Energy , value)
		end
	end
	self.ItemLoop = nil
	self.ItemLoop = {}
	--开启一个全局的界面轮序
	self.mGLoopId = TimeMgr:CreateTimer(UIDefine.UI_Energy, function()
		self:Timer()
	end,1)
	
	self:InitUIItems()
	
	--self:SetIsUpdateTick(true)
	self:SetDisplayVisible(true)
	self.isFromFind = isFromFind or false
	--为了顶掉，挂起时的旧数据，或者网络错误的时候，最新的数据没有给到最新
	--每次打开界面都要求，拿到刮刮卡的最新数据
	NetEnergyData:requestInfo()
	
	local level = LevelConfig:GetLevel()
	SetActive(self.ui.m_goCarousel,level >= GlobalConfig.OPEN_GIFTPACK_LEVEL)
	
	--local listout,count = MapController:GetItemByType(ItemUseType.Box)
	--MapController:MoveCameraToGrid()
	--self:FindItem(listOut[1], 1)
	local openLevel = PrivilegeAdConfig:GetOpenLevel()
	local level = LevelConfig:GetLevel()
	if level >= openLevel then
		NetGlobalData:SetADImage(self.ui.m_imgAD2,true)
		SetActive(self.ui.m_goAD,not NetGlobalData:IsCanFreeAD())

		RemoveUIComponentEventCallback(self.ui.m_btnAD,UEUI.Button)
		AddUIComponentEventCallback(self.ui.m_btnAD, UEUI.Button, function(arg1,arg2)
				UI_SHOW(UIDefine.UI_ActCenter,6)
				self:Close()
			end)
	else
		SetActive(self.ui.m_goAD,false)
	end
	
	self.isAdBack = false
	EventMgr:Add(EventID.AD_BUY_GIFT_BACK, self.OnBuyGiftBack, self)
end

function UI_Energy:SetDisplayVisible(_bVisibel)
	NetEnergyLotteryData:set_ViewDisplay(_bVisibel)
	--EventMgr:Dispatch(EventID.UPDATE_MAIN_ENERGY_UI, nil)
	UI_UPDATE(UIMgr:GetNowMainUI(), 13, nil)
end

--广告的更新状态
function UI_Energy:OnAdStateChange(_state)
	Log.Info("Ad state Change!!")
	local configs = EnergyLotteryModule:get_AllConfigInfos()
	for key, value in pairs(configs) do
		self:UpdateItemById(tonumber(value.id))
	end
	self:InitSpecialPack()
	local openLevel = PrivilegeAdConfig:GetOpenLevel()
	local level = LevelConfig:GetLevel()
	if level >= openLevel then
		NetGlobalData:SetADImage(self.ui.m_imgAD2,true)
		SetActive(self.ui.m_goAD,not NetGlobalData:IsCanFreeAD())
	end
end

--广告的更新状态
function UI_Energy:OnBuyGiftBack()
	Log.Info("Ad state Change!!")
	local configs = EnergyLotteryModule:get_AllConfigInfos()
	for key, value in pairs(configs) do
		self:UpdateItemById(tonumber(value.id))
	end
	local openLevel = PrivilegeAdConfig:GetOpenLevel()
	local level = LevelConfig:GetLevel()
	self:InitSpecialPack()
	NetGlobalData:SetADImage(self.ui.m_imgAD2,true)
	SetActive(self.ui.m_goAD,not NetGlobalData:IsCanFreeAD())
end

function UI_Energy:UpdateItemUI(configIds)
	configIds = tostring(configIds)
	if configIds == "All" then
		local configs = EnergyLotteryModule:get_AllConfigInfos()
		for key, value in pairs(configs) do
			self:UpdateItemById(tonumber(value.id))
		end
	else
		local ids = string.split(configIds , "|")
		for i = 1, #ids do
			local tabId = tonumber(ids[i])
			self:UpdateItemById(tabId)
		end
	end
	
end

--更新列表里面的内容
function UI_Energy:InitUIItems()
	
	--先获取表中的所有数据
	local configs = EnergyLotteryModule:get_AllConfigInfos()
	self.shopList = nil
	self.shopList = {}
	for key, value in pairs(configs) do
		local uiTab = {}
		uiTab.config = value --这里存一份静态数据，主要是要走UI逻辑使用
		local itemObj = UEGO.Instantiate(self.shopPrefab)
		itemObj.transform:SetParent(self.shopParent.transform)
		itemObj.transform.localScale = Vector3.New(1,1,1)
		itemObj.transform:SetLocalPosition(0, 0, 0)
		itemObj.name = tostring(value.id)
		--先不显示出来
		uiTab.uiObj = itemObj
		table.insert(self.shopList , uiTab)
		self:UpdateItemById(tonumber(value.id))
	end
	self:InitSpecialPack()
	self:UpdateContentSize()
end

function UI_Energy:InitSpecialPack()
	self.specialObj.transform:SetParent(self.shopParent.transform)
	SetUILastSibling(self.specialObj)
	local maxLayer = false
	local sp_config = EnergyLotteryModule:get_SpecialPack()
	if nil == next(sp_config) then
		sp_config = EnergyLotteryModule:get_MaxSpecialPack()
		maxLayer = true
	end
	self.sp_config = sp_config
	local pay_config = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, sp_config.pay_id)
	local imgae = GetChild(self.specialObj,"unlock/Icon" , UEUI.Image)
	local priceTxt = GetChild(self.specialObj,"btnObj/txtPrice" , UEUI.Text)
	local btn = GetChild(self.specialObj,"btnObj" , UEUI.Button)
	local icon = GetChild(self.specialObj,"unlock/normal/rewardTxt/ImgCost" , UEUI.Image)
	local titleTxt = GetChild(self.specialObj,"unlock/normal/txtName" , UEUI.Text)
	local energyTxt = GetChild(self.specialObj,"unlock/normal/rewardTxt", UEUI.Text)
	local timeTxt = GetChild(self.specialObj, "unlock/normal/rewardTimeBg/rewardTime/rewardTime", UEUI.Text)
	
	local energyObj = GetChild(self.specialObj,"unlock/normal/rewardTxt")
	local timeObj = GetChild(self.specialObj, "unlock/normal/rewardTimeBg")
	
	local reward = string.split(sp_config.reward,"|")
	
	local level = NetUpdatePlayerData:GetLevel()
	local open_level =NetGrowthFund:IsOpenActivity()
	if not open_level then
		SetActive(self.specialObj,false)
	else
		SetActive(self.specialObj,true)
	end
	if maxLayer then
		priceTxt.text = LangMgr:GetLang(53)
		SetUIBtnGrayAndEnable(btn, false)
		SetActive(timeObj,true)
		SetActive(energyObj,false)
		
		--local nowTime = TimeMgr:GetServerTime()
		--local last_zero = TimeZoneMgr:GetServerClockStampByNextDay(nowTime)
		--local server_time = TimeZoneMgr:GetServerStampWithServerZone()
		--local subTime = last_zero - server_time
		local subTime = TimeZoneMgr:GetCurDayRemainingSec()
		local timerId = TimeMgr:CreateTimer(UIDefine.UI_Energy, function()
				--print("当前剩余时间==" .. subTime)
				subTime = subTime - 1
				if subTime <= 0 then
					subTime = 0
				end
				timeTxt.text = TimeMgr:ConverSecondToString(subTime)
			end,1)
	elseif not open_level then
		priceTxt.text = LangMgr:GetLang(42)
		SetUIBtnGrayAndEnable(btn, false)
		SetActive(timeObj,false)
		SetActive(energyObj,true)
		if reward[2] then
			energyTxt.text = reward[2]
		end
	else
		if reward[2] then
			energyTxt.text = reward[2]
		end
		SetActive(timeObj,false)
		SetActive(energyObj,true)						
		priceTxt.text = LangMgr:GetLang(pay_config.price_langid)
		SetUIBtnGrayAndEnable(btn, true)
	end	
	titleTxt.text = LangMgr:GetLang(2057)
	SetImageSprite(imgae,sp_config.icon)
	SetImageSprite(icon,sp_config.s_icon)
	RemoveUIComponentEventCallback(btn,UEUI.Button)
	AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
			AudioMgr:Play(14)
			self:BuySpecialPack()
		end)
	--self.specialObj.transform:SetParent(self.shopParent.transform)
end

function UI_Energy:BuySpecialPack()
	if self.sp_config then
		PaymentConfig:ShowPay(self.sp_config.pay_id)
	end
end

function UI_Energy:UpdateItemById(tabId)
	for i = 1, #self.shopList do
		local uiData = self.shopList[i]
		if tonumber(uiData.config.id) == tonumber(tabId) then
			self:FillItem(uiData.uiObj , uiData.config)
			self:UpdateContentSize()
			break;
		end
	end
	--self.specialObj.transform:SetParent(self.shopPrefab.transform)
	--self.specialObj.transform:SetParent(self.shopParent.transform)
end

function UI_Energy:UpdateContentSize()
	
	local showCount = 0
	for i = 1, #self.shopList do
		local uiData = self.shopList[i]
		if uiData.uiObj.activeSelf then
			showCount = showCount + 1
		end
	end
	
	--if showCount <= self.mItemCount then
	--	self.ui.m_scrollview.enabled = false
	--	self.contentSizeFitter.enabled = false
	--	self.viewMask.enabled = false
	--	self.contentRect.sizeDelta = self.m_contentSize.sizeDelta
	--else
	--	self.ui.m_scrollview.enabled = true
	--	self.contentSizeFitter.enabled = true
	--	self.viewMask.enabled = true
	--end
end

--使用最新数据填充一个Item
function UI_Energy:FillItem(itemObj , config)
	
	local configId = tonumber(config.id)
	
	local itemType = config.item_type
	
	local unlockObj = GetChild(itemObj,"unlock")
	local lock = GetChild(itemObj,"lock")
	local bg = GetChild(itemObj,"bg")
	local bgPath = "Sprite/ui_shop/shoplist-big_bg6.png"
	if itemType == 1 then
		bgPath = "Sprite/ui_shop/shoplist-big_bg7.png"
	end
	SetUIImage(bg,bgPath)
	
	--先处理当的数据是否已经解锁
	local uiIsVisible = false
	local dataIsOpen = NetEnergyLotteryData:Is_UnLockLogicData(configId)
	
	local loopId = self.ItemLoop[tostring(config.id)]
	if nil ~= loopId and loopId > 0 then
		TimeMgr:DestroyTimer(UIDefine.UI_Energy , loopId)
	end
	self.ItemLoop[tostring(config.id)] = nil
	
	--这里可以确定是否展示解锁的界面了
	if not dataIsOpen then
		local ui_show_cond_2 = tonumber(config.ui_show_cond_2)
		--如果没有开启 走UI的ui_show_cond_2逻辑
		if ui_show_cond_2 > 0 then
			SetActive(itemObj , true)
			SetActive(unlockObj , dataIsOpen)
			SetActive(lock , true)
			--拿到提示的部分
			local tipTxt = GetChild(lock, "m_txtClock", UEUI.Text)
			tipTxt.text = LangMgr:GetLang(tonumber(ui_show_cond_2))
			--设置按钮为未解锁的状态
			--处理是否是广告类型
			if tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
				self:UpdateBtnByState(itemObj , 4 , config.id)
			else
				self:UpdateBtnByState(itemObj , 3 , config.id)
			end
			return
		else
			SetActive(itemObj , false)
			return
		end
	end
	--处理数据解锁的状态 --走到这里dataIsOpen是true
	local isShow = NetEnergyLotteryData:ui_IsShowCondition_1(configId)
	if isShow and tonumber(itemType) == POWER_BAG_TYPE.ADON then
		isShow = not ADMovieModule:get_IsCloseADMovie()
	end
	SetActive(itemObj , isShow)
	if not isShow then
		return
	end
	SetActive(unlockObj , isShow)
	SetActive(lock , not isShow)
	
	--处理广告部分
	
	--先填充通用的数据
	--Icon
	local Icon = GetChild(unlockObj, "Icon", UEUI.Image)
	local imgList = string.split(config.ui_icon, "|")
	local ImageIndex = 1
	local curAchieved = NetEnergyLotteryData:get_BuyAchieved(configId)
	if curAchieved then
		ImageIndex = curAchieved.achieved
	end
	SetUIImage(Icon, imgList[ImageIndex], false)
	
	--免费
	--local freeObj = GetChild(unlockObj,"freeObj")
	local isFree = false
	if tonumber(config.item_type) == POWER_BAG_TYPE.CARD then
		isFree = EnergyLotteryModule:get_Free()
	else
		isFree = NetEnergyLotteryData:get_IsFree(configId)
	end
	--SetActive(freeObj , isFree)
	
	--广告提示
	--local adsTipObj = GetChild(unlockObj,"adstips")
	--if tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
		--SetActive(adsTipObj , true)
	--else
		--SetActive(adsTipObj , false)
	--end

	local remainTxt = GetChild(unlockObj, "normal/remainTxt");
	if tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
		SetActive(remainTxt, true)
	else
		SetActive(remainTxt, false)
	end
	
	--额外奖励显示
	local extraObj = GetChild(unlockObj,"extraObj")
	local arr = string.split(config.ui_extra , "|")
	local itemId = tonumber(arr[1])
	if itemId <= 0 then
		SetActive(extraObj , false)
	else
		SetActive(extraObj , true)
		local m_txtcount = GetChild(extraObj, "m_txtcount", UEUI.Text)
		local m_Image = GetChild(extraObj, "m_txtcount/Image", UEUI.Image)
		local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item , itemId)
		SetUIImage(m_Image, itemCfg.icon_b, false)
		m_txtcount.text = "+" .. tostring(arr[2])
		
	end
	
	
	local cardObj = GetChild(unlockObj,"card")
	local normalObj = GetChild(unlockObj,"normal")
	local btnHelp = GetChild(itemObj,"m_btnHelp")
	RemoveUIComponentEventCallback(btnHelp, UEUI.Button)
	AddUIComponentEventCallback(btnHelp, UEUI.Button, function(arg1,arg2)
			 UI_SHOW(UIDefine.UI_CommonDialog,DIALOG_TYPE.COMMON_CONFIRM,config.name,7002)
		end)
	
	--这里的逻辑就要分开处理啦
	if config.item_type == POWER_BAG_TYPE.CARD then
		SetActive(cardObj , true)
		SetActive(btnHelp , true)
		SetActive(normalObj , false)
		self:UpdateCardState(itemObj , config)
	else
		SetActive(cardObj , false)
		SetActive(normalObj , true)
		SetActive(btnHelp , false)
		self:UpdateNormalState(itemObj , config)
	end
end

--function UI_Energy:TickUI(delta)
	
--end


--处理正常的礼包显示
function UI_Energy:UpdateNormalState(itemObj , config)
	
	local normalObj = GetChild(itemObj,"unlock/normal")
	
	local nameTxt = GetChild(normalObj, "m_txtName", UEUI.Text)
	
	nameTxt.text = LangMgr:GetLang(tonumber(config.name))
	UnifyOutline(nameTxt,"#673914")
	local rewardObj = GetChild(normalObj,"rewardTxt")
	local rewardBgObj = GetChild(normalObj,"rewardTxtBg")
	local rewardTimeBg = GetChild(normalObj,"rewardTimeBg")
	local rewardTime = GetChild(normalObj,"rewardTime")
	
	local loopId = self.ItemLoop[tostring(config.id)]
	if nil ~= loopId and loopId > 0 then
		TimeMgr:DestroyTimer(UIDefine.UI_Energy , loopId)
	end
	self.ItemLoop[tostring(config.id)] = nil
	--先处理最终的奖励
	local rewardList = NetEnergyLotteryData:get_FinalReward(config.id)
	if #rewardList <= 0 then
		SetActive(rewardObj , false)
		SetActive(rewardBgObj , false)
		SetActive(rewardTimeBg , false)
	else
		local reImg = GetChild(normalObj, "rewardTxt/ImgCost", UEUI.Image)
		local reTxt = GetChild(normalObj, "rewardTxt", UEUI.Text)
		local timeTxt = GetChild(normalObj, "rewardTimeBg/rewardTime", UEUI.Text)
		local bRewardStr = true
		if tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
			local AdState , subTime = ADMovieModule:State(config.ad_type)
			if AdState == 0 then
			elseif AdState == 7 or AdState == 8 then
				--不可以播放
				bRewardStr = false
				--走时间倒计时
				local timerId = TimeMgr:CreateTimer(UIDefine.UI_Energy, function()
						--print("当前剩余时间==" .. subTime)
						subTime = subTime - 1
						timeTxt.text = TimeMgr:ConverSecondToString(subTime)
						--if subTime <= 1 then
							--self:OnAdStateChange(nil)
						--end
					end,1)
				self.ItemLoop[tostring(config.id)] = timerId
			end
		end
		
		SetActive(rewardObj , bRewardStr)
		SetActive(rewardBgObj , bRewardStr)
		SetActive(rewardTimeBg , not bRewardStr)
		
		local itemId = rewardList[1].itemId
		local itemCount = rewardList[1].itemCount
		
		--处理广告的显示
		if tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
			local adId = tonumber(config.ad_type)
			local adConfig = ConfigMgr:GetDataByKey(ConfigDefine.ID.advertisement, "ad_id", adId)
			local adType = tonumber(adConfig["game_type"])
			local rankingTb = ConfigMgr:GetDataByID(ConfigDefine.ID.adlimit , adType)

			local remainTxt = GetChild(normalObj, "remainTxt", UEUI.Text);
			local limit = adConfig.times_limit + ADMovieModule:GetExtraInt(adId, "limit");
			local count = ADMovieModule:GetRecord(v2s(adType)).times;
			local num = limit > count and limit - count or 0;
			remainTxt.text = LangMgr:GetLang(9018) .. num .. "/" .. limit;

			if rankingTb == nil then
				error("不存在的RankingId =>" .. tostring(adType));
				return
			end

			local ad_double = rankingTb["ad_double"]
			if nil == ad_double then
				DebugLogTable(rankingTb)
				error("adlimit table not data ==>" .. tostring(adType))
				return
			end
			--档位信息
			local ranking = ADMovieModule:GetExtraInt(adId, "ranking")
			local dou_arr = string.split(ad_double, "|")
			if dou_arr[tonumber(ranking)] == nil then
				error(ad_double .. "-不存在的档位信息 =>" .. tostring(ranking));
				return
			end
			local itemId = ItemID.ENERGY
			if adType == 5 then
				itemId = ItemID.LimitEnergy
			end
			itemCount = tonumber(dou_arr[tonumber(ranking)])
		end
			
			
		local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item , itemId)
		SetUIImage(reImg, itemCfg.icon_b, false)
		reTxt.text = tostring(itemCount)
	end
	
	--获得当前这个礼包的最终价格
	local final = NetEnergyLotteryData:get_FinalPrice(config.id)
	if final <= 0 then
		--免费的状态
		--self:UpdateBtnByState(itemObj , 1 , config.id)
		if tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
			local AdState , subTime = ADMovieModule:State(config.ad_type)
			if AdState == 0 then
				self:UpdateBtnByState(itemObj , 7 , config.id)
			else
				self:UpdateBtnByState(itemObj , 4 , config.id)
			end
		else
			self:UpdateBtnByState(itemObj , 1 , config.id)
		end
	else
		self:UpdateBtnByState(itemObj , 6 , config.id)
		--如果不是免费状态
		local cost_id = tonumber(config.cost_item_type)
		local itemCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.item , cost_id)
		local price_txt = GetChild(itemObj, "m_btnObj/m_txtPrice", UEUI.Text)
		local price_img = GetChild(itemObj, "m_btnObj/m_txtPrice/ImgCost", UEUI.Image)
		SetUIImage(price_img, itemCfg.icon_b, false)
		price_txt.text = tostring(final)
	end
end

--特殊处理刮刮卡
function UI_Energy:UpdateCardState(itemObj , config)
	
	--
	--UI_CLOSE(UIDefine.UI_EnergyLottery)
	
	local cardObj = GetChild(itemObj,"unlock/card")
	
	local freeObj = GetChild(cardObj,"free")
	local canRewradObj = GetChild(cardObj,"canreward")
	local rewardObj = GetChild(cardObj,"rewarded")
	
	local curState = EnergyLotteryModule:get_CurState()
	SetActive(freeObj , curState == CARD_STATE.FREE)
	SetActive(canRewradObj , curState == CARD_STATE.CANREWARD)
	SetActive(rewardObj ,curState == CARD_STATE.REWARDED or curState == CARD_STATE.EXPIRE or curState == CARD_STATE.WAIT)
	
	--设置进度
	local curCnt = 0
	local maxCnt = 0
	local remainCnt = 0
	local netData = NetEnergyData:GetNetData()
	if netData then
		curCnt = netData["count"] or 0
		maxCnt = netData["maxCount"] or 0
		remainCnt = maxCnt - curCnt
	end
	local progressTxt = GetChild(cardObj,"txtLotteryCnt",UEUI.Text)
	progressTxt.text = LangMgr:GetLang(9018)..remainCnt.."/"..maxCnt

	--更新里面的东西
	if self.mCardLoopId > 0 then
		TimeMgr:DestroyTimer(UIDefine.UI_Energy , self.mCardLoopId)
		self.mCardLoopId = 0
	end
	local nameTxt
	if curState == CARD_STATE.FREE then
		nameTxt = GetChild(freeObj, "nameTxt", UEUI.Text)
		local tipTxt = GetChild(freeObj, "tips", UEUI.Text)
		nameTxt.text = LangMgr:GetLang(tonumber(config.name))
		tipTxt.text =  LangMgr:GetLang(152)
		self:UpdateBtnByState(itemObj , 1 , config.id)
	elseif curState == CARD_STATE.CANREWARD then
		nameTxt = GetChild(canRewradObj, "nameTxt", UEUI.Text)
		local tipTxt = GetChild(canRewradObj, "tips", UEUI.Text)
		local timeText = GetChild(canRewradObj, "m_txtTime", UEUI.Text)
		nameTxt.text = LangMgr:GetLang(tonumber(config.name))
		tipTxt.text =  LangMgr:GetLang(206023)
		--时间
		timeText.text = TimeMgr:ConverSecondToString(EnergyLotteryModule:get_SubTime())
		self.mCardLoopId = TimeMgr:CreateTimer(UIDefine.UI_Energy, function()
				timeText.text = TimeMgr:ConverSecondToString(EnergyLotteryModule:get_SubTime())
			end,1)
		self:UpdateBtnByState(itemObj , 2 , config.id)
	elseif curState == CARD_STATE.REWARDED or curState == CARD_STATE.EXPIRE or curState == CARD_STATE.WAIT then
		nameTxt = GetChild(rewardObj, "nameTxt", UEUI.Text)
		nameTxt.text = LangMgr:GetLang(tonumber(config.name))
		local timeText = GetChild(rewardObj, "m_txtTime", UEUI.Text)
		timeText.text = TimeMgr:ConverSecondToString(EnergyLotteryModule:get_SubTime())
		self.mCardLoopId = TimeMgr:CreateTimer(UIDefine.UI_Energy, function()
				timeText.text = TimeMgr:ConverSecondToString(EnergyLotteryModule:get_SubTime())
			end,1)
		self:UpdateBtnByState(itemObj , 5 , config.id, true)
	else
		error("未知的刮刮卡状态---->" .. tostring(curState))
	end
	UnifyOutline(nameTxt,"#673914")
end

--根据状态设置按钮的显示状态 --1-免费 2-可以领取 3-未开启(即将登场) 4-未开启观看广告 5-已领取 6-带货币的显示
function UI_Energy:UpdateBtnByState(uiObj , _state , configId, btnForceEnable)
	
	local btn = GetChild(uiObj, "m_btnObj", UEUI.Button)
	local m_layout = GetChild(btn, "ImagTstWig", UE.RectTransform)
	local m_txtState = GetChild(btn, "ImagTstWig/m_txtState", UEUI.Text)
	local m_ImgState = GetChild(btn, "ImagTstWig/imgState", UEUI.Image)
	local m_txtPrice = GetChild(btn, "m_txtPrice", UEUI.Text)
	
	local freeRow = GetChild(btn, "freeRow")
	
	local arrow = GetChild(uiObj,"m_btnObj/arrow",UE.RectTransform)
	local arrowTween =  GetChild(uiObj,"m_btnObj/arrow/glodArrow",TweenAnim)
	NetGlobalData:SetADImage(m_ImgState)
	SetActive(m_ImgState , false)
	local bTxtVisible = true
	local btnEnable = false
	local isAni = false
	local isGoldAni = false
	local txtStr = ""
	
	local isFree = (_state == 1)
	local hex = isFree and "056e00" or "0069AB"
	UnifyOutline(GetChild(btn, "ImagTstWig/m_txtState"),hex)
	
	local sprite = isFree and "button2-green.png" or "button2-blue.png"
	SetUIImage(btn,"Sprite/ui_public/"..sprite,false)
	
	if _state == 1 then
		--免费
		txtStr = LangMgr:GetLang(66)
		btnEnable = true
		isAni = true
	elseif _state == 2 then
		--可以领取
		txtStr = LangMgr:GetLang(17)
		btnEnable = true
	elseif _state == 3 then
		--未开启(即将登场)
		txtStr = LangMgr:GetLang(42)
	elseif _state == 4 then
		--未开启观看广告
		txtStr = LangMgr:GetLang(40)
		if NetGlobalData:IsCanFreeAD() then
			txtStr = LangMgr:GetLang(405)
		end
		SetActive(m_ImgState , true)
	elseif _state == 5 then
		--已领取
		txtStr = LangMgr:GetLang(71)
		local curState = EnergyLotteryModule:get_CurState()
		if curState == CARD_STATE.EXPIRE then
			txtStr = LangMgr:GetLang(211)
		elseif curState == CARD_STATE.WAIT then
			txtStr = LangMgr:GetLang(7402)
		end
	elseif _state == 7 then
		--开启了观看广告
		txtStr = LangMgr:GetLang(40)
		if NetGlobalData:IsCanFreeAD() then
			txtStr = LangMgr:GetLang(405)
		end
		btnEnable = true
		--40:观看广告   405：直接领取
		SetActive(m_ImgState , true)
	elseif _state == 6 then
		--带货币的显示
		bTxtVisible = false
		btnEnable = true
		isGoldAni = true
	end
	
	SetActive(m_txtState , bTxtVisible)
	SetActive(m_txtPrice , not bTxtVisible)
	if bTxtVisible then
		m_txtState.text = txtStr
		UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(m_layout)
	end

	if btnForceEnable then
		btnEnable = true
	end

	--按钮图片
	SetUIBtnGrayAndEnable(btn, btnEnable)
	SetUIImageGray(btn.transform:GetChild(0), not btnEnable)
	local isEnableColor = btnEnable and hex or "454545"
	UnifyOutline(GetChild(btn, "ImagTstWig/m_txtState"),isEnableColor)
	if btnEnable then
		--绑定事件
		RemoveUIComponentEventCallback(btn, UEUI.Button)
		AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
				self:OnButton(configId , uiObj)
			end)
	end
	--动画部分
	DOKill(freeRow.transform)
	if isAni then
		SetActive(freeRow , true)
		freeRow.transform.anchoredPosition = Vector2.New(0 , 153)
		DOLocalMoveYLoop(freeRow.transform , 130 , 0.5 , LoopType.Yoyo , Ease.InOutSine)
	else
		if self.isFromFind and configId == 1 then
			SetActive(freeRow , true)
			freeRow.transform.anchoredPosition = Vector2.New(0 , 153)
			DOLocalMoveYLoop(freeRow.transform , 130 , 0.5 , LoopType.Yoyo , Ease.InOutSine)
			local function EndWait()
				SetActive(freeRow , false)
			end
			self.fromFindAni = TimeMgr:CreateTimer(self,EndWait,3, 1)
		else
			SetActive(freeRow , false)
		end
	
	end
	local level = NetUpdatePlayerData:GetLevel()
	if isGoldAni and v2n(level) <= 7 then
		local curState = EnergyLotteryModule:get_CurState()
		if nil ~= configId and curState ~= CARD_STATE.FREE then
			local final = NetEnergyLotteryData:get_FinalPrice(configId)
			if nil ~= final then
				if v2n(final) == 350 then
					SetActive(freeRow , true)
					freeRow.transform.anchoredPosition = Vector2.New(0 , 153)
					DOLocalMoveYLoop(freeRow.transform , 130 , 0.5 , LoopType.Yoyo , Ease.InOutSine)
				end
			end
		end
	end
end
--处理button的点击事件
function UI_Energy:OnButton(configId , uiObj)
	local config = EnergyLotteryModule:get_ConfigInfoById(configId)
	if nil == config then
		error("不存在的数据元素------>" .. tostring(configId))
		return
	end
	AudioMgr:Play(14)
	if tonumber(config.item_type) == POWER_BAG_TYPE.CARD then
		--刮刮卡的数据
		if EnergyLotteryModule:get_Free() then
			--直接发送领取的数据
		else
			--打开刮刮卡的界面
		end
		
		--打开界面
		UI_SHOW(UIDefine.UI_EnergyLottery)
		
	elseif tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
		--广告的播放
		
		local AdState = ADMovieModule:State(config.ad_type)
		--AdState 可用
		if AdState == 0 then
			Log.Info("播放广告")
			local tempObj = uiObj
			ADMovieModule:PlayAD(config.ad_type , function(type, adID, state , ranking)
					print("播放广告回调=-=>" .. tostring(type) )
					if type == 0 then
						if self.isAdBack then
							return
						end
						self:BuyNormalSucReward(tempObj , config.id , ranking , adID)
						self:BuyNormalSuc(tempObj , config.id)
						self.isAdBack = true
					end
				end)
		else
			Log.Info("广告还不可以播放")
			UI_SHOW(UIDefine.UI_WidgetTip , LangMgr:GetLang(7079))
		end
	else
		--正常的礼包数据
		local final = NetEnergyLotteryData:get_FinalPrice(config.id)
		if final <= 0 then
			--免费次数之内
			self:BuyNormalSucReward(uiObj , config.id)
			self:BuyNormalSuc(uiObj , config.id)
			
		else
			--处理需要消耗的钱币是否足够
			local cost_id = tonumber(config.cost_item_type)
			local playerCount = NetUpdatePlayerData:GetResourceNumByID(cost_id)
			if playerCount < final then
				--货币不足无法购买
				Log.Info("所需货币不足，无法购买")
				
				if cost_id == ItemID.DIAMOND then
					UI_SHOW(UIDefine.UI_Shop)
				elseif cost_id == ItemID.COIN then
					local dimaion = math.ceil((final - playerCount) / tonumber(GlobalConfig:GetString(1107)))
					UI_SHOW(UIDefine.UI_BuyGold, tonumber(final - playerCount), config, 3, function()
							--这里是购买成功
							--减金币 , 减钻石
							local rewardId = self:BuyNormalSucReward(uiObj , config.id)
							NetUpdatePlayerData:AddResource(PlayerDefine[cost_id], -playerCount,nil,nil,"UI_Energy")
							NetUpdatePlayerData:AddResource(PlayerDefine.Diamond, -dimaion,nil,nil,"UI_Energy")
							NetNotification:NotifyConsume(11,rewardId,1,cost_id,playerCount)
							NetNotification:NotifyConsume(11,rewardId,1,ItemID.DIAMOND,dimaion)
							self:BuyNormalSuc(uiObj , config.id)
						end)
				else
					Log.Info("所需货币不足，无法购买")
					local function onOk()
					end
					UI_SHOW(UIDefine.UI_Tips, "Not Enough Cost Type ~", onOk)
				end
				return
			end

			--消耗货币购买东西
			local rewardId = self:BuyNormalSucReward(uiObj , config.id)
			NetUpdatePlayerData:AddResource(PlayerDefine[cost_id], -final,nil,nil,"UI_Energy")
			NetNotification:NotifyConsume(11,rewardId,1,cost_id,final)
			self:BuyNormalSuc(uiObj , config.id)
		end
	end
end

--先给奖励
function UI_Energy:BuyNormalSucReward(uiObj , configId , ranking , adID)
	local btn = nil
	if not IsNil(uiObj) then 
		btn = GetChild(uiObj, "m_btnObj", UEUI.Button)
	end
	local config = EnergyLotteryModule:get_ConfigInfoById(configId)
	if nil == config then
		error("不存在的数据元素------>" .. tostring(configId))
		return
	end

	--处理是否是广告部分
	if tonumber(config.item_type) == POWER_BAG_TYPE.ADON then
		--ranking 档位信息
		local config = ConfigMgr:GetDataByKey(ConfigDefine.ID.advertisement, "ad_id", adID)
		if config == nil then
			error("不存在的advertisementId =>" .. tostring(adID));
			return
		end
		local adType = tonumber(config["game_type"])
		local rankingTb = ConfigMgr:GetDataByID(ConfigDefine.ID.adlimit , adType)
		if rankingTb == nil then
			error("不存在的RankingId =>" .. tostring(adType));
			return
		end

		local ad_double = rankingTb["ad_double"]
		if nil == ad_double then
			DebugLogTable(rankingTb)
			error("adlimit table not data ==>" .. tostring(adType))
			return
		end
		local dou_arr = string.split(ad_double, "|")
		if dou_arr[tonumber(ranking)] == nil then
			error(ad_double .. "-不存在的档位信息 =>" .. tostring(ranking));
			return
		end
		local itemId = ItemID.ENERGY
		if adType == 5 then
			itemId = ItemID.LimitEnergy
		end
		local itemCount = tonumber(dou_arr[tonumber(ranking)])
		NetUpdatePlayerData:AddResourceNumByID(itemId, itemCount,true,"UI_Energy")
		local flyPos = Vector2.New(0, 0)
		if not IsNil(btn) then
		 	flyPos = MapController:GetUIPosByWorld(btn.transform.position)
		end
		
		MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, itemId, itemCount, nil, nil, ItemID.ENERGY)
		return
	end
	
	local list_reward = NetEnergyLotteryData:get_FinalReward(config.id)
	if #list_reward <= 0 then
		error("静态表中不存在，免费信息--" .. tostring(config.id))
		return
	end
	--额外奖励
	local extra_list = string.split(tostring(config.ui_extra) , "|")
	if tonumber(extra_list[1]) > 0 then
		local ext_tab = {}
		ext_tab.itemId = tonumber(extra_list[1])
		ext_tab.itemCount = tonumber(extra_list[2])
		table.insert(list_reward , ext_tab)
	end

	local energyId
	for i = 1, #list_reward do
		local itemId = list_reward[i].itemId
		if itemId == ItemID.LimitEnergy or ItemID.ENERGY then
			energyId = itemId
		end
		local itemCount = list_reward[i].itemCount
		NetUpdatePlayerData:AddResourceNumByID(itemId, itemCount,true,"UI_Energy")
		local flyPos = Vector2.New(0, 0)
		if not IsNil(btn) then
			flyPos = MapController:GetUIPosByWorld(btn.transform.position)
		end
		MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, itemId, itemCount, nil, nil, ItemID.ENERGY)
	end
	return energyId
end

--购买成功的回调
function UI_Energy:BuyNormalSuc(uiObj, configId)
	local config = EnergyLotteryModule:get_ConfigInfoById(configId)
	if nil == config then
		error("不存在的数据元素------>" .. tostring(configId))
		return
	end
	--购买成功后的回调
	self:UpdateBtnByState(uiObj , 5)
	local bUpdate = NetEnergyLotteryData:BuySucc(config.id)
	if bUpdate then
		Log.Info("更新当前的页面")
		NetEnergyLotteryData:OnBuying()
	end
	if configId == POWER_BAG_TYPE.HIGH then
		local isFirstUse = NetPaymentData:GetTypeByName("first_buy_energy")
		if not isFirstUse then
			local eventName =AdjustToken.purEnergyFirst
			local eventName2 = "purEnergyFirst"
			SdkHelper:AFEvent(eventName, eventName2)
			NetPaymentData:SetTypeByName("first_buy_energy",true)
		end
	end
end

--全局的轮询数据
function UI_Energy:Timer()
	local isShow = false
	
	if EnergyLotteryModule:get_IsUpdateAchivedTime() then
		isShow = true
		local subTime = EnergyLotteryModule:get_AchievedSubTime()
		self.m_txtRefreshTime.text = string.format(self.txtLang , TimeMgr:ConverSecondToString(subTime))
	else
		local scope = EnergyLotteryModule:get_MapScope()
		if scope == GIFT_SCOPE.MAIN_CAMP or scope == GIFT_SCOPE.LIMIT_LEVEL then
			isShow = true
			local count = 1
			if scope == GIFT_SCOPE.MAIN_CAMP then
				count = v2n(GlobalConfig:GetString(1302))
			elseif scope == GIFT_SCOPE.LIMIT_LEVEL then
				count = v2n(GlobalConfig:GetString(1303))
			end
			local finish,subTime = TimeZoneMgr:CheckNeedRefresh(nil,count)
			self.m_txtRefreshTime.text = string.format(self.txtLang , TimeMgr:ConverSecondToString(subTime))
		else
			isShow = false
		end
	end

	SetActive(self.m_txtRefreshTime , isShow)
	SetActive(self.ui.m_imgRefreshTime , isShow)

	self.ui.m_txtGiftPack.text =  TimeMgr:CheckHMS(GiftPackModule:getLastTime())
end
--界面的通知消息
function UI_Energy:OnRefresh(param)
	if param == 10 then
		local itemId = ItemID.ENERGY
		local curMapID = NetUpdatePlayerData.playerInfo.curMap
		if not IsHomeMap(curMapID) then
			itemId = ItemID.LimitEnergy
		end
		local flyPos = MapController:GetUIPosByWorld(self.specialBtn.transform.position)
		MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, itemId, 15, nil, nil, ItemID.ENERGY)
		self:InitSpecialPack()
	end
	self:UpdateItemUI(param)
end

function UI_Energy:onDestroy()
	--local cur_energy = EnergyLotteryModule:GetCurScopeEnergyType()
	UI_UPDATE(UIMgr:GetNowMainUI(),12,{isShow = false,list={PlayerDefine.Coin,PlayerDefine.Energy}})
	EventMgr:Remove(EventID.AD_STATE, self.OnAdStateChange)
	if self.mCardLoopId > 0 then
		TimeMgr:DestroyTimer(UIDefine.UI_Energy , self.mCardLoopId)
		self.mCardLoopId = 0
	end
	if self.mGLoopId > 0 then
		TimeMgr:DestroyTimer(UIDefine.UI_Energy , self.mGLoopId)
		self.mGLoopId = 0
	end
	for key, value in pairs(self.ItemLoop) do
		if nil ~= value and value > 0 then
			TimeMgr:DestroyTimer(UIDefine.UI_Energy , value)
		end
	end
	self.ItemLoop = nil
	self.ItemLoop = {}
	self:SetDisplayVisible(false)
	TimeMgr:DestroyTimer(self , self.fromFindAni)
	NetEnergyLotteryData:ClosePushView()
	NetEnergyLotteryData:TriggerComeType()
	NetEnergyLotteryData:OnClearComeType()
	
	local mapId = NetUpdatePlayerData.playerInfo.curMap
	if IsHomeMap(mapId) then
		TriggerGiftConfig:ChangeEnergyGift()
		if self.notEnoughNum then
			TriggerGiftConfig:ChangeEnergyGiftNew(self.notEnoughNum)
		end
	else
		TriggerGiftConfig:ChangeLimitEnergy2()
		if self.notEnoughNum then
			TriggerGiftConfig:ChangeLimitEnergyNew2(self.notEnoughNum)
		end
	end
	self.notEnoughNum = nil
	EventMgr:Remove(EventID.AD_BUY_GIFT_BACK, self.OnBuyGiftBack, self)
end

function UI_Energy:onUIEventClick(go,param)
	local name = go.name
	if name == "btn_close" then
		AudioMgr:Play(5)
		self:Close()
	elseif name == "m_goCarousel" then
		UI_SHOW(UIDefine.UI_GiftPack)
	end
end

return UI_Energy
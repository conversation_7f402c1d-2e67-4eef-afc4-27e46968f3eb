local UI_UnlockTipsModel = {}

UI_UnlockTipsModel.config = {["name"] = "UI_UnlockTips", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_UnlockTipsModel:Init(c)
    c.ui = {}    
    c.ui.m_btnClose = GetChild(c.uiGameObject,"Bg/m_btnClose",UEUI.Button)
    c.ui.m_imgItem1 = GetChild(c.uiGameObject,"Bg/goItem/m_imgItem1",UEUI.Image)
    c.ui.m_imgItem2 = GetChild(c.uiGameObject,"Bg/goItem/m_imgItem2",UEUI.Image)
    c.ui.m_imgItem3 = GetChild(c.uiGameObject,"Bg/goItem/m_imgItem3",UEUI.Image)
    c.ui.m_goEffLight = GetChild(c.uiGameObject,"Bg/goCombine/imgBg/m_goEffLight")
    c.ui.m_imgCombine = GetChild(c.uiGameObject,"Bg/goCombine/m_imgCombine",UEUI.Image)
    c.ui.m_goCombineSpine = GetChild(c.uiGameObject,"Bg/goCombine/m_goCombineSpine")
    c.ui.m_btnConfirm = GetChild(c.uiGameObject,"Bg/m_btnConfirm",UEUI.Button)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_UnlockTipsModel
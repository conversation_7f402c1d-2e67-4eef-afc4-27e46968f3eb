-- 卡牌收集活动数据类

local NetCollectCardData = Class()
local M = NetCollectCardData

local CardEventType = {
    GET_FIVE_STAR_CARD = 1,
    LACK_ONE_CARD_NORMAL = 2,
    LACK_ONE_CARD_GOLD = 3
}

--region --------------------------------------- 数据结构 ---------------------------------------

function M:ctor()
    self.data = {}
    self.data.cardList = {}            -- 卡牌列表
    self.data.seriesRewardList = {}    -- 卡牌系列奖励列表
    self.data.point = 0                -- 点数
    self.data.universalCard = 0        -- 万能卡数量
    self.data.universalGoldCard = 0    -- 黄金万能卡数量
    self.data.isAutoSelect = false     -- 是否自动选择
    self.data.redPointList = {}        -- 红点列表
    self.data.cardPackRewardList = {}  -- 卡包奖励列表（用于展示抽卡动画）
    self.data.eventList = {}           -- 事件列表
    self.data.isGetGrandPrize = false  -- 是否获得活动大奖
end

function M:ClearNetData()
    self.data.cardList = {}
    self.data.seriesRewardList = {}
    self.data.isAutoSelect = false
    self.data.redPointList = {}
    self.data.cardPackRewardList = {}
    self.data.eventList = {}
    self.data.isGetGrandPrize = false
end

function M:ReadNetData(jsonData)
    if jsonData == nil then
        return
    end
    local data = jsonData["data"]
    if data then
        self.data.cardList = data.cardList or {}
        self.data.seriesRewardList = data.seriesRewardList or {}
        self.data.point = data.point or 0
        self.data.universalCard = data.universalCard or 0
        self.data.universalGoldCard = data.universalGoldCard or 0
        self.data.isAutoSelect = data.isAutoSelect or false
        self.data.redPointList = data.redPointList or {}
        self.data.cardPackRewardList = data.cardPackRewardList or {}
        self.data.eventList = data.eventList or {}
        self.data.isGetGrandPrize = data.isGetGrandPrize or false
    end
end

function M:WriteNetData()
    local jsonData = {}
    jsonData["data"] = self.data
    return jsonData
end

function M:SetDataByKey(key, value)
    if self.data[key] ~= nil then
        self.data[key] = value
    end
end

function M:GetDataByKey(key)
    if self.data[key] ~= nil then
        return self.data[key]
    end
end

--endregion ------------------------------------ 数据结构 ---------------------------------------

--region --------------------------------------- 活动流程 ---------------------------------------

--- 初始化活动数据
function M:InitActiveData(activityId)

end

--- 活动结束，检查推送
--- @param activityId number 活动 ID
function M:CheckEndPush(activityId)
    local active = LimitActivityController:GetActiveMessage(activityId)
    if active then
        -- 活动结束，推送卡牌分解界面
        if active:IsActivityEnd() then
            UI_CLOSE(UIDefine.UI_CollectCardDetail)
            UI_CLOSE(UIDefine.UI_CollectCardExchange)
            UI_CLOSE(UIDefine.UI_CollectCardHelp)
            UI_CLOSE(UIDefine.UI_CollectCardRank)
            UI_CLOSE(UIDefine.UI_CollectCardRate)
            UI_CLOSE(UIDefine.UI_CollectCardResolve)
            UI_CLOSE(UIDefine.UI_CollectCardShop)
            UI_CLOSE(UIDefine.UI_CollectCardSingle)
            UI_CLOSE(UIDefine.UI_CollectCardUniversal)
            UI_CLOSE(UIDefine.UI_CollectionCenter)

            UI_CLOSE(UIDefine.UI_CollectionCenter)
            UI_CLOSE(UIDefine.UI_CollectionDetail)
            UI_CLOSE(UIDefine.UI_CollectionSkinList)

            NetPushViewData:PushView(PushDefine.UI_CollectCardResolve)
        end
    end
end

--- 设置活动大奖领取状态
--- @param flag boolean 领取状态
function M:SetGrandPrizeState(flag)
    self.data.isGetGrandPrize = flag
end

--- 获取活动大奖领取状态
--- @return boolean flag 领取状态
function M:GetGrandPrizeState()
    return self.data.isGetGrandPrize
end

--endregion ------------------------------------ 活动流程 ---------------------------------------

--region ---------------------------------------- 普通卡 ----------------------------------------

--- 添加卡牌
--- @param cardID string|number 卡牌 ID
--- @param num number 添加数量
--- @param reason string 变化原因
function M:AddCard(cardID, num, reason)
    cardID = tostring(cardID)
    local addNum = num or 1
    if not self.data.cardList[cardID] then
        self.data.cardList[cardID] = 0
    end

    local isNewCard = self.data.cardList[cardID] == 0
    local seriesID = CollectCardManager:GetSeriesIDByCard(cardID)
    local cardOldNum = self.data.cardList[cardID]
    local seriesOldNum = self:GetCardNumOfSeries(seriesID)
    local totalOldNum = self:GetCardNumOfSeriesTotal()

    self.data.cardList[cardID] = self.data.cardList[cardID] + addNum

    local cardNewNum = self.data.cardList[cardID]
    local seriesNewNum = self:GetCardNumOfSeries(seriesID)
    local totalNewNum = self:GetCardNumOfSeriesTotal()

    -- 设置红点
    self:SetRedPoint(cardID)

    self:TrackEventCardChange(reason, isNewCard, v2n(cardID), v2n(seriesID),
        cardOldNum, addNum, cardNewNum, seriesOldNum, seriesNewNum, totalOldNum, totalNewNum)
end

--- 减少卡牌
--- @param cardID string|number 卡牌 ID
--- @param num number 减少的数量
--- @param reason string 变化原因
function M:ReduceCard(cardID, num, reason)
    cardID = tostring(cardID)
    local reduceNum = num or 1
    if not self.data.cardList[cardID] then
        self.data.cardList[cardID] = 0
    end

    local isNewCard = self.data.cardList[cardID] == 0
    local seriesID = CollectCardManager:GetSeriesIDByCard(cardID)
    local cardOldNum = self.data.cardList[cardID]
    local seriesOldNum = self:GetCardNumOfSeries(seriesID)
    local totalOldNum = self:GetCardNumOfSeriesTotal()

    self.data.cardList[cardID] = math.max(self.data.cardList[cardID] - reduceNum, 0)

    local cardNewNum = self.data.cardList[cardID]
    local seriesNewNum = self:GetCardNumOfSeries(seriesID)
    local totalNewNum = self:GetCardNumOfSeriesTotal()

    self:TrackEventCardChange(reason, isNewCard, v2n(cardID), v2n(seriesID),
        cardOldNum, -reduceNum, cardNewNum, seriesOldNum, seriesNewNum, totalOldNum, totalNewNum)
end

--- 获取卡牌数量
--- @param cardID string|number 卡牌 ID
--- @return number cardNum 卡牌数量
function M:GetCardNum(cardID)
    cardID = tostring(cardID)
    if not self.data.cardList[cardID] then
        self.data.cardList[cardID] = 0
    end
    return self.data.cardList[cardID]
end

--- 获取卡牌系列收集数量
--- @param seriesID number 卡牌系列 ID
--- @return integer currentNum 当前收集数量
--- @return integer totalNum 总数量
function M:GetCardNumOfSeries(seriesID)
    local currentNum = 0
    local totalNum = 0
    local config = CollectCardManager:GetCardCollectionItemConfig(seriesID)
    if config then
        local cardList = string.split(config.card, "|")
        for _, value in ipairs(cardList) do
            local num = self:GetCardNum(value)
            if num > 0 then
                currentNum = currentNum + 1
            end
        end
        totalNum = #cardList
    end
    return currentNum, totalNum
end

--- 获取全部卡牌系列收集数量
--- @return integer currentSum 当前收集数量
--- @return integer totalSum 总数量
function M:GetCardNumOfSeriesTotal()
    local currentSum = 0
    local totalSum = 0
    local cardConfig = CollectCardManager:GetCardSeries()
    if cardConfig then
        for _, series in ipairs(cardConfig) do
            local currentNum, totalNum = self:GetCardNumOfSeries(series.id)
            currentSum = currentSum + currentNum
            totalSum = totalSum + totalNum
        end
    end
    return currentSum, totalSum
end

--- 获取已集齐的收藏册数量
--- @return integer result 已集齐的收藏册数量
function M:GetSeriesFinishNum()
    local result = 0
    local config = CollectCardManager:GetCardSeries()
    if not config then return end
    for _, series in ipairs(config) do
        local currentNum, totalNum = self:GetCardNumOfSeries(series.id)
        if currentNum == totalNum then
            result = result + 1
        end
    end
    return result
end

--- 获取包含重复卡牌的系列
--- @return table result 包含重复卡牌的系列
function M:GetRepeatCardSeries()
    local result = {}
    local config = CollectCardManager:GetCardSeries()
    if config then
        for _, series in ipairs(config) do
            local cardList = string.split(series.card, "|")
            for _, value in ipairs(cardList) do
                local num = self:GetCardNum(value)
                -- 包含重复卡牌
                if num > 1 then
                    table.insert(result, series)
                    break
                end
            end
        end
    end
    return result
end

--- 获取未拥有卡牌的系列
--- @return table result 包含未拥有卡牌的系列
function M:GetNotHaveCardSeries()
    local result = {}
    local config = CollectCardManager:GetCardSeries()
    if config then
        for _, series in ipairs(config) do
            local cardList = string.split(series.card, "|")
            for _, value in ipairs(cardList) do
                local num = self:GetCardNum(value)
                -- 包含未拥有卡牌
                if num == 0 then
                    table.insert(result, series)
                    break
                end
            end
        end
    end
    return result
end

--- 获取系列中缺少的单张卡牌 ID
---@param seriesID number 卡牌系列 ID
---@return string cardID 卡牌 ID
function M:GetLackOneCardOfSeries(seriesID)
    local config = CollectCardManager:GetCardCollectionItemConfig(seriesID)
    if config then
        local cardList = string.split(config.card, "|")
        for _, value in ipairs(cardList) do
            local num = self:GetCardNum(value)
            if num == 0 then
                return value
            end
        end
    end
end

--- 获取包含普通卡牌的系列
--- @return table result 包含普通卡牌的系列
function M:GetNormalCardSeries()
    local result = {}
    local config = CollectCardManager:GetCardSeries()
    if config then
        for _, series in ipairs(config) do
            local cardList = string.split(series.card, "|")
            for _, value in ipairs(cardList) do
                local cardID = v2n(value)
                local isGold = self:IsGoldCard(cardID)
                -- 包含普通卡牌
                if not isGold then
                    table.insert(result, series)
                    break
                end
            end
        end
    end
    return result
end

--- 获取包含黄金卡牌的系列
--- @return table result 包含黄金卡牌的系列
function M:GetGoldCardSeries()
    local result = {}
    local config = CollectCardManager:GetCardSeries()
    if config then
        for _, series in ipairs(config) do
            local cardList = string.split(series.card, "|")
            for _, value in ipairs(cardList) do
                local cardID = v2n(value)
                local isGold = self:IsGoldCard(cardID)
                -- 包含黄金卡牌
                if isGold then
                    table.insert(result, series)
                    break
                end
            end
        end
    end
    return result
end

--- 获取已拥有卡牌的星级总数
--- @return integer starSum 星级总数
function M:GetAllCardStarSum()
    local starSum = 0
    for cardID, num in pairs(self.data.cardList) do
        if num > 0 then
            local itemID = v2n(cardID)
            local star = self:GetCardStar(itemID)
            starSum = starSum + star
        end
    end
    return starSum
end

--- 设置卡牌系列奖励领取状态
--- @param seriesID string|number 卡牌系列 ID
function M:SetSeriesReward(seriesID)
    seriesID = tostring(seriesID)
    if not self.data.seriesRewardList[seriesID] then
        self.data.seriesRewardList[seriesID] = false
    end
    self.data.seriesRewardList[seriesID] = true
end

--- 获取卡牌系列奖励领取状态
--- @param seriesID string|number 卡牌系列 ID
function M:GetSeriesReward(seriesID)
    seriesID = tostring(seriesID)
    if not self.data.seriesRewardList[seriesID] then
        self.data.seriesRewardList[seriesID] = false
    end
    return self.data.seriesRewardList[seriesID]
end

--- 添加点数
--- @param point number 点数
function M:AddPoint(point)
    self.data.point = self.data.point + point
end

--- 减少点数
--- @param point number 点数
function M:ReducePoint(point)
    self.data.point = math.max(self.data.point - point, 0)
end

--- 获取点数
--- @return integer point 点数
function M:GetPoint()
    return self.data.point
end

--- 打开卡包
--- @param itemID number 卡包 ID
--- @return table reward 开出的卡牌
function M:OpenCardPack(itemID, reason)
    local result = ""

    local dropList = self:GetDropList(itemID)
    if not dropList then return result end

    local cardList = {}

    for _, value in ipairs(dropList) do
        local quality = string.sub(value, 1, string.len(value) - 1)
        local star = string.sub(value, -1)
        local cardGroup = CollectCardManager:GetCardGroupByQualityStar(quality, star)
        if cardGroup then
            local random = math.random(1, #cardGroup)
            local card = cardGroup[random]
            result = result .. card.cardID .. "|"

            local cardPackStar = self:GetCardPackStar(itemID)
            local reasonCardPack = string.format("OpenCardPack_Star_%s", cardPackStar)
            self:AddCard(card.cardID, 1, reasonCardPack)

            -- 获得五星卡牌
            if v2n(star) == 5 then
                -- EventMgr:Dispatch(EventID.GET_FIVE_STAR_CARD)
                table.insert(self.data.eventList, CardEventType.GET_FIVE_STAR_CARD)
            end

            -- 收藏册仅缺少一张卡牌
            local currentNum, totalNum = self:GetCardNumOfSeries(card.seriesID)
            if totalNum - currentNum == 1 then
                local lackCardID = self:GetLackOneCardOfSeries(card.seriesID)
                local lackCard = CollectCardManager:GetCardByID(lackCardID)
                table.insert(cardList, lackCard)
            end
        end
    end

    -- 缺少卡牌排序，黄金品质在前
    table.sort(cardList, function (a, b)
        local qualityLevelA = string.len(a.quality)
        local qualityLevelB = string.len(b.quality)
        if qualityLevelA > qualityLevelB then
            return true
        end
        return false
    end)

    -- 发送缺少卡牌事件
    for _, card in ipairs(cardList) do
        -- 缺少一张普通卡牌
        if card.quality == "c" then
            -- EventMgr:Dispatch(EventID.LACK_ONE_CARD, 1)
            table.insert(self.data.eventList, CardEventType.LACK_ONE_CARD_NORMAL)
        -- 缺少一张黄金卡牌
        elseif card.quality == "cg" then
            -- EventMgr:Dispatch(EventID.LACK_ONE_CARD, 2)
            table.insert(self.data.eventList, CardEventType.LACK_ONE_CARD_GOLD)
        end
    end

    result = string.sub(result, 1, string.len(result) - 1)
    local reward = {
        packID = itemID,
        result = result
    }
    table.insert(self.data.cardPackRewardList, reward)

    local cardPackStar = self:GetCardPackStar(itemID)
    self:TrackEventCardBag(itemID, cardPackStar, reason)

    return reward
end

--- 触发事件列表
function M:TriggerEventList()
    for _, value in ipairs(self.data.eventList) do
        if value == CardEventType.GET_FIVE_STAR_CARD then
            EventMgr:Dispatch(EventID.GET_FIVE_STAR_CARD)
        elseif value == CardEventType.LACK_ONE_CARD_NORMAL then
            EventMgr:Dispatch(EventID.LACK_ONE_CARD, 1)
        elseif value == CardEventType.LACK_ONE_CARD_GOLD then
            EventMgr:Dispatch(EventID.LACK_ONE_CARD, 2)
        end
    end
    self.data.eventList = {}
end

--- 获取卡包奖励列表
--- @return table cardPackRewardList 卡包奖励列表
function M:GetCardPackRewardList()
    return self.data.cardPackRewardList
end

function M:GetCardPackRewardListLength()
    return #self.data.cardPackRewardList
end

--- 是否有卡包奖励
--- @return boolean flag 是否有卡包奖励
function M:HasCardPackReward()
    if not IsTableEmpty(self.data.cardPackRewardList) then
        return true
    end
    return false
end

--- 重置卡包奖励列表
function M:ResetCardPackRewardList()
    self.data.cardPackRewardList = {}
end

function M:removeCardPackReward(index)
    if IsTableEmpty(self.data.cardPackRewardList) then
        return;
    end

    if index <= #self.data.cardPackRewardList then
        table.remove(self.data.cardPackRewardList, index); 
    end
end

function M:GetCardPackRewardIdx(itemId)
    if IsTableEmpty(self.data.cardPackRewardList) then
        return nil;
    end

    for i = 1, #self.data.cardPackRewardList do
        if self.data.cardPackRewardList[i].packID == itemId then
            return i;
        end
    end
    return nil;
end

--- 获取掉落卡牌列表
--- @param itemID number 卡包的物品 ID
--- @return table result 掉落卡牌列表
function M:GetDropList(itemID)
    local result = {}
    self:RandomDrop(itemID, "normal", result)
    self:RandomDrop(itemID, "castle", result)
    return result
end

--- 随机掉落
--- @param itemID number 卡包的物品 ID
--- @param type string 掉落类型（普通和保底）
--- @param list table 掉落卡牌列表
function M:RandomDrop(itemID, type, list)
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemConfig then return end

    local id_use = itemConfig.id_use
    local dropConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.drop_box, id_use)
    if not dropConfig then return end

    -- 卡包掉落参数
    local dropParam

    if type == "normal" then
        dropParam = dropConfig.drop_normal
    elseif type == "castle" then
        dropParam = dropConfig.drop_castle
    end

    local dropList = string.split(dropParam, ";")
    local times = string.split(dropList[1], '-')
    local items = string.split(dropList[2], '|')
    local weights = string.split(dropList[3], '|')

    -- 掉落次数
    local randomTimes = 1
    if #times == 2 then
        local timesMin = tonumber(times[1])
        local timesMax = tonumber(times[2])
        if timesMin == timesMax then
            randomTimes = timesMin
        end
        randomTimes = math.random(timesMin, timesMax)
    end

    -- 权重总和
    local sum = GetSumNum(weights)
    for i = 1, randomTimes do
        -- 根据权重计算出掉落的物品
        local idx = GetWeightFunctionIdxByStr(weights, sum)
        table.insert(list, items[idx])
    end
end

--- 获取卡包概率
--- @param itemID number 卡包 ID
--- @return table list 概率列表
--- @return integer dropTimes 包含卡牌数量
--- @return string|nil lowStar 保底星级
--- @return integer|nil dropCastleTimes 保底卡牌数量
function M:GetCardPackRate(itemID)
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemConfig then return end

    local id_use = itemConfig.id_use
    local dropConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.drop_box, id_use)
    if not dropConfig then return end

    -- 卡包掉落参数
    local dropParam = dropConfig.drop_normal

    local dropList = string.split(dropParam, ";")
    local times = string.split(dropList[1], '-')
    local items = string.split(dropList[2], '|')
    local weights = string.split(dropList[3], '|')

    -- 掉落次数
    local dropNormalTimes = 1
    if #times == 2 then
        local timesMin = tonumber(times[1])
        local timesMax = tonumber(times[2])
        if timesMin == timesMax then
            dropNormalTimes = timesMin
        end
        dropNormalTimes = math.random(timesMin, timesMax)
    end

    -- 权重总和
    local list = {}
    local sum = GetSumNum(weights)
    for i = 1, #weights do
        local card = items[i]
        local rate = weights[i] / sum
        list[card] = rate
    end

    local drop_castle = dropConfig.drop_castle
    local dropList2 = string.split(drop_castle, ";")
    local times2 = string.split(dropList2[1], '-')
    local items2 = string.split(dropList2[2], '|')

    -- 掉落次数
    local dropCastleTimes = 1
    if #times == 2 then
        local timesMin = tonumber(times2[1])
        local timesMax = tonumber(times2[2])
        if timesMin == timesMax then
            dropCastleTimes = timesMin
        end
        dropCastleTimes = math.random(timesMin, timesMax)
    end

    local dropTimes = dropNormalTimes + dropCastleTimes
    local lowStar = string.sub(items2[1], -1)
    return list, dropTimes, lowStar, dropCastleTimes
end

--- 是否卡包
--- @param itemID number 物品 ID
--- @return boolean flag 是否卡包标记
function M:IsCardPack(itemID)
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemConfig then return false end
    if itemConfig.type_use == ItemUseType.CardPack then
        return true
    end
    return false
end

--- 获取卡包星级
--- @param itemID number 物品 ID
--- @return integer star 卡包星级
function M:GetCardPackStar(itemID)
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemConfig then return 1 end
    return itemConfig.level or 1
end

--- 获取卡牌星级
--- @param itemID number 物品 ID
--- @return integer star 卡牌星级
function M:GetCardStar(itemID)
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemConfig then return 1 end
    return itemConfig.id_use or 1
end

--- 是否黄金品质卡牌
--- @param itemID number 物品 ID
--- @return boolean flag 是否黄金品质
function M:IsGoldCard(itemID)
    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
    if not itemConfig then return false end
    if itemConfig.id_use2 then
        return true
    end
    return false
end

--endregion ------------------------------------- 普通卡 ----------------------------------------

--region ---------------------------------------- 万能卡 ----------------------------------------

--- 添加万能卡
--- @param num number 数量
function M:AddUniversalCard(num)
    local oldNum = self.data.universalCard
    self.data.universalCard = self.data.universalCard + num
    local newNum = self.data.universalCard
    self:TrackEventCardUniversal(oldNum, num, newNum, false)
end

--- 减少万能卡
--- @param num number 数量
function M:ReduceUniversalCard(num)
    local oldNum = self.data.universalCard
    self.data.universalCard = math.max(self.data.universalCard - num, 0)
    local newNum = self.data.universalCard
    self:TrackEventCardUniversal(oldNum, -num, newNum, false)
end

--- 获取万能卡数量
--- @return integer num 万能卡数量
function M:GetUniversalCardNum()
    return self.data.universalCard
end

--- 添加黄金万能卡
--- @param num number 数量
function M:AddUniversalGoldCard(num)
    local oldNum = self.data.universalGoldCard
    self.data.universalGoldCard = self.data.universalGoldCard + num
    local newNum = self.data.universalGoldCard
    self:TrackEventCardUniversal(oldNum, num, newNum, true)
end

--- 减少黄金万能卡
--- @param num number 数量
function M:ReduceUniversalGoldCard(num)
    local oldNum = self.data.universalGoldCard
    self.data.universalGoldCard = math.max(self.data.universalGoldCard - num, 0)
    local newNum = self.data.universalGoldCard
    self:TrackEventCardUniversal(oldNum, -num, newNum, true)
end

--- 获取黄金万能卡数量
--- @return integer num 黄金万能卡数量
function M:GetUniversalGoldCardNum()
    return self.data.universalGoldCard
end

--endregion ------------------------------------- 万能卡 ----------------------------------------

--region ---------------------------------------- 排行榜 ----------------------------------------

--- 发送卡牌星级总数给服务端
function M:SendPoint(type)
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.CollectCard)
    if not activityItem then return end
    local param = {}
    param.id = activityItem.info.activeId
    if type == 100 then
        param.point = self:GetSeriesFinishNum()
    elseif type == 200 then
        param.point = self:GetAllCardStarSum()
    end
    param.subType = type
    HttpClient:SendToGS("/active/activepoint", param, function(objJson)
        local isErr, _ = HttpClient:GetNetJsonError(objJson)
        if isErr then
            return
        end
        local data = objJson["data"]
        if not data then return end
    end, true)
end

--- 请求排行榜数据
--- @param callback any 回调（回传全部数据）
--- @param type number 类型
--- @param notShowWaiting boolean|nil 是否不显示转圈
function M:RequestRankData(callback, type, notShowWaiting)
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.CollectCard)
    if not activityItem then return end

    local param = {}
    param.id = activityItem.info.activeId
    param.subType = type
    -- 默认显示转圈
    local showWaiting = not notShowWaiting
    if showWaiting then
        UIMgr:ShowWaiting(true)
    end
    HttpClient:SendToGS("/active/activetranking", param, function(objJson)
        local isErr, _ = HttpClient:GetNetJsonError(objJson)
        if isErr then
            if showWaiting then
                UIMgr:ShowWaiting(false)
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(7062))
            end
            return
        end
        if showWaiting then
            UIMgr:ShowWaiting(false)
        end
        local data = objJson["data"]     -- 排行榜数据
        if not data then return end
        -- 回传数据
        if callback then
            callback(data)
        end
    end, true)
end

--endregion ------------------------------------- 排行榜 ----------------------------------------

--region ----------------------------------------- 红点 -----------------------------------------

--- 设置红点
--- @param cardID number 卡牌 ID
--- @param checked boolean|nil 是否查看红点
function M:SetRedPoint(cardID, checked)
    cardID = tostring(cardID)

    -- 首次获得，标记红点
    if self.data.redPointList[cardID] == nil then
        self.data.redPointList[cardID] = true
        CollectionItems:ReflushMainPoint()
    end

    -- 查看了红点
    if checked then
        self.data.redPointList[cardID] = false
    end
end

--- 获取红点
--- @param cardID number 卡牌 ID
--- @return boolean|nil hasRedPoint 是否有红点
function M:GetRedPoint(cardID)
    return self.data.redPointList[tostring(cardID)]
end

--- 获取卡牌系列的红点数量
--- @param seriesID number 卡牌系列 ID
--- @return integer redPointNum 红点数量
function M:GetRedPointNumOfSeries(seriesID)
    local redPointNum = 0
    local config = CollectCardManager:GetCardCollectionItemConfig(seriesID)
    if config then
        local cardList = string.split(config.card, "|")
        for _, cardID in ipairs(cardList) do
            local hasRedPoint = self:GetRedPoint(cardID)
            if hasRedPoint then
                redPointNum = redPointNum + 1
            end
        end
    end
    return redPointNum
end

--- 获取全部卡牌的红点数量
--- @return integer redPointNum 红点数量
function M:GetRedPointNumAll()
    local redPointNum = 0
    for _, hasRedPoint in pairs(self.data.redPointList) do
        if hasRedPoint then
            redPointNum = redPointNum + 1
        end
    end
    return redPointNum
end

--endregion -------------------------------------- 红点 -----------------------------------------

--region ----------------------------------------- 引导 -----------------------------------------

function M:FirstGuide()
    -- 第一步引导回调，打开收藏册界面
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
        UI_SHOW(UIDefine.UI_CollectionCenter, 3)
		NetPushViewData:RemoveViewByIndex(PushDefine.UI_GuideMask)
		NetPushViewData:CheckOtherView(true)
    end

    -- 第一步引导帮助按钮
    local centerPos = LimitActivityController:GetCollectionPos()
    local view = UIMgr:GetUIItem(UIMgr:GetNowMainUI())
    if view and view.ui then
        local targetGo = view.ui["m_goCollect"]
        centerPos = UIRectPosFit(targetGo)
    end
    Log.InfoPink("centerPos", centerPos)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 90},                -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {2, 2},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 + 2, 0, 0, 0},   -- 箭头位置
        {-0.5, -1.5, 51029152},                    -- 对话框位置和内容
        "Sprite/new_hero_0/headFrame_1.png",   -- 对话框头像
        nil,
    })
end

--endregion -------------------------------------- 引导 -----------------------------------------

--region ----------------------------------------- 埋点 -----------------------------------------

--- 埋点 卡牌增减
function M:TrackEventCardChange(reason, isNewCard, cardID, seriesID, cardOldNum, cardChangeNum, cardNewNum,
    seriesOldNum, seriesNewNum, totalOldNum, totalNewNum)
    local thinkTable = {
        ["reason"] = reason,                    -- 卡牌变化的原因
        ["Card_isnew"] = isNewCard,             -- 是否新卡
        ["Card_ID"] = cardID,                   -- 变化的卡牌ID
        ["Card_GroupID"] = seriesID,            -- 变化卡牌归属的卡组ID
        ["Card_OldNum"] = cardOldNum,           -- 卡牌变化前数量
        ["Card_ChangeNum"] = cardChangeNum,     -- 卡牌变化数量
        ["Card_NewNum"] = cardNewNum,           -- 卡牌变化后数量
        ["Card_OldGroupNum"] = seriesOldNum,    -- 卡组变化前进度
        ["Card_NewGroupNum"] = seriesNewNum,    -- 卡组变化后进度
        ["Card_OldProgress"] = totalOldNum,     -- 总卡牌变化前进度
        ["Card_NewProgress"] = totalNewNum,     -- 总卡牌变化后进度
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.CardChange, thinkTable)
end

--- 埋点 卡包获得
function M:TrackEventCardBag(packID, packStar, reason)
    local thinkTable = {
        ["CardBag_ID"] = packID,         -- 卡包ID
        ["CardBag_star"] = packStar,     -- 卡包星级
        ["reason"] = reason,             -- 卡包获得原因
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.CardBag, thinkTable)
end

--- 埋点 万能卡
function M:TrackEventCardUniversal(oldNum, changeNum, newNum, isGold)
    local thinkTable = {
        ["Card_OldNum"] = oldNum,        -- 万能卡变化前数量
        ["Card_ChangeNum"] = changeNum,  -- 万能卡变化数量
        ["Card_NewNum"] = newNum,        -- 万能卡变化后数量
        ["Card_IsGold"] = isGold,        -- 是否黄金万能卡
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.CardUniversal, thinkTable)
end

--endregion -------------------------------------- 埋点 -----------------------------------------

return NetCollectCardData
local UI_MailConent = Class(BaseView)

local MailTemplate_LeagueBossKillReward = require "UI.Mail.MailTemplate_LeagueBossKillReward"
local MailTemplate_LeagueBossRankReward = require "UI.Mail.MailTemplate_LeagueBossRankReward"
local MailTemplate_ArenaSettleReward = require "UI.Mail.MailTemplate_ArenaSettleReward"
local MailTemplate_TrainDepart = require "UI.Mail.MailTemplate_TrainDepart"
local MailTemplate_TrainArrival = require "UI.Mail.MailTemplate_TrainArrival"
local MailTemplate_TrainReward = require "UI.Mail.MailTemplate_TrainReward"
local MailTemplate_WorldBossRankReward = require "UI.Mail.MailTemplate_WorldBossRankReward"

local ItemBase = require("UI.Common.BaseSlideItem")
local ItemReward = Class(ItemBase)

function UI_MailConent:OnInit()
    self.sliderReward = require("UI.Common.SlideRect").new()
    self.curMailId = 0

    --底部描述
    local day = MailManager:GetMailDeleteRentionDay()
    self.ui.m_txtNotice.text = LangMgr:GetLangFormat(70000586,day)
end

function UI_MailConent:OnCreate(param)
    if param then
        self.mailData = param
    end
    if self.mailData == nil then
        return
    end

    self.curMailId = self.mailData.id
    --附件
    local rewardCell = self.ui.m_goRewardCell
    SetActive(rewardCell,false)
    self.sliderReward:Init(GetComponent(self.ui.m_goGiftviewPort,UEUI.ScrollRect),1)
    local _items = {}
    for i = 1, 10 do
        _items[i] = ItemReward.new()
        _items[i]:Init(UEGO.Instantiate(rewardCell.transform))
        _items[i].panel = self
    end
    self.sliderReward:SetItems(_items,5,Vector2.New(0,0))
    
    self:RefreshMailPanel()
end

function UI_MailConent:OnRefresh(param)
    
end

function UI_MailConent:onDestroy()
    
end

function UI_MailConent:onUIEventClick(go,param)
    local name = go.name
    if name == "btnClose" then
        self:Close()
    elseif name == "m_btnClaim" then
        if self.curMailId then
            Log.Info("领取附件")
            if self.mailData then
                MailManager:ClaimMail(self.mailData.type,self.mailData.id)
            end
        end
    elseif name == "m_btnDelete" then
        if self.curMailId then
            local mailData = self.mailData
            if mailData then
                if MailManager:HasReward(mailData) and not MailManager:HasClaimed(mailData) then
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000668))
                    return
                end

                Log.Info("删除指定邮件"..mailData.id)
                UI_SHOW(UIDefine.UI_Reconfirm, LangMgr:GetLang(89), LangMgr:GetLang(70000656),
                        function ()
                            UI_CLOSE(UIDefine.UI_Reconfirm)
                            MailManager:DeleteMail(mailData.type,mailData.id)
                            self:Close()
                        end,
                        nil)
            end
        end
    elseif name == "m_btnTranslate" then
        if self.curMailId then
            local mailData = self.mailData
            if mailData then
                local td = MailManager:GetMailTranslateData(mailData.id)
                if not td.title then
                    local title = MailManager:GetMailTitle(mailData)
                    MailManager:SendTranslateInfo(mailData.id,1,title,function (data)
                        self:RefreshMailPanel()
                        --self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
                    end)
                end

                if not td.content then
                    local content = MailManager:GetMailContentText(mailData)
                    MailManager:SendTranslateInfo(mailData.id,2,content,function (data)
                        self:RefreshMailPanel()
                        --self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
                    end)
                end

                if not td.subTitle then
                    local subTitle = MailManager:GetMailSubTitle(mailData)
                    MailManager:SendTranslateInfo(mailData.id,3,subTitle,function (data)
                        self:RefreshMailPanel(true)
                        --self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
                    end)
                end

                td.inTranslate = true
                if td.title or td.content or not td.subTitle then
                    -- local force = td.content~=nil
                    self:RefreshMailPanel()
                    --self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
                end
            end
        end
    elseif name == "m_btnBackTranslate" then
        if self.curMailId then
            local mailData = self.mailData
            if mailData then
                local td = MailManager:GetMailTranslateData(mailData.id)
                td.inTranslate = false
                self:RefreshMailPanel()
                --self:RefreshMailList(self.curMailType,true,false,self.curMailIndex)
            end
        end
    end
end

function UI_MailConent:RefreshMailPanel(force)
    local mailData = self.mailData
    if mailData then
        local tempTemplate = self:GetMailContentPanelByType(mailData.content.type)
        --发送已读
        if not MailManager:HasRead(mailData) then
            MailManager:ReadMail(mailData.type,mailData.id)
        end
        SetActive(self.ui.m_goMailInfoCenter,true)
        --top 标题和日期
        if tempTemplate then
            self.ui.m_txtTitle.text = MailManager:GetMailTitle(mailData)
        else
            self.ui.m_txtTitle.text = LangMgr:GetLang(7071)--未知邮件
        end
        SetActive(self.ui.m_goMailInfoTop,true)
        --按钮
        SetActive(self.ui.m_btnDelete,true)
        if mailData.content.type == MAIL_CONTENT_TYPE.TEXT then
            local td = MailManager:GetMailTranslateData(mailData.id)
            if td and td.inTranslate then
                SetActive(self.ui.m_btnTranslate,false)
                SetActive(self.ui.m_btnBackTranslate,true)
            else
                SetActive(self.ui.m_btnTranslate,true)
                SetActive(self.ui.m_btnBackTranslate,false)
            end
        else
            SetActive(self.ui.m_btnTranslate,false)
            SetActive(self.ui.m_btnBackTranslate,false)
        end

        --邮件剩余时间
        local rentionDay = MailManager:GetMailDeleteRentionDay()
        local nowTime = TimeMgr:GetServerTime()
        local remainDay = rentionDay - Mathf.Floor((nowTime - mailData.create_at)/(60*60*24))
        if remainDay <= 0 then
            self.ui.m_txtTime.text = TimeMgr:StampToDateDes(mailData.create_at)..GetStrRichColor(LangMgr:GetLang(70000589),"ff7787")
        else
            self.ui.m_txtTime.text = TimeMgr:StampToDateDes(mailData.create_at)..GetStrRichColor(LangMgr:GetLangFormat(70000588,remainDay),"ff7787")
        end

        -- bottom 附件列表
        if mailData.rewards and next(mailData.rewards) then
            --数据二次封装
            local data = {}
            local hasClaimed = MailManager:HasClaimed(mailData)
            for k,v in pairs(mailData.rewards) do
                local temp = {}
                temp.code = v.code
                temp.amount = v.amount
                temp.hasClaimed = hasClaimed
                table.insert(data,temp)
            end

            self.sliderReward:SetData(data,1)
            SetActive(self.ui.m_goMailInfoBottom,true)

            local notClaimed = not MailManager:HasClaimed(mailData)
            if notClaimed then
                SetActive(self.ui.m_btnClaim,true)
                SetActive(self.ui.m_btnHasClaim,false)
            else
                SetActive(self.ui.m_btnClaim,false)
                SetActive(self.ui.m_btnHasClaim,true)
            end
        else
            SetActive(self.ui.m_goMailInfoBottom,false)
        end
        --邮件内容
        local parent = self.ui.m_goMailInfoCenter

        if self.mailTemplate then
            UEGO.Destroy(self.mailTemplate)
            self.mailTemplate = nil
        end
        local tempTemplate = self:GetMailContentPanelByType(mailData.content.type)
        if tempTemplate then
            self.mailTemplate = CreateGameObjectWithParent(tempTemplate,parent)
            self:FillTemplate(mailData,self.mailTemplate)
            -- local rectTrans = GetComponent(self.mailTemplate, UE.RectTransform)
            -- UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(rectTrans)
            -- parent:SetInsetAndSizeFromParentEdge(2,0,rectTrans.sizeDelta.y)
        else
            self.mailTemplate = CreateGameObjectWithParent(self.ui.m_goTemplateUnsupportedType,parent)
        end
    else
        SetActive(self.ui.m_goMailInfoTop,false)
        SetActive(self.ui.m_goMailInfoCenter,false)
        SetActive(self.ui.m_goMailInfoBottom,false)
    end
end

function UI_MailConent:GetMailContentPanelByType(type)
    local template = nil
    if type == MAIL_CONTENT_TYPE.TEXT or type == MAIL_CONTENT_TYPE.WORLD_BOSS_JOIN_REWARD or type == MAIL_CONTENT_TYPE.TEXT_LANG then
        template = self.ui.m_goTemplateText
    elseif type == MAIL_CONTENT_TYPE.TEST then
    elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_KILL_REWARD then
        template = self.ui.m_goTemplateLeagueBossRank
    elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_RANK_REWARD then
        template = self.ui.m_goTemplateLeagueBossRank
    elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_DEPART then
        template = self.ui.m_goTemplateTrainDepart
    elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_ARRIVAL then
        template = self.ui.m_goTemplateTrainArrival
    elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_REWARD then
        template = self.ui.m_goTemplateTrainReward
    elseif type == MAIL_CONTENT_TYPE.ARENA_SETTLE_REWARD then
        template = self.ui.m_goTemplateArenaSettleReward
    elseif type == MAIL_CONTENT_TYPE.WORLD_BOSS_RANK_REWARD then
        template = self.ui.m_goTemplateWorlBossRank
    end
    return template
end

function UI_MailConent:FillTemplate(mailData,template)
    local content = mailData.content
    local type = content.type
    local bytes = content.content

    if type == MAIL_CONTENT_TYPE.TEXT then
        self:FillTemplate_Text(bytes,template,mailData)
    elseif type == MAIL_CONTENT_TYPE.TEST then
    elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_KILL_REWARD then
        if not self.mtLeagueBossRankReward then
            self.mtLeagueBossRankReward = MailTemplate_LeagueBossRankReward.new()
        end
        self.mtLeagueBossRankReward:FillTemplate(1,bytes,template,mailData)
    elseif type == MAIL_CONTENT_TYPE.LEAGUE_BOSS_RANK_REWARD then
        if not self.mtLeagueBossRankReward then
            self.mtLeagueBossRankReward = MailTemplate_LeagueBossRankReward.new()
        end
        self.mtLeagueBossRankReward:FillTemplate(2,bytes,template,mailData)
    elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_DEPART then
        --贸易货车，火车发车邮件
        if not self.mtTrainDepart then
            self.mtTrainDepart = MailTemplate_TrainDepart.new()
        end
        self.mtTrainDepart:FillTemplate(bytes,template,mailData)
    elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_ARRIVAL then
        --贸易货车，火车到达邮件
        if not self.mtTrainArrival then
            self.mtTrainArrival = MailTemplate_TrainArrival.new()
        end
        self.mtTrainArrival:FillTemplate(bytes,template,mailData)
    elseif type == MAIL_CONTENT_TYPE.TRADE_TRAIN_REWARD then
        --贸易货车，火车奖励邮件
        if not self.mtTrainReward then
            self.mtTrainReward = MailTemplate_TrainReward.new()
        end
        self.mtTrainReward:FillTemplate(bytes,template,mailData)
    elseif type == MAIL_CONTENT_TYPE.ARENA_SETTLE_REWARD then
        if not self.mtArenaSettleReward then
            self.mtArenaSettleReward = MailTemplate_ArenaSettleReward.new()
        end
        self.mtArenaSettleReward:FillTemplate(bytes, template, mailData)
    elseif type == MAIL_CONTENT_TYPE.WORLD_BOSS_RANK_REWARD then
        if not self.mtWorldBossRankReward then
            self.mtWorldBossRankReward = MailTemplate_WorldBossRankReward.new()
        end
        self.mtWorldBossRankReward:FillTemplate(bytes, template, mailData)
    elseif type == MAIL_CONTENT_TYPE.WORLD_BOSS_JOIN_REWARD then
        self:FillTemplate_WorldBossJoinText(bytes, template, mailData);
    elseif type == MAIL_CONTENT_TYPE.TEXT_LANG then
        self:FillTemplate_LangText(bytes, template, mailData);
    end
end

function UI_MailConent:FillTemplate_Text(bytes,template,mailData)
    local textGo = GetChild(template, "Viewport/Content/text",UEUI.Text)
    if textGo then
        local contentText = MailManager:GetMailContentText(mailData)
        textGo.OnClick = function(url)
            url = string.gsub(url, "%s+", "")
            url = string.gsub(url, "\"", "")
            url = string.gsub(url, "'", "")
            CS.UnityEngine.Application.OpenURL(url);
        end
        textGo.text = contentText
    end
end

function UI_MailConent:FillTemplate_WorldBossJoinText(bytes, template, mailData)
    local textGo = GetChild(template, "Viewport/Content/text", UEUI.Text)
    if textGo then
        local data = ProtocManager:Decode("marge.topia.mail.ContentWorldBossJoinReward", bytes)
        if not data then
            Log.Error("protocManager decode error")
            return
        end

        local content_id = data.content_id;
        local boss_id = data.boss_id;
        local join_times = data.join_times;
        textGo.text = LangMgr:GetLangFormat(content_id, WorldBossManager:GetMonsterName(boss_id), join_times);
    end
end

function UI_MailConent:FillTemplate_LangText(bytes, template, mailData)
    local textGo = GetChild(template, "Viewport/Content/text", UEUI.Text)
    if textGo then
        local data = ProtocManager:Decode("marge.topia.mail.ContextLang", bytes)
        if not data then
            Log.Error("protocManager decode error")
            return
        end

        local content_id = data.content_id;
        local args = data.args;
        if content_id and args then
            local dic = {};
            for i = 1, #args do
                local langStr = LangMgr:GetLang(args[i]);
                table.insert(dic, langStr)
            end
            textGo.text = LangMgr:GetLangFormat(content_id, table.SafeUnpack(dic))
        end
    end
end

--region ItemReward
function ItemReward:UpdateData(data,index)
    local itemId = data.code
    local count = data.amount
    local hasClaimed = data.hasClaimed
    self.itemId = itemId
    SetImageSprite(self.imgItemIcon,ItemConfig:GetIcon(itemId),false)
    self.txtItemCnt.text = "x"..NumToGameString(count)
    SetActive(self.goHasClaim,hasClaimed)
end

function ItemReward:OnClick()
    if self.itemId then
        -- Log.Info("click"..self.itemId)
        UI_SHOW(UIDefine.UI_ItemTips,self.itemId)
    end
end

function ItemReward:OnInit(transform)
    self.transform = transform.transform
    self.imgItemIcon = GetChild(transform, "imgIcon",UEUI.Image)
    self.txtItemCnt = GetChild(transform, "txtCnt",UEUI.Text)
    self.goHasClaim = GetChild(transform, "hasClaim")

    self.btn =  GetComponent(transform,UEUI.Button)
    self.btn.onClick:RemoveAllListeners()
    local function onStepClick()
        self:OnClick()
    end
    self.btn.onClick:AddListener(onStepClick)
end

function ItemReward:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function ItemReward:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end
--endregion

return UI_MailConent
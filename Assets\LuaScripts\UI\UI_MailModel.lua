local UI_MailModel = {}

UI_MailModel.config = {["name"] = "UI_Mail", ["layer"] = UILayerType.Normal, ["type"] = UIType.Normal, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_MailModel:Init(c)
    c.ui = {}    
    c.ui.m_togCell = GetChild(c.uiGameObject,"bg/bottom/tabs/m_togCell",UEUI.Toggle)
    c.ui.m_goMagic = GetChild(c.uiGameObject,"bg/ResourceItems/m_goMagic")
    c.ui.m_txtMagic = GetChild(c.uiGameObject,"bg/ResourceItems/m_goMagic/m_txtMagic",UEUI.Text)
    c.ui.m_doMagic = GetChild(c.uiGameObject,"bg/ResourceItems/m_goMagic/m_doMagic",TweenAnim)
    c.ui.m_goCoin = GetChild(c.uiGameObject,"bg/ResourceItems/m_goCoin")
    c.ui.m_doCoin = GetChild(c.uiGameObject,"bg/ResourceItems/m_goCoin/m_doCoin",TweenAnim)
    c.ui.m_txtCoin = GetChild(c.uiGameObject,"bg/ResourceItems/m_goCoin/m_txtCoin",UEUI.Text)
    c.ui.m_goDiamond = GetChild(c.uiGameObject,"bg/ResourceItems/m_goDiamond")
    c.ui.m_txtDiamond = GetChild(c.uiGameObject,"bg/ResourceItems/m_goDiamond/m_txtDiamond",UEUI.Text)
    c.ui.m_doDiamond = GetChild(c.uiGameObject,"bg/ResourceItems/m_goDiamond/m_doDiamond",TweenAnim)
    c.ui.m_goRedDiamond = GetChild(c.uiGameObject,"bg/ResourceItems/m_goRedDiamond")
    c.ui.m_txtRedDiamond = GetChild(c.uiGameObject,"bg/ResourceItems/m_goRedDiamond/m_txtRedDiamond",UEUI.Text)
    c.ui.m_doRedDiamond = GetChild(c.uiGameObject,"bg/ResourceItems/m_goRedDiamond/m_doRedDiamond",TweenAnim)
    c.ui.m_imgRedDiamBuy = GetChild(c.uiGameObject,"bg/ResourceItems/m_goRedDiamond/m_imgRedDiamBuy",UEUI.Image)
    c.ui.m_goEmptyTip = GetChild(c.uiGameObject,"bg/content/center/m_goEmptyTip")
    c.ui.m_goCell = GetChild(c.uiGameObject,"bg/content/center/m_goCell")
    c.ui.m_goSlideRect = GetChild(c.uiGameObject,"bg/content/center/m_goSlideRect")
    c.ui.m_txtNotice = GetChild(c.uiGameObject,"bg/content/center/m_txtNotice",UEUI.Text)
    c.ui.m_txtMailCnt = GetChild(c.uiGameObject,"bg/content/center/m_txtAuto111/m_txtMailCnt",UEUI.Text)
    c.ui.m_btnReceiveAll = GetChild(c.uiGameObject,"bg/content/center/btns/m_btnReceiveAll",UEUI.Button)
    c.ui.m_btnDeleteAll = GetChild(c.uiGameObject,"bg/content/center/btns/m_btnDeleteAll",UEUI.Button)
    c.ui.m_goEmptyTip2 = GetChild(c.uiGameObject,"bg/content/right/content/m_goEmptyTip2")
    c.ui.m_goMailInfoTop = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoTop")
    c.ui.m_txtTitle = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoTop/m_txtTitle",UEUI.Text)
    c.ui.m_txtTime = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoTop/m_txtTime",UEUI.Text)
    c.ui.m_btnDelete = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoTop/m_btnDelete",UEUI.Button)
    c.ui.m_btnTranslate = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoTop/m_btnTranslate",UEUI.Button)
    c.ui.m_btnBackTranslate = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoTop/m_btnBackTranslate",UEUI.Button)
    c.ui.m_goMailInfoCenter = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoCenter")
    c.ui.m_goMailInfoBottom = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoBottom")
    c.ui.m_goGiftviewPort = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoBottom/m_goGiftviewPort")
    c.ui.m_goRewardCell = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoBottom/m_goGiftviewPort/m_goRewardCell")
    c.ui.m_btnClaim = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoBottom/m_btnClaim",UEUI.Button)
    c.ui.m_btnHasClaim = GetChild(c.uiGameObject,"bg/content/right/content/m_goMailInfoBottom/m_btnHasClaim",UEUI.Button)
    c.ui.m_goTemplateText = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateText")
    c.ui.m_goTemplateArenaSettleReward = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateArenaSettleReward")
    c.ui.m_goSpine = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateArenaSettleReward/Viewport/Content/list/bg/jjcRankItem/m_goSpine")
    c.ui.m_goTemplateLeagueBossRank = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateLeagueBossRank")
    c.ui.m_goTemplateUnsupportedType = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateUnsupportedType")
    c.ui.m_goTemplateTrainDepart = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainDepart")
    c.ui.m_goMemberItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainDepart/Scroll View/Viewport/Content/main/prefab/m_goMemberItem")
    c.ui.m_goTemplateTrainArrival = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival")
    c.ui.m_goRewardItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival/Scroll View/Viewport/Content/main/prefab/m_goRewardItem")
    c.ui.m_goMemberItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival/Scroll View/Viewport/Content/main/prefab/m_goMemberItem")
    c.ui.m_goRecordItem = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainArrival/Scroll View/Viewport/Content/main/prefab/m_goRecordItem")
    c.ui.m_goTemplateTrainReward = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateTrainReward")
    c.ui.m_goTemplateWorlBossRank = GetChild(c.uiGameObject,"mailTemplate/m_goTemplateWorlBossRank")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_MailModel
local UI_AthleticTalentHelpModel = {}

UI_AthleticTalentHelpModel.config = {["name"] = "UI_AthleticTalentHelp", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1,["onEscape"] = false}

function UI_AthleticTalentHelpModel:Init(c)
    c.ui = {}    
    c.ui.m_spui = GetChild(c.uiGameObject,"spine (1)/m_spui",CS.Spine.Unity.SkeletonGraphic)
    c.ui.m_transDesc = GetChild(c.uiGameObject,"bg (2)/Context/m_transDesc",UE.Transform)
    c.ui.m_togG = GetChild(c.uiGameObject,"bg (2)/m_togG",UEUI.ToggleGroup)
    c.ui.m_goTog = GetChild(c.uiGameObject,"bg (2)/m_togG/m_goTog")
    c.ui.m_btnPrev = GetChild(c.uiGameObject,"bg (2)/PageUI/m_btnPrev",UEUI.Button)
    c.ui.m_btnNext = GetChild(c.uiGameObject,"bg (2)/PageUI/m_btnNext",UEUI.Button)
    c.ui.m_scrollview = GetChild(c.uiGameObject,"bg (2)/m_scrollview",UEUI.ScrollRect)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_AthleticTalentHelpModel
---



---

NodeMgr = require "Game.MapLogic.NodeManager".new()

MapController = require "Game.MapLogic.MapController".new()
LimitActivityController = require "Game.MapLogic.LimitActivityController".new()
WorkerController = require "Game.MapLogic.WorkerController".new()
WorkerHelpItem = require "Game.MapLogic.WorkerHelpItem"
DropController = require "Game.MapLogic.DropController"
ClearController = require "Game.MapLogic.ClearController".new()
FindController = require "Game.MapLogic.FindController"
GuideController = require "Game.MapLogic.GuideController".new()
HeroController = require("Game.MapLogic.HeroController").new()
RecipesController = require("Game.MapLogic.RecipesController").new()
--CollectionController = require("Game.MapLogic.CollectionController").new()
DailyTaskController = require("Game.MapLogic.DailyTaskController").new()
MagicElvesController = require "Game.MapLogic.MagicElvesController".new()
TouchMoveController = require "Game.MapLogic.TouchMoveController".new()


MapControllerVisit = require "Game.MapLogic.MapControllerVisit".new()

GAME_APP_ID = "2059"
GAME_CHANNEL_ID = "2059002"
GAME_QUESTION_VERSION = "2059-EA-ZS"

BloomLayer = 19
MAP_ID_HOME_UNKNOW = -9898 --1和2的叠加态

MAP_SIZE_MAX = 100
MAP_ID_MAIN = 1 --主地图Id
MAP_ID_SECOND = 2--第二营地
MAP_ID_ZOO = 7 -- 

FREE_TIME = 30--采集免费时间
QUICK_COLLECT_TIME = 1.5--快速采集占用时间
REDWORK_SPEED_TIME = 1
PLAY_VIEW_TIME = 14
MAXResourceCount = 6
RECIPE_REFRESH_MAX = 30
RECIPE_REFRESH_UP = 5
RANKLEVEL=9--排行榜奖励变更等级

WORKER_CNT_NORMAL = 2--普通工人数量
WORKER_CNT_PAY = 1--付费工人数量
WORKER_CNT_EXPAND = 3--扩展工人数量

WORKER_ID_PAY = 3--工人的ID 付费
WORKER_START_ID_EXPAND = WORKER_CNT_NORMAL + WORKER_CNT_PAY + 1--扩展工人索引
WORKER_END_ID_EXPAND = WORKER_CNT_NORMAL + WORKER_CNT_PAY + WORKER_CNT_EXPAND


BATTLE_FRAME_SECONDS = 24--每秒多少帧
BATTLE_FRAME_DURATION = 1/BATTLE_FRAME_SECONDS--每帧多少秒
BATTLE_VIEW_HEIGHT_SCALE = 0.5--斜视角度宽高比

BATTLE_ENTER_TIME = 1--进场时间
BATTLE_ENTER_DELAY = 0.2--延迟进场时间

BATTLE_MAIN_LAYER_OFFSET = 50--战斗层级偏移
BATTLE_BOTTOM_EFFECT_LAYER_OFFSET = 45--战斗底层特效层级偏移

LockState = {
	Lock = 0,
	Unlock = 1
}

--this is not init at there
SortingLayerInGame = {
    Default = 0,
    WORLD_BACKGROUND = 0,
    WORLD_TILE = 0,
    WORLD_LINE = 0,
    WORLD_ITEM = 0,
    WORLD_CLOUD = 0,
    WORLD_STAR = 0,
    WORLD_PICK = 0,
    WORLD_FLY = 0,
    UI_BOTTOM = 0,
    UI_MIDDLE = 0,
    UI_TOP = 0,
}

--资源加载优先级
AssetLoadPriority = {
	VeryHigh = 1,  -- 很高
    High     = 3,  -- 高
    Normal   = 5,  -- 一般
    Low      = 7,  -- 低
    VeryLow  = 9   -- 很低
}

FlyId ={
    Air = -1,
}

ItemID = {
    ENERGY = 1,
    DIAMOND = 2,
    COIN = 3,
    MAGIC = 4,
    EXP = 5,
    LimitEnergy = 6,
    HERO_ENERGY = 7,
	WEEK_ACTIVE = 8, --每周的活跃值
    ZOO_EXP = 9,
    --_RESOURCE_MAX = 10,
    FoodZoo = 11,--TD Zoo Food FeedProgress
	EATFOODZOO = 12,
	
	SeasonEnergy = 13,
	DOLLAR = 14,
    RED_DIAMOND = 15,
    MEIRIXUNZHANG = 16,
    TUANDUIGONGXIAN = 17,
	_RESOURCE_MAX = 18,

	COLLECTION = 50,
	SkinBtn = 51,--皮肤
	EmojiUnLockItem = 320,--表情包解锁道具
	
	
	
    MAGIC_HEART_0 = 121,
    WAN_NENG_BAO_SHI = 134,
	WAN_NENG_BAO_SHI2 = 135,
    RedWork = 145,
	
	BlueWork = 154,
	
	Head_UIMainFace = 201, -- 主界面 头像
	
	VisitId = 301,
	VisitTree = 302,
	
    Infinity_Energy = 900,

    Worker1 = 30050,
    Worker2 = 30051,
    Worker3 = 149,
	Worker4 = 148,
    PortMaterial = 65000,
    TaskFinishStar = 91099,
    Axe = 79008,
    Boom = 79009,
	OpenLandId = 71000, --花园岛花瓣开地
	Day7Score =  70001,--7日积分

	Flower_Competition = 29101, --鲜花比拼
	LuckOreItem = 53100,         --幸运神矿积分道具
    RelicKey = 54204,  -- 遗迹考古放大镜道具
	DiceItem = 52409, --骰子次数道具
	CatFood = 198420, --猫碗 第一次必掉
	CatTree = 198423, --猫高级矿
	CatTree2 = 198424, --猫高级矿
	SkiingCar = 35105, --最高级雪车
    DailyTargetItem1 = 29111, --每日积分道具 周一
    DailyTargetItem2 = 29112, --每日积分道具 周二
    DailyTargetItem3 = 29113, --每日积分道具 周三
    DailyTargetItem4 = 29114, --每日积分道具 周四
    DailyTargetItem5 = 29115, --每日积分道具 周五
    DailyTargetItem6 = 29116, --每日积分道具 周六
    DailyTargetItem7 = 29117, --每日积分道具 周七
    UniversalCard = 60007,      -- 万能卡
    UniversalGoldCard = 60008,  -- 黄金万能卡
    ColorfulWindmill = 70999,   -- 动物园五彩风车
	
	---BagItem
	HeroTicket = 1000001,
	EquipmentTicket = 1000002,
	JJcScore = 5002,
	JJcFightTicket = 1000003,
	JJcRefreshTicket = 1000004,
    SlgDungeonTick = 10000071,    -- slg关卡挑战券
    SlgDungeonTickServer = 1000007,    -- slg关卡挑战券服务用itemid
    TradeContract = 1000005,  -- 贸易合同
	TopFightCoin = 1000010,--争霸币
}

MapType = {
    Main = 1,
    Limit = 2,
    Zoo = 3,
    Main2 = 4,
}

ItemUseType = {
    Normal = 1,
    Tree = 2,
    BigTree = 3,
    Castle = 4,
    Box = 5,
    MixDiamond = 6,
    Field = 7,
    RedWorker = 8,
    OrderGift = 9,
    Hero = 10,
    Resource = 11,
    Material = 12,
    BuildingUp = 13, --红工人可以建造的建筑
    CastleUnBuild = 14,
    BigTreeBuild = 15,
    HeroFly = 16,
    -- PortUpdateMaterial=17,
    -- PortFly = 18,
    Activity = 19,
    Floater = 20,
    DungeonBuild = 21,
    DungeonTree = 22,
    BoxAD = 23,
    Infinity = 24,
    GameCurrencyBox = 25,
    PayBox = 26,
    CutDownBox = 27,
	BlueWorker = 28, --绿皮怪
    CutDown = 29, --绿皮怪

    Producer = 31, --tree dead

	Spatula = 34, --铲子
    DropOnce = 35,
    AutoBuild = 36, --自动建造
    NewWorker = 37, --自动建造
    FieldGrade = 38,--TD Type —— 2021ChristmasActivity NewItem, Can Harvest more, Over to trinket
	NPCUnBuild = 39 ,--未建造的NPC元素
	ChristmasTree = 40,--TD Type —— 2021ChristmasActivity NewItem, Click To Play Spine And PlayMusic
	NPCFood = 41, --Npc吃的食物
	--NPCCommon = 42 , --NPC食物的合成物
    AnimalZoo = 43,--TD Type —— Zoo Animal NewItem, Eat Food Add Progress To Get Awards
    ZooHouse = 44,
    Boom = 45,
    MaxTree = 46,
    Torch = 47,
    GetItem = 48,
	GridFloater = 100,
	GetZooFood = 101, --动物园果实
	NpcOrder = 49,
	Toy = 50,
	HeadVip = 51, --季节活动vip头像
	ObjSkinItem = 52,--皮肤道具
	ObjItemVisit = 53,
	ObjItemVisitTree = 54,
	Dress = 102,--地块装饰
	Obstacles = 103, --点击显示弹出文字
	IsGetItem = 104, --获得过某种物品触发事件
	SeasonIsland = 105,
	
	ObjItemTreeDoubleOre = 55,
	ObjItemTriggerGift = 56,
	AIWorker = 57,

	
	BoxTinyGame = 1001, --小游戏宝箱
	TinyGameGuideBg = 1010, --小游戏版本引导背景
	TinyGameGuideItem = 1011, --小游戏引导道具
	Souvenir = 58,
	ObjItemHalloweenOre = 59,   -- 万圣节限时矿
	ObjItemHalloweenItem = 60,   -- 万圣节凋落物

    ObjItemConditionBox = 61, -- 条件满足以后 打开宝箱掉物品

    SmallStorageBoxMaterial = 62,-- 收纳箱 里面的物品
	ObjItemChooseBox = 63,--月季奖励自选宝箱
	ObjItemCD = 64,--建造加速道具
    ObjItemHelpNPC = 65,-- 帮助NPC
    ToyOre = 66,                -- 玩具矿
    ObjItemConditionItem = 67,--条件宝箱的关键物品
	SkiingTree = 68,--滑雪矿
	ObjSkiingItem = 69, --滑雪A链
	ObjSkiingRewardItem=70,--滑雪B链
	ObjItemSkinDecorate = 71,--皮肤挂件
    ObjItemEnd2Less = 72,--圣诞无尽
    ObjItemStackTree = 73,--树堆
	ObjItemDice = 74,--骰子活动道具
	ObjItemADFree = 75,--免广告道具
	ObjItemMultiple3 = 76,--增加3倍采集BUFF的剩余时间 道具
	ObjItemMultiple9 = 77,--增加9倍采集BUFF的剩余时间 道具
    ObjItemMultiple27 = 78,--增加27倍采集BUFF的剩余时间 道具
    ObjItemMagnetBuff = 79,--磁铁BUFF道具
    ObjItemDock = 80,--船坞
    ObjItemShipTicket = 81,--船票
    ObjItemGroundTip = 82,--地面提示物
    ObjItemNewBalloon = 83,--触发购买物品(原来触发的是热气球)
    ObjItemLuckOreChoose = 84,    --幸运神矿选矿道具
	ObjItemRelic = 85,--遗迹图腾链
    CardPack = 87,  -- 卡包
    UniversalCard = 88,  -- 万能卡
	ObjItemAutoUseRescoure = 89,--直接使用资源类
	ObjItemCatIsland = 95,--猫岛
	ObjItemCatFood = 90,--普通猫粮
	ObjItemCatFoodHight = 91,--高级猫粮
	ObjItemCatCombine = 93,--合成链
	ObjItemLimitBox = 96,--限时点击纸箱
	ObjItemWaterPipe = 97,--污染水管
	ObjitemClearPipe = 98,--修复水管
	ObjitemClearGrid = 99,--修复污染格子
	ObjitemLianPipe = 106,--清理合成道具
	ObjItemHeadBorder = 107,--头像框
	ObjItemChatBorder =108,--气泡框
	ObjItemEmoji = 109, --表情包表情解锁道具
	
	-----Bag-----
	BagHeroMaterial = 201,--角色碎片
	BagHeroCommonMat = 202,--通用角色碎片
	BagHeroClothes = 301,--角色装备
	BagTickets = 302,--抽奖券
	BagItemBox = 303,--背包宝箱
    BagSkillMedal = 304,--技能勋章
    BagDungeonTick = 309,--关卡挑战券
	BagItemBox2 = 314,--经验宝箱
}

TaskProgress = {
    Finish = -1,
    Close = -2
}

TaskFinishType = {
    GetItem = 1, --获得物品
    CollectItem = 2, --采集物品
    UnlockCloud = 3, --解锁云层
    UseBlueWorker = 4, --使用蓝色工人
    UseItem = 5, --使用道具
    GetOrderMaterial = 6, --获得订单材料
    CompleteOrder = 7, --完成订单操作
    FMergeT = 8, --完成5合2
    OpenBox = 9, --打开箱子
    UseTower = 10, --使用城堡
    CollectBigTree = 11, --砍伐矿藏
    HeroCast = 12, --英雄施法
    PortLevel = 13, --升级船坞
    BindingAccount = 14, --账号绑定
    TowerUpStart = 15,--城堡升星
    UnlockAssignCloud = 16,--解锁制定云区
    EatItem = 17,
    OpenHeadSet = 18,--打开头像设置界面
    OpenNameSet = 19,--打开名字设置界面
    GetOrnamentBuilding = 20,--获得指定装饰物
    UseBoom = 21,--使用炸弹
    UseOpenGroupItem = 22,--使用开云道具
	NpcOrder = 23,---NPC订单
	GoToFaceBook = 24,--前往facebook
	TinyGame = 25,--小游戏
}

RandomTaskType = {
    UseBlueWorker = 1, --使用蓝色工人
    CollectBigTree = 2, --砍伐矿藏
    GetOrderMaterial = 3, --收集原材料
    GetGem = 4, --手机万能宝石
    CompleteOrder = 5, --完成订单操作
    CollectItem = 6, --清除障碍物
    GetHouse = 7, --创建房屋
    OpenBox = 8, --打开箱子
    HeroCast = 9, --英雄施法
}

TaskOpenType = {
    All = 0,
    UnlockCloud = 1, --解锁某块云
    GetItem = 2, --获得道具
    FinishTask = 3, --完成任务
    Level = 4, --到达某个等级
    PortLevel = 5, --船坞等级
    ZooLevel = 6,--排行榜等级
	TinyGame = 7,--小游戏
}

ClearType = {
    NotTime = 1,
    Time = 2,
    Build = 3,
    RedBuild = 4,
    Collect = 5,
    Exchange = 6,
    Candy = 7,
    Harvest = 8,
    PowerBuild = 9,
    PowerRedBuild = 10,
	BlueBuild = 11,
	Spatula = 12,
    UseItem = 13,
    HarvestGrade = 14,--TD —— Add Chirstmasitem in CD, Click Show UI
    AnimalZooUI = 15,--TD —— Animal Click By State To Show UI
    DesertCollect = 16,
    DesertSearch = 17,
    DesertGet = 18,
    DesertBoom = 19,
	Castle = 20,
	Dress = 21,
	FbNotTime = 22,
	Question=23,
	Cube=24,
	HalloweenOre=25,
    HalloweenOreItem=26,
    NormalShowItem = 27,
	SkiingMatchItem = 28,
	MultipleDropItem = 29,
}

JobType = {
    Collect = 1,
    Build = 2,
}

ItemFromWhere = {
    MapInit = -1,
    Default = 0,
    Combine = 1,
    DropBox = 2,
    CloudBig = 3,
    CloudMagic = 4,
    TreeLast = 5,
    HeroOrder = 6,
    FireFly = 7,
    Balloon = 8,
    FieldCollect = 9,
    Build = 10,
    HeroWaste = 11,
    CastleBuild = 12,
    BigTreeBuild = 13,
    BigTree = 14,
    Gift2 = 15,
    Guide = 16,
    CloudPayment = 17,
    UnlockByGridBlack = 18,
    UnlockByCloudMagic = 19,
    Aircraft = 20,
    Exchange = 21,
    DropOnce = 22,
	Spatula = 23,
    Producer = 24,
    CutDown = 25,
    AutoBuild = 26,
    AnimalBag = 27,
    CloudUnlockGift = 28,
    Somewhere = 29,
    UIDrag = 30,
    WaterBall = 31,
    DoubleOre = 32,
    TriggerGift = 33,
    ConditionBox = 34,
    ToyOre = 35,
    ShipUnload = 36,
    NewBalloon = 37,
    TriggerGiftAddReward = 38,
    LuckOreChoose = 39,
}

ActivityTotal = {
    Season = 1, --季节活动
    Normal = 2, --通行证
    Extra = 3, --额外奖励
    LimitIns = 4, --限时副本
    Rank = 5,--排行榜
    LimitRank = 6,--副本排行榜
	BuildBuff = 7,--建造加速
	GoldBuff = 8,--金币加速
	saving_bank = 9,--存钱罐
	Endless = 10,--无尽奖励
	FlashBuy = 11,--闪购
	Toy = 12,--玩具活动
	EasterEgg = 13,--砸蛋
	EnergyBuff = 14,--体力祝福活动
	DiamondBuff = 15,--钻石buff活动
	DiyGift = 16,--自选礼包
	FlowerCompetition = 17,--鲜花比拼活动
	BowlingBattle = 18,--保龄球比拼
	DoubleOre = 19,--双倍矿
	HalloweenOre = 20,--万圣节限时矿
    MonthlySeason = 21,--月度赛季
    SkiingMatch = 22,--滑雪比赛
    End2less = 23,--无尽奖励2
    RollDice = 24,--掷骰子活动
	ContinuousRecharge = 25, --连续充值活动
	LuckOre = 26, --幸运神矿
	Relic = 27,--遗迹考古
    OneToOne = 28,  -- 1v1对决活动
    AthleticTalent = 29,  -- 竞技达人活动
	KeepCat = 30,--猫猫活动
    NewSkiing = 31,--新滑雪竞赛活动
    CollectCard = 32,  -- 卡牌收集活动
    --DailyTarget = 33,  -- 每日目标活动类型
}

ActivitySubtype = {
    Build = 1, --建造
    MergeItem = 2, --完成合成
    OpenBox = 3, --宝箱
    Season1 = 4, --季节活动1
    Season2 = 5, --季节活动2
    Season3 = 6, --季节活动3
    Season4 = 7, --季节活动4
    Trolls = 8, --魔法经理
    SpeedBumper = 9, --加速丰收
    EnergyMax = 10, --精力充沛
    PowerBuild = 11, --强力建筑
    GoldTime = 12, --黄金计时器
    NormalLimit = 13,--普通限时
    ChristmasLimit = 14,--圣诞限时
    MergeRank = 15,--合成竞赛
    BuildRank = 16,--建造竞赛
    CollectRank = 17,--采集竞赛
	Collect = 18,--采集活动
	SavingBank = 19,--存钱罐
	DiamondBuff = 25,--钻石buff活动
	BowlingBattle = 27,--保龄球比拼
	DoubleOre = 28,--双倍矿
    HalloweenOre = 29,--万圣节限时矿
    MonthlySeason = 30,--月度赛季
    SkiingMatch = 31,--滑雪比赛
    RollDice = 32,--掷骰子活动
	ContinuousRecharge = 33, --连续充值活动
    LuckOre = 34, --幸运神矿
    OrderRank = 35, --订单排行榜
    OneToOne = 37,  -- 1v1对决活动
    AthleticTalent = 38,  -- 竞技达人活动
    DailyTarget = 42,  -- 每日目标活动类型
}
--在营地1不显示的活动（使用ActivityTotal配置）
--注意是不显示的活动
Main1Activity_NoActive = {
    DailyTarget = 33,
}
--在营地2不显示的活动（使用ActivityTotal配置）
--注意是不显示的活动
Main2Activity_NoActive = {
	EnergyBuff = 14,
	DoubleOre = 19,
    DailyTarget = 33,
}
--在动物园显示的活动（使用ActivityTotal配置）
ZooActivity = {
    MonthlySeason = 21,
	ContinuousRecharge = 25,
    OneToOne = 28,
    AthleticTalent = 29
}
--在副本显示的活动（使用ActivityTotal配置）
InsActivity = {
    HalloweenOre = 20,
    MonthlySeason = 21,
    RollDice = 24,
	ContinuousRecharge = 25,
	Relic = 27,
    OneToOne = 28,
    AthleticTalent = 29,
	KeepCat = 30,--猫猫活动
}
--每日任务的状态
DAY_TASK_STATE = {
    FINISHED_NOT_REWARD = 1, --已完成未领取奖励
    NOT_FINISH = 2, --未完成
    FINISHED_REWARDED = 3, --已完成已经领取奖励
}

---每日任务的类型
DAY_TEASK_TYPE = {
    ALL_DAYTASK = 1, --全部领取任务
    THREE_FOR_ONE = 2, --任意3合1
    FIVE_FOE_TWO = 3, --任意5合2
    MARKET_BUY = 4, --市场任意购买
    CUT_TREE = 5, --清除障碍（砍树）
    COMPTED_ORDER_NUM = 6, --完成订单次数任务
    USE_SPRITE_NUM = 7, --使用工人次数任务
    USE_GOLD_NUM = 8, --任意渠道消耗金币数量任务
    USE_DIMAND_NUM = 9, --任意渠道消耗钻石数量任务
    OPEN_CASE = 10, --打开任意等级箱子
    GET_GOLD_NUM = 11, --获得金币数量任务
    GET_RAW_MATERIAL = 12, --收集任意原材料数量任务
	
	--新加任务类型
	CASTLE_COLLECT = 13,--1)	采集城堡x次
	CLOATING_COLLECT = 14 , --2)	收集漂浮物x个
	REWARD_ALL_DAILY_TASK = 15,--3)	领取每日任务的全部奖励x次
	MINING_FIELD = 16, --4)	开采矿藏x次
	MARKET_FREE = 17, --5)	商店获取免费宝箱x次
	COMPOSE_KEY = 18 , --6)	合成某个元素x次
	CONSUME_MAIN_STRONG = 19 , --7)	消耗主岛体力x
	OPEN_SEED = 20 , --8)	打开种子袋x个
	USE_UNIVASL = 21 , --使用万能宝石X次
	USE_REDWORK_MINUTE = 22 , --使用红工人减少了X分钟
	
	ANIMAL_COMBINE = 23,--动物园合成X次
	LUCKY_TIMES = 24 ,--幸运抽奖X次
	ANIMAL_EAT_TIMES = 25, --投喂动物X次
	GET_FEED_CHEST = 26, --获得投喂宝箱X个
	
	GET_ITEM_BOX = 27,--获得宝箱
	COLLECT_MAGIC_TASK = 28,--收集迷你风车数量任务
	ONLINE_DYA = 29,--登陆天数
	CLOUD_UNLOCKED_TASK = 30,--解锁迷雾数量
	FINISH_MAIN_TASK = 31, --完成主线任务数量任务
	UNLOCKED_CLOUDID = 32 , --解锁某个云区
	BUILD_FINISH_WORKER = 33, --完成建筑
	OPEN_HERO_TASK = 34 , --解锁英雄数量
	FIVE_ID_FOR_TWO = 35,--五个ID合2
	USE_RED_WORKR_DONE = 36,--消耗红工人X个
	TO_ACTIVITY_MAP = 37,--参加副本
	CUT_TREE_COMPLETE = 38, --挖光资源
	OPEN_SPECIAL_BOX = 39, --打开特定宝箱
	ROOKIE_LEVEL_TASK = 40,--新手等级完成奖励
	UNLOCKED_CLOUDID2 = 41,--解锁某个云区2
	CUT_TREE_TYPE2 = 42, --砍完USE_TYPE2的矿
	UNLOCKED_HOTLE = 43,--解锁酒店
	COMPOSE_MOVE_TREE = 44,--合成可移动资源
	COMPOSE_CROPS = 45,--合成农作物
	
	-----季度任务
	GET_SEASON_ITEM = 48, ---获得特定id的item
	OPEN_SEASON_BOX = 49,---打开特定id的箱子

	FINISH_LIMIT_ACTIVITY = 50,--通关限时副本
	CHANGE_NAME = 51,--改名字
	CHANGE_HEAD = 52,--改头像

	CONSUME_LIMIT_STRONG = 53, --消耗限时副本体力x
	PLAYER_LEVEL = 54, --玩家等级
	ITEM_NEW = 55,--获得物品
    GET_DIMAND_NUM = 56,  -- 获得钻石
    MERGE_BUILDING_CHAIN = 57,  -- 合成建筑链
    USE_MAGIC_CUBE = 58,  -- 消耗万能魔方
}

TASK_SPLIT = {
	DAILY = 1, --每日任务
	WEEK = 2 , -- 每周任务
	ROOKIE = 3, --新手任务
    MAIN = 4, --主线任务
}

TASK_VIEW_TYPE = {
	ONLY_DAILY = 1, --只开每日/周 任务
	DAILY_AND_ROOKIE = 2, --新手任务和每日任务同时开启
	ONLY_ROOKIE = 3,--只开新手任务
    ONLY_MAIN = 4, --只开主线任务
}


TASK_RANDOM_NUM = {
	DAILY = 7 , 
	WEEK = 15
}

--刮刮卡领取的时间段
ENERGY_INTERVAL_TIME = 3600

--礼包的生效范围
GIFT_SCOPE = {
    MAIN_CAMP = 1, --主营地生效
    NORMAL_LEVEL = 2, --正常关卡
    RESIDENT_LEVEL = 3, --常驻关卡
    LIMIT_LEVEL = 4, --限时关卡
}

--礼包类型
GIFT_TYPE = {
    POWER = 1, --体力礼包的类型
}

--体力商店中的礼包类型
POWER_BAG_TYPE = {
    CARD = 1, --刮刮卡
    LIFE = 2, --生命值礼包
    HIGH = 3, --高级礼包
    NORMAL = 4, --普通礼包
    ADON = 5   --广告礼包
}

--打开界面传递的类型
OPEN_ENERGY_TYPE =
{
	NONE = 1 ,
	PUSH_VIEW = 2,
	NORMAL = 3,
}

TRIGGER_ENERGY_MINI = 10

--开启条件
DATA_OPEN_TYPE = {
    LEVEL = 1, --类型1：玩家等级限制
    ITEM_BUY = 2, --特定礼包id|购买次数
    TRIGGER = 3, --类型3：触发体力不够的次数（这个数值的增加，是在第一次购买item_type为3之后
}

--刮刮卡的状态
CARD_STATE = {
    NONE = 0, --无任何状态
    FREE = 1, --免费
    CANREWARD = 2, --可以领取
    REWARDED = 3, --不可领取的状态
	EXPIRE = 4,--未领取过期
	WAIT = 5,
}

--首冲领取状态
FIRST_GIFT = {
	UNLOCK = 0,--未购买
	CANREWARD = 1,--可领取
	NO_ENOUGH_TIME = 2,--已购买未到时间
	REWARDED = 3, --不可领取
	}

--南瓜图鉴
HALLOWEEN_ITEM = {
	NO_REWARD = 0,
	IS_REWARDED = 1,
	}

--通用图鉴
COMMON_COLLECTION_ITEM = {
    NO_REWARD = 0,
    IS_REWARDED = 1,
}

--物品状态
ITEM_STATE = {
	UNLOCK = 0,
	CAN_REWARD = 1,
	REWARED = 2,	
}

--月季
MS_VIEW_TYPE = {
	TASK = 1,
	REWARD = 2,
	SHOW = 3,
	}

--季节活动锁状态
SEASON_AREA_CLOUD = {
	LOCK = 0,--未解锁
	CAN_UNLOCK = 1,--可解锁
	UNLOCK = 2, --已解锁
}

--月卡/周卡
VIP_CARD = {
	LOCK = 0, 
	WEEK = 1,
	MONTH = 2,
    YEAR = 3,
	}

--特权月卡
PRIVILEGE_CARD = {
	LOCK = 0,
	UNLOCK =1,
	NO_REWARD =2,
	HAVE_REWARD=3,
}

--消耗公式对应的类型
COST_TYPE = {

    BASE = 1, --1-基本类型的消耗
    FOR_ID = 2, --2-按照特定ID的档位信息消耗，如果特定ID的item_type不是3的话报错改表(总数-（货币总数/档位换算倍率）取余）)
    ACHIECVED = 3 --3.消耗档位，购买总次数-货币值

}

--奖励的公式类型
REWARD_TYPE = {

    RE_BASE = 1, --类型1-固定奖励
    RE_FOR_ID = 2, --类型2-按照特定id的档位计算（现在只有这一种计算方式）（货币总数/档位换算倍率）取整
    RE_ACHIEVED = 3 --类型3：按照档位奖励

}

E_DataType = {
    BuildData = 1,
    heroData = 2,
    prodData = 3,
    materialData = 4,
    currecyData = 5,
    ActiveData = 6

}

MARKETBOX_DataType = {
    BuildData = 1,
    heroData = 2,
    prodData = 3,
    materialData = 4
}

CHATTYPE = {
    NOCHATONTENT = 1,
    HAVECHATCONTENT = 2
}
SpecialId = {
    InitFreeBoxTaskId = "1010",
    BindingAccountTaskId = "1036",
	SeasonsTaskId = "4501",
	GoToFaceBook = "2112",
    InitFreeBoxGuideId = 60,
	TinyGame = "1201",
	TinyGame2 = "1202",
	Show5To2Tip = "1005",
	MoveHero = "1011",
    
    ADTriggerGiftID = "2102",   -- 每日免广告礼包ID
    DailyTargetActivityID = 3342001,   -- 每日目标活动ID （假的活动ID）
    
    MainSkinSea = 15301,    -- 主营地海岸线皮肤ID
}
SPINE_CALLBACK_TYPE = {
    Start = 1,
    End = 2,
    Interrupt = 3,
    Complete = 4,
}

BEAST_UI_STATUS =
{
    BEAST_DEFAULT       = 1, -- 神兽库默认状态
    BEAST_SELECT_ONE    = 2, -- 选择单一神兽
    BEAST_DRAG          = 3, -- 拖拽神兽
    BEAST_FINISHED      = 4, -- 拖拽成功
    BEAST_FULL          = 5  -- 拖拽失败 背包已满
}

BEAST_SORT_TYPE =
{
    QUALITY     = 1, -- 按稀有度
    NUMBER      = 2, -- 按数量
    ORDER       = 3, -- 按顺序
}

--市场常驻和随机库数据区分
RANDOM_TYPE = 
{
	NORMAL = 1,	
}

--市场随机个数
MARKETSEEDCOUNT = 2

ZooLibState =
{
    Show = 1,
    Put = 2,
    Finish = 3,
    Full = 4,
    FullClose = 5,
    FullAutoClose = 6,
    FullCloseOnly = 7,
    BuyGridAutoClose = 8,
    NotSave = 9,
}

--todo Dialog 类型
DIALOG_TYPE =
{
    MONEY_BUY_ONE   = 1, -- param( titleDes, contentDes, okFunc, price )
    MONEY_BUY_TWO   = 2, -- param( titleDes, contentDes, okFunc, cancelFunc, price )
    COMMON_TIPS     = 3, -- param( titleDes, contentDes, okFunc, cancelFunc )
    COMMON_CONFIRM  = 4, -- param( titleDes, contentDes, okFunc )
    COMMON_CANCEL   = 5, -- param( titleDes, contentDes, cancelFunc )
}

--todo 市场购买类型
MARKET_BUY_TYPE =
{
    GOLD    = 1,
    DIAMOND = 2,
    FREE    = 3,
}

MainFaceDefine={
    Energy = "m_goEnergyPoint",
    Work = "m_goWorkerPoint",
    SetList = "m_goSetlistsPoint",
    Mail = "m_goEmailPoint",
    DailyTask = "m_goDailyTaskPoint",
    Order = "m_goOrderPoint",
    Collect = "m_goCollectPoint",
    Market = "m_goMarketPoint",
    Setting  = "m_goSettingPoint",
    Work1 = "m_goWork1",
    Work2 = "m_goWork2",
    EnergyTask = "m_goEnergyTaskPoint",
    ChristmasTask = "m_goChristamasTaskPoint",
	AirPoint = "m_goAirPoint",
	FriendRed = "m_goFriendRed",
	TinyGamePoint = "m_goTinyGamePoint",
}
AddBtnDefine =
{
    diamond = "m_imgDiamBuy",
    coin = "m_imgCoinBuy",
    energy = "m_imgEnergyBuy",
    worker = "m_goExtraWork",
    limitenergy = "m_imgEnergyBuy",
}

MainTipWall =
{
    minX = -367,
    maxX = 450,
    minY = -153,
    maxY = 150,
}
FlyResourceType =
{ 
	FlyNone = "FlyNone",
	FlyOrder=  "FlyOrder"
}

AlphaType =
{
	TreeUnBuild = 0.8, --item 13 36 类型
	CastleUnBuild = 0.9,
	ObjLuck2 = 0.6,  --CloudUpItem
	ObjLuck3 = 1.0,
	ObjLuck0Gray = 1, --DeadFlootItem
}

UISpecialDefine =
{
	UIAutoCloseX = "AutoCloseX"
 }

-- 好友聊天视图类型
FRIEND_VIEW_TYPE =
{
    Friend = 1,               -- 好友
    Accept = 2,               -- 申请列表
    Request = 3,              -- 添加好友
    AllRank = 4,
    UnionRank = 5,
    JoinLeauge = 6,
    CreatLeauge = 7,
    MyUnion = 8,
    Chat = 9,
    FuBenRank = 10,
    ZooRank = 11,
    SystemMessages = 12,      -- 系统消息
    WorldChannel = 13,        -- 世界频道
    OptionalLanguage = 14,    -- 自选语言
    LeagueChat = 15,          -- 联盟聊天
    LeagueAnnouncement = 16,  -- 联盟公告
    Recently = 17,            -- 最近联系
    Blacklist = 18,           -- 屏蔽名单
}

-- 好友列表请求类型
FRIEND_REQUEST_TYPE = {
    Friend = 1,               -- 好友列表
    Blacklist = 2,            -- 屏蔽名单
    AddFriend = 3,            -- 添加好友
    OtherRequest = 4,         -- 他人申请添加好友
    Recently = 5              -- 最近联系
}

-- 聊天配置类型
CHAT_SETTING = {
    ChatOpen = 1,               -- 聊天功能开放等级
    WorldChannelOpen = 2,       -- 世界频道开放等级
    AreaChannelOpen = 3,        -- 区域频道开放等级
    ChannelCountMax = 4,        -- 频道-分频数量上限
    ChannelPeopleMax = 5,       -- 频道-分频人数上限
    MessageMax = 6,             -- 消息记录上限
    SendMessageInterval = 7,    -- 消息发送间隔时间（秒）
}

FRIEND_RANK_TAB_TYPE = {
    ALLRANKE = 1,   -- 原排行榜
    FUBEN = 2,      -- 副本通关次数
    ZOO = 3,        -- 动物园
    LEAGUE = 4,     -- 联盟
}

BOWLING_BATTLE_TASK_TYPE = {
	A = 1,--主营地中消耗红工人
	B = 2,--制作订单
	C = 3,--使用万能宝石
	D = 4,--消耗体力
    E = 5,--打开宝箱
    F = 6,--消耗金币
	G = 7,--获得钻石
	H = 8,--建筑升级
}

--联盟活动的奖杯
LEAGUE_ACTIVE_CUP = {
    "Sprite/ui_friend/lianmeng_huodong_blq_xunzhang5.png",
    "Sprite/ui_friend/lianmeng_huodong_blq_xunzhang4.png",
    "Sprite/ui_friend/lianmeng_huodong_blq_xunzhang3.png",
    "Sprite/ui_friend/lianmeng_huodong_blq_xunzhang2.png",
    "Sprite/ui_friend/lianmeng_huodong_blq_xunzhang1.png",
}

INTELLIGENT_WORKER_ACTION = {
    PICK_UP = 1,--拾取
    MERGE = 2,--合成
    BUILD = 3,--建造
    COLLECT = 4,--收集
}

INTELLIGENT_WORKER_ACTION_STATE = {
    WORKING = 1,--工作中
    FINISHED = 2--结束
}

WROKER_SKIN_ANIM_TYPE = {
    BAWAN = "bawan",
    IDLE = "idle",
    JUMP = "jump",
    NINGLUOSI = "ningluosi",
}

MapComponentId = {
    SmallStorageBoxComponent = 0,
    MapNavigateComponent = 1,
    ConditionBoxHelperComponent = 2,
    HighlightItemComponent = 3,
    ColorfulItemComponent = 4,
    MapP2PNavigateComponent = 5,
    ShipDockComponent = 6
}

SHIP_STATE = {
    NONE = 0,--闲置
    MOVE = 1,--移动中
    STOP = 2,--停靠中 有物品等待获取
}

CommonCollectionType = {
    Toy = 1,
}

CD_BOX_STATUS =
{
	ACTIVE = 1,
	FINISH = 2
}

PopupPromptUiType = {
    TASK_FINISH = 1,--任务完成
    TASK_ACCEPT = 2,--接受任务
    MONTHLY_SEASON_ITEM = 3,
    DAILY_TASK_FINISH = 4,
	SEVEN_DAY_TASK_FINISH = 5,--7天任务
	SLG_BATTLE_PASS = 6,--slg战令
}

ACTIVITY_RANK_TABINDEX = {
    One_to_one          = 1,        -- 1v1
    AthleticTalent      = 2,        -- 竞技达人
    BowlingBattle       = 3,        -- 保龄球
    Rank                = 4,        -- 竞赛排行榜
    ActiveFollow        = 5,        -- 三方活动
    LevelEnter          = 6,        -- slg关卡
    TowerClimbing       = 7,        -- slg爬塔
    Train               = 8,        -- slg联盟火车
    WorldBoss           = 10,       -- 世界Boss
    TopFight            = 11,       -- 争霸赛
}

DailyTargetRewardState = {
    NONE = 0,
    CAN_REWARD = 1,
    REWARDED = 2,
}

PAY_BOX_ID = {
	85131,
	85132,
	85133,
	85134,
	85135,
}

DAILY_GIFT = {
	DAY = 1, --每日限购
	WEEK = 2 , -- 每周限购
	MONTH = 3, --每月限购
}

MultipleNum = {
    NONE = 0,
	ONE = 1,
	THREE = 3,
	NINE = 9,
	TWENTY_SEVEN = 27,
}

MultipleFilterItemID = {
    33211,  -- 9增加9倍采集BUFF的剩余时间
    33221,  --2增加27倍采集BUFF的剩余时间
    52001,  --3，9，27倍下特殊掉落道具不参与多倍lv1
    52002,  --3，9，27倍下特殊掉落道具不参与多倍lv2
    52003,  --3，9，27倍下特殊掉落道具不参与多倍lv3
    10000071,  --slg 不参与多倍掉落
	--29301,
}
MultipleBUFFID = {
    33211,
    33221,
    33201,
}

MAIN_UI_ICON_ENUM = {
    Collect = "Collect",
}

ANNOUNCE_ID_ENUM = {
    DROP_SPECIAL_ITEM       = 1001,          -- 惊喜宝箱掉落大奖
    COMBINE_NEW_CASTLE      = 1002,          -- 合成建筑
    COMBINE_NEW_HERO        = 1003,          -- 合成角色
    CASTLE_UP               = 1004,          -- 建筑升星
}

SCIENCE_TYPE = {
    UNION_PEOPLE = 1,      -- 团队扩张
    UNION_CONTRIBUTE = 2,  -- 额外贡献
    UNION_HELP = 3,        -- 友好协助
    UNION_GIFT = 4,        -- 众乐乐
    SHOP_UNLOCK = 5,       -- 商品解锁
    SHOP_BUY = 6,          -- 商品数量
    BUILD_SPEED = 7,       -- 建造速度
    COLLECT_SPEED = 8,     -- 采集速度
    ORDER_SPEED = 9,       -- 订单速度
    ENERGY = 10,           -- 体力上限
}

-- 聊天类型
CHAT_TYPE_ENUM = {
    SYSTEM      =   1,      -- 系统消息
    WORLD       =   2,      -- 世界消息
    AREA        =   3,      -- 区域消息
    UNION       =   4,      -- 联盟消息
    PRIVATE     =   5,      -- 私聊
}

-- 发送消息类型
CHAT_MSG_TYPE = {
    NORMAL                  = 1,    -- 普通消息
    EMOJI                   = 2,    -- 大表情 
    UNION_INVITE            = 3,    -- 联盟邀请
    UNION_INVITE_BOSS       = 4,    -- 联盟BOSS召集
    UNION_BOSS_SHARE        = 5,    -- 联盟BOSS伤害分享
    ARENA_REPORT_SHARE      = 11,   -- 竞技场战报分享
    TRADE_TRAIN_INFO        = 21,   -- 掠夺火车的基本信息
    TRADE_TRAIN_ROB         = 22,   -- 火车被掠夺推送
    WORLD_BOSS_SHARE        = 31,   -- 世界BOSS分享
}

FACEBOOK_FRIEND_STATE = {
	FACEBOOK_AND_GAME_FRIEND = 2,	--用户既是 Facebook 好友，也是游戏好友
	ONEY_FACEBOOK_FRIEND = 3		--用户是 Facebook 好友，但不是游戏好友
}

DRAWCARD_TYPE = {
	HERO = 1,
	EQUIP = 2,
	USE_TICKET=3,
}

-- 英雄兵种
HERO_KIND = {
    ALL = 0,       -- 所有
    TANK = 130,    -- 坦克
    PLAEN = 131,   -- 飞机
    MISSILE = 132, -- 导弹
}

HERO_KIND_NAME = {
	ALL = 70000000,     -- 所有
    TANK = 58006060,    -- 坦克
    PLAEN = 58006061,   -- 飞机
    MISSILE = 58006062, -- 导弹
}

TOWER_TYPE = {
    All     = 0,        -- 全兵种
    Tank    = 1,        -- 坦克
    Missile = 2,        -- 导弹车
    Plane   = 3,        -- 飞机
}

-- 英雄定位
HERO_CAREER = {
    SUP = 11,    -- 增益
    DEF = 12,    -- 承伤
    ADC = 13,    -- 伤害
}

-- 装备养成界面页签类型
EQUIPMENT_DEVELOP_VIEW_TYPE = {
    STRENGTHEN = 1,  -- 强化
    RISINGSTAR = 2,  -- 升星
    RESOLVE    = 3,  -- 分解
    FALLBACK   = 4,  -- 回退
}

-- 装备部位
EQUIPMENT_PART = {
    WEAPON = 1,  -- 武器
    ARMOUR = 2,  -- 装甲
    CHIP   = 3,  -- 芯片
    RADAR  = 4,  -- 雷达
}

-- 道具品质
ITEM_QUALITY = {
    GREEN = 1,   -- 绿色
    BLUE = 2,    -- 蓝色
    PURPLE = 3,  -- 紫色
    GOLD = 4,    -- 金色
}

BAGITEM_QUALITY_PATH = {
	[1] = "Sprite/ui_slg_beibao/beibao_daojukuang_green.png",
	[2] = "Sprite/ui_slg_beibao/beibao_daojukuang_blue.png",
	[3] = "Sprite/ui_slg_beibao/beibao_daojukuang_purple.png",
	[4] = "Sprite/ui_slg_beibao/beibao_daojukuang_gold.png",
    [5] = "Sprite/ui_slg_beibao/beibao_daojukuang_red.png",
}
ATTRIBUTE_TYPE_PATH = {
	[1] = "Sprite/ui_slg_beibao/zb_shuxing_gongji.png",
	[2] = "Sprite/ui_slg_beibao/zb_shuxing_shengming.png",
	[3] = "Sprite/ui_slg_beibao/zb_shuxing_fangyu.png",	
}

--战斗

--关卡类型
DUNGEON_NODE_TYPE = {
    Normal = 1;--普通关卡
    Boss = 2;--Boss关卡
	BigBoss = 3;--Boss关卡
}


BATTLE_RESULT = {
	WIN = 1,
	LOSE = 2
}

BATTLE_SIDE = {
	LEFT = 1,--左侧
	RIGHT = 2--右侧
}

BATTLE_TARGET_TYPE = {
	UNIT = 1,--以个体为目标
	TEAM = 2,--以小组为目标
	ENEMY_FRONT = 3,--敌方前排
	ENEMY_BACK = 4,--敌方后排
	ENEMY_CENTER = 5,--敌方中场
	FRIENDLY_FRONT =6,--友方前方
	FRIENDLY_BACK = 7,--友方后排
	FRIENDLY_CENTER = 8,--友方中场
}

BATTLE_BULLET_TYPE = {
	NONE = 0,--空子弹
	NORMAL = 1,--普通子弹
	PARABOLIC = 2,--抛物线子弹
	LASER = 3,--激光子弹
	BEZIER = 4,--贝塞尔曲线
	PARTICLE = 5,--粒子
	LASER_LINK = 6,--激光链
	LIGHTNING_BOLT = 7,--电弧
	BOUNCING = 8,--跳跳弹
}

BATTLE_SOLDIER_ANIMATION = {
	NONE = 0,--
	IDLE = 1,--待机
	RUN = 2,--进场
	SKILL1 = 3,--普攻
	SKILL2 = 4,--大招
	DIE = 5,--死亡
}

BATTLE_ACTION_TYPE = {
	None = 0,-- 无
	Skill = 1,-- 释放技能
	Attr = 2,-- 属性变化
	Buff = 3,-- buff 变化
	Special = 4,-- 特殊处理
	Result = 100,-- 战斗结束
}

BATTLE_CHANGE_TYPE = {
    AttrHP = 3,-- 生命值发生变化
    AttrSoldiers = 5,-- 带兵数量
    BuffAdd = 7,-- 添加 buff
    BuffRemove = 8,-- 移除 buff
    Die = 9,-- 死亡
    Resurgence = 10,-- 复活
    Mask = 11,-- 读取掩码
}

BATTLE_MASK_TYPE = {
	Dodge 	= 1,--闪避
	Crit 	= 2,--暴击
	Immune 	= 3,--免疫
	Block   = 4,--格挡
	Poison 	= 5,--中毒
	Corrosion = 6,--腐蚀
	Burn 	= 7,--燃烧
}

BATTLE_ATTR_TYPE = {
	None = 0,--
	HP = 1,
	MaxHP = 2,
	Atk = 3,
	Def = 4,
	Soldiers = 5,
}

BATTLE_HURT_NUM_TYPE = {
	Add = 0,
	Reduce = 1,
	AddCrit = 2,
	ReduceCrit = 3,
	Poison = 4,--中毒
	Corrosion = 5,--腐蚀
	Burn = 6,--燃烧
}

--battleFiled 的事件
BATTLE_FILED_EVENT_ID = {
	CAST_SKILL = "CAST_SKILL",--释放技能
	ATTR_CHANGE = "ATTR_CHANGE",--属性变化
	BATTLE_RESULT = "BATTLE_RESULT",--战斗结果
	TEAM_CHANGE = "TEAM_CHANGE",--队伍创建或者销毁
	BUFF_CHANGE = "BUFF_CHANGE",--BUFF
	TEAM_UNDER_ATK = "TEAM_UNDER_ATK",--队伍遭到攻击
	SOLDIER_UNDER_ATK = "SOLDIER_UNDER_ATK",--士兵遭到攻击
	TICK_PRE_SECOND = "TICK_PRE_SECOND",--每秒更新
	TICK_PRE_FRAME = "TICK_PRE_FRAME",--每帧更新
	TEAM_DIE = "TEAM_DIE",--队伍成员全部死亡
	TEAM_MOVE = "TEAM_MOVE",--队伍移动 UI 操作
}

--战斗场景类型
BATTLE_SCENE_TYPE = {
	NONE = 0,--无
	CHOOSE = 1,--选角色界面
	BATTLE_REPORT = 2,--战斗界面
	BATTLE_DEBUG = 3,--测试界面(角色自由选择)
	BATTLE_DEBUG_SKILL = 4,--技能测试界面
	BATTLE_OPEN_READY = 5,--战报回放准备
}

-- 开始战斗类型
BATTLE_TYPE = {
    LEVEL = 1,      --关卡
    TOWER = 2,      --爬塔
}

BATTLE_TEAM_TYPE = {
    DUNGEON                     = 1,        -- 副本队伍
    TOWER                       = 2,        -- 爬塔队伍-无限制
    TOWER_TANK                  = 3,        -- 爬塔队伍-全坦克
    TOWER_AIRPLANE              = 4,        -- 爬塔队伍-全飞机
    TOWER_MISSILE               = 5,        -- 爬塔队伍-全导弹
    TRADE_CAR_1                 = 11,       -- 押镖-货车防守队伍 1
    TRADE_CAR_2                 = 12,       -- 押镖-货车防守队伍 2
    TRADE_CAR_3                 = 13,       -- 押镖-货车防守队伍 3
    TRADE_CAR_4                 = 14,       -- 押镖-货车防守队伍 4
    TRADE_CAR_LOOT              = 15,       -- 押镖-货车掠夺队伍 5
    UNION_BOSS                  = 21,       -- 联盟BOSS
    ARENA_SELF                  = 31,       -- 竞技场-自己阵容
    ARENA_DEFENDER              = 32,       -- 竞技场-防守阵容
    TRADE_TRAIN_ROB_1           = 40,       -- 掠夺货车进攻队伍1
    TRADE_TRAIN_ROB_2           = 41,       -- 掠夺货车进攻队伍2
    TRADE_TRAIN_ROB_3           = 42,       -- 掠夺货车进攻队伍3
    TRADE_TRAIN_DEFEND_1_1      = 43,       -- 货车防御1_1
    TRADE_TRAIN_DEFEND_1_2      = 44,       -- 货车防御1_2
    TRADE_TRAIN_DEFEND_1_3      = 45,       -- 货车防御1_3
    TRADE_TRAIN_DEFEND_2_1      = 46,       -- 货车防御2_1
    TRADE_TRAIN_DEFEND_2_2      = 47,       -- 货车防御2_2
    TRADE_TRAIN_DEFEND_2_3      = 48,       -- 货车防御2_3
    TRADE_TRAIN_DEFEND_3_1      = 49,       -- 货车防御3_1
    TRADE_TRAIN_DEFEND_3_2      = 50,       -- 货车防御3_2
    TRADE_TRAIN_DEFEND_3_3      = 51,       -- 货车防御3_3
    
    TOPFIGHT_BATTELE_TEAM1      = 60,       -- 争霸赛战斗队伍1
    TOPFIGHT_BATTELE_TEAM2      = 61,       -- 争霸赛战斗队伍2
    TOPFIGHT_BATTELE_TEAM3      = 62,       -- 争霸赛战斗队伍3
    WORLD_BOSS                  = 101,      -- 世界boss
}

--玩家火车位置索引
TRAIN_CONST = {
    RoleTrainIndex1 = 1,
    RoleTrainIndex2 = 2,
    RoleTrainIndex3 = 3,
}

BATTLE_FIGHT_TYPE = {
    NONE            = 0,            --无
    Test            = 1,            --战斗测试
    Dungeon         = 1001,         --普通副本
    Tower           = 1002,         --爬塔副本
    LeagueBoss      = 2001,         --联盟boss
    TradeTruck      = 2002,         --货车掠夺
    Arena           = 3001,         --竞技场
}

BATTLE_TIME_EASE = {
	None = 0,

	InSine = 1,
	OutSine = 2,
	InOutSine = 3,

	InQuad = 4,
	OutQuad = 5,
	InOutQuad = 6,

	InCubic = 7,
	OutCubic = 8,
	InOutCubic = 9,

	InQuart = 10,
	OutQuart = 11,
	InOutQuart = 12,

	Bezier = 13,
}

TOWER_RANK_TYPE = {
    World       = 1,
    Area        = 2,
    League      = 3,
    Friend      = 4,
}

-- SLG 通用品质
SLG_QUALITY = {
    N = 1,
    R = 2,
    SR = 3,
    SSR = 4,
    UR = 5
}

-- 贸易货车记录类型
TRADE_WAGONS_RECORD = {
    Depart = 1,   -- 运输记录
    Plunder = 2,  -- 掠夺记录
}

-- 贸易货车详情
TRADE_WAGONS_DETAIL = {
    Me = 1,       -- 我的
    Other = 2,    -- 其他的
}

-- 贸易货车发车状态
TRADE_WAGONS_STATUS = {
    None = 0,     -- 未创建
    Ready = 1,    -- 已创建未发车
    Depart = 2,   -- 运输中
    Finish = 3,   -- 已完成
    Reward = 4,   -- 已领奖
}

-- 贸易货车奖励状态
TRADE_WAGONS_REWARD_STATUS = {
    Normal = 0,   -- 正常
    Back = 1,     -- 夺回
    Lost = 2,     -- 丢失
}

-- 贸易货车提示弹窗类型
TRADE_WAGONS_TIP_VIEW = {
    NormalTip = 1,      -- 普通提示
    ShareChannel = 2,   -- 分享到频道
    ConfirmTip = 3,     -- 确认提示
    ConfirmTip2 = 4,
}

-- 贸易货车 火车车厢类型
TRADE_TRAIN_CARRIAGE = {
    Head = 0,           -- 车头
    Carriage1 = 1,      -- 车厢 1
    Carriage2 = 2,      -- 车厢 2
    Carriage3 = 3,      -- 车厢 3
    Carriage4 = 4,      -- 车厢 4
}

-- 贸易货车 火车状态
TRADE_TRAIN_STATUS = {
    NotReady = 0,                 -- 召唤未准备(不能候车)
    Ready = 1,                    -- 召唤已准备(可以候车)
    Transporting = 2,             -- 运输中
    FinishedNotGiveReward = 3,    -- 已完成，未发奖励
    FinishedHadGiveReward = 4,    -- 已完成，已发奖励
}

-- 贸易货车 页签
TRADE_WAGONS_TAB = {
    Other = 1,  -- 他人货车
    My = 2,     -- 我的货车
}

-- 邮件
MAIL_TYPE = {
    SYSTEM = 1,-- 系统邮件
    LEAGUE = 2,-- 团队邮件
    REPORT = 3,-- 战报邮件
    SEARCH = 4,-- 探索邮件
}

-- 邮件内容分类
MAIL_CONTENT_TYPE = {
	TEXT = 0,--全文字
	TEST = 1,--测试，内含自己角色信息，cheat sg 2001 0
    TEXT_LANG = 2, -- 多语言
	LEAGUE_BOSS_KILL_REWARD = 101, --团队 Boss 击杀奖励
    LEAGUE_BOSS_RANK_REWARD = 102, --团队 Boss 排名奖励
    TRADE_TRAIN_DEPART = 201, --贸易货车，火车发车邮件
    TRADE_TRAIN_ARRIVAL = 202, --贸易货车，火车到达邮件
    TRADE_TRAIN_REWARD = 203, --贸易货车，火车奖励邮件
	ARENA_SETTLE_REWARD = 301,--竞技场赛季结算奖励
    WORLD_BOSS_RANK_REWARD = 401, -- 世界 boss 排名奖励
    WORLD_BOSS_JOIN_REWARD = 402, -- 世界 boss 参与奖励
}

-- 邮件状态
MAIL_STATE = {
	READ = 1,
	CLAIMED = 2
}

TOP_FIGHT_STATE = {
	Closed = 0; -- 活动关闭期间，可以查看上一届数据
	APPLY = 1; -- 报名期间，可以查看上一届数据

	PREPARE_READY = 2; -- 报名结束，预选赛准备期间，玩家可以调整战斗队伍
	PREPARE = 3; -- 预选赛期间，每隔 10 分钟一场，比赛前 5 分钟不可以再调整战斗队伍。直到决出 64 强为止

	FIGHT_64_TO_32_READY = 4; -- 32 强赛开始，参赛玩家可以调整战斗队伍，已报名玩家可以参与竞猜
	FIGHT_64_TO_32_WAIT = 5; -- 32 强赛等待，不可以再调整队伍，此为服务端计算战斗期间
	FIGHT_64_TO_32 = 6; -- 32 强赛展示

	FIGHT_32_TO_16_READY = 7; -- 16 强赛开始，参赛玩家可以调整战斗队伍，已报名玩家可以参与竞猜
	FIGHT_32_TO_16_WAIT = 8; -- 16 强赛等待，不可以再调整队伍，此为服务端计算战斗期间
	FIGHT_32_TO_16 = 9; -- 16 强赛展示

	FIGHT_16_TO_8_READY = 10; -- 8 强赛开始，参赛玩家可以调整战斗队伍，已报名玩家可以参与竞猜
	FIGHT_16_TO_8_WAIT = 11; -- 8 强赛等待，不可以再调整队伍，此为服务端计算战斗期间
	FIGHT_16_TO_8 = 12; -- 8 强赛展示

	FIGHT_8_TO_4_READY = 13; -- 4 强赛开始，参赛玩家可以调整战斗队伍，已报名玩家可以参与竞猜
	FIGHT_8_TO_4_WAIT = 14; -- 4 强赛等待，不可以再调整队伍，此为服务端计算战斗期间
	FIGHT_8_TO_4 = 15; -- 4 强赛展示

	FIGHT_4_TO_2_READY = 16; -- 半决赛开始，参赛玩家可以调整战斗队伍，已报名玩家可以参与竞猜
	FIGHT_4_TO_2_WAIT = 17; -- 半决赛等待，不可以再调整队伍，此为服务端计算战斗期间
	FIGHT_4_TO_2 = 18; -- 半决赛展示

	FIGHT_2_TO_1_READY = 19; -- 决赛开始，参赛玩家可以调整战斗队伍，已报名玩家可以参与竞猜
	FIGHT_2_TO_1_WAIT = 20; -- 决赛等待，不可以再调整队伍，此为服务端计算战斗期间
	FIGHT_2_TO_1 = 21; -- 决赛展示
}

FightStage = {
	Prepare = 1,
	Qualifier = 2,
	Match32 = 3,
	Match16 = 4,
	Match8 = 5,
	Match4 = 6,
	Match2 = 7,
	Finals = 8,
	End = 9,
}

---------------------------------------------------------------------
local upValue
local coff

--第一个参数时是否是金币，第二个参数是数量
function GetSpeedUpDiamond(time)
    upValue = upValue or GlobalConfig:GetNumber(1014)
    coff = coff or GlobalConfig:GetNumber(1015)
    local min = math.ceil(time / 60)
    local cost = math.floor(Mathf.Pow(min + upValue, coff))
    return LimitActivityController:GetGoldTime(cost)
end

function IsOnlyHomeMap(mapId)
	if not mapId then return false end
	mapId = v2n(mapId)
	return mapId == MAP_ID_MAIN
end

function IsHomeMap(mapId)
    return mapId == MAP_ID_MAIN or mapId == MAP_ID_SECOND
end

function IsHomeMaps(...)
    local args = {...}
    for _, v in ipairs(args) do
        if not IsHomeMap(v) then
            return false
        end
    end
    return true
end

function IsHomeZooMap(mapId)
	mapId = v2n(mapId)
	return mapId == MAP_ID_MAIN or mapId == MAP_ID_SECOND or mapId == MAP_ID_ZOO
end

function IsRuGoogle()
	if Game.IsRuGame and Game.Channel == "Google" then
		return true
	end
	return false
end
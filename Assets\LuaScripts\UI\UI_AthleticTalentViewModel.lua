local UI_AthleticTalentViewModel = {}

UI_AthleticTalentViewModel.config = {["name"] = "UI_AthleticTalentView", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = false, ["anim"] = 0,["background"] = 0,["onEscape"] = false}

function UI_AthleticTalentViewModel:Init(c)
    c.ui = {}    
    c.ui.m_goDaily = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily")
    c.ui.m_txtTopic = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/TopicBg/m_txtTopic",UEUI.Text)
    c.ui.m_goArrow = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/TopicBg/m_goArrow")
    c.ui.m_btnTaskTip = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/TopicBg/m_btnTaskTip",UEUI.Button)
    c.ui.m_txtDailyTime = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/TopicBg/RewardTime/RewardTimeBg/m_txtDailyTime",UEUI.Text)
    c.ui.m_imgClock = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/TopicBg/RewardTime/m_imgClock",UEUI.Image)
    c.ui.m_goGuide2 = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_goGuide2")
    c.ui.m_imgBanner = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_imgBanner",UEUI.Image)
    c.ui.m_imgBanner2 = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_imgBanner2",UEUI.Image)
    c.ui.m_btnRank = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_btnRank",UEUI.Button)
    c.ui.m_rtransTopic = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_rtransTopic",UE.RectTransform)
    c.ui.m_transDirectBuy = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_transDirectBuy",UE.Transform)
    c.ui.m_txtMyScore = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/Image (3)/m_txtMyScore",UEUI.Text)
    c.ui.m_goRankToday = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_goRankToday")
    c.ui.m_txtRankToday = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_goRankToday/m_txtRankToday",UEUI.Text)
    c.ui.m_goRankTotal = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_goRankTotal")
    c.ui.m_txtRankTotal = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/Topic/Bg/m_goRankTotal/m_txtRankTotal",UEUI.Text)
    c.ui.m_scrollviewReward = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/ScrollView/Reward/m_scrollviewReward",UEUI.ScrollRect)
    c.ui.m_goGuide4 = GetChild(c.uiGameObject,"bg/Bottom/m_goDaily/ScrollView/Reward/m_goGuide4")
    c.ui.m_goSettlement = GetChild(c.uiGameObject,"bg/Bottom/m_goSettlement")
    c.ui.m_goNoRank = GetChild(c.uiGameObject,"bg/Bottom/m_goNoRank")
    c.ui.m_btnCloseNoRank = GetChild(c.uiGameObject,"bg/Bottom/m_goNoRank/NoRankBg/m_btnCloseNoRank",UEUI.Button)
    c.ui.m_btnHelp = GetChild(c.uiGameObject,"bg/Top/TopBg/Title/Help/m_btnHelp",UEUI.Button)
    c.ui.m_imgHelp = GetChild(c.uiGameObject,"bg/Top/TopBg/Title/Help/m_btnHelp/m_imgHelp",UEUI.Image)
    c.ui.m_goParentTog = GetChild(c.uiGameObject,"bg/Top/m_goParentTog")
    c.ui.m_goTog = GetChild(c.uiGameObject,"bg/Top/m_goTog")
    c.ui.m_txtTime = GetChild(c.uiGameObject,"bg/Top/Time/ImgCountdownBg/m_txtTime",UEUI.Text)
    c.ui.m_imgClock = GetChild(c.uiGameObject,"bg/Top/Time/m_imgClock",UEUI.Image)
    c.ui.m_goRewardItem = GetChild(c.uiGameObject,"bg/m_goRewardItem")
    c.ui.m_txtName = GetChild(c.uiGameObject,"bg/m_goRewardItem/AnimObj/m_txtName",UEUI.Text)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_AthleticTalentViewModel
local UI_RankLimit = Class(BaseView)
local M = UI_RankLimit
local BubbleItem = require("UI.BubbleItem")
local RankBox = require("UI.RankBox")
local RankBoxFlyAnim = require("UI.RankBoxFlyAnim")

local RewardState = {
    Normal = 1,
    Unclaimed = 2,
    Received = 3,
}


function M:OnInit()
    
end

function M:OnCreate(activeId,rankMess,isPush)
    self.rankBox = nil
    self.lastY = nil
    self.active = LimitActivityController:GetActiveMessage(activeId)
    self.activeInfo = self.active.info
    self.activeForm = self.active.form
    self:SetItem(rankMess,isPush)
    self.rankConfig = self.activeForm.rankStage
    self.rewardList = {}
    self.m_childGo = nil
    self.initSelf = nil
    self.finishState = 0
    self.ui.m_txtTitle.text = LangMgr:GetLang(self.active.form.title)
	local imagePath = self.activeForm.activeMess.pass_icon2
	self:ChangeImage(self.ui.m_goList,imagePath)
	self:ChangeImage(self.ui.m_goUp,imagePath)
	self:ChangeImage(self.ui.m_goDown,imagePath)
	CreateCommonHead(GetChild(self.ui.m_goUp,"iconBg").transform,0.45)
	CreateCommonHead(GetChild(self.ui.m_goDown,"iconBg").transform,0.45)
	CreateCommonHead(GetChild(self.ui.m_goList,"iconBg").transform,0.45)
	
    self:InitNotHave()

    local spine = self.ui.m_spuiLeft
    --RoleSpineConfig:SetSpineByName(self.ui.m_spuiLeft,self.activeForm.activeImg1)

    self.bubble = BubbleItem.new("UI_RankLimit")
    self.bubble:Init(self.uiGameObject,{x= -352,y = 710},function() self:Close() end)
	
    self:InitReward()

    if rankMess.ranking  then
        SetActive(self.ui.m_goHave,true)
        SetActive(self.ui.m_goNotHave,false)
        self:InitUIList()
    else
        SetActive(self.ui.m_goHave,false)
        SetActive(self.ui.m_goNotHave,true)
    end
    self.deltaTime = 0

    local ranking = rankMess and v2n(rankMess.player.ranking) or 0
    if ranking > 0 then
        self:PlayFinishReward(true)
    else
        self:SetIsUpdateTick(true)
    end
    self:Timer(true)

end

function M:ChangeImage(obj,path)
	local child = GetChild(obj,"Image")
	local image = GET_UI(child, "Image", TP(UEUI.Image))
	SetImageSprite(image,path,true)
end

function M:InitNotHave()
    --local str1 = string.split(LangMgr:GetLang(self.activeForm.endDes1) ,"|")
   -- local pos = string.split(str1[2],",")
    self.ui.m_txtCr1.text = LangMgr:GetLang(self.activeForm.endDes1)
   -- SetUIPos(self.ui.m_goCr1,pos[1],pos[2])
   -- str1 = string.split(LangMgr:GetLang(self.activeForm.endDes2) ,"|")
   -- pos = string.split(str1[2],",")
    self.ui.m_txtCr2.text = LangMgr:GetLang(self.activeForm.endDes2)
   -- SetUIPos(self.ui.m_goCr2,pos[1],pos[2])
    self.ui.m_txtCr3.text = LangMgr:GetLang(self.activeForm.endDes3)

    local listpoint = self.rankConfig.listpoint
    SetUISize(self.ui.m_imgSlider2,(self.activeInfo.integral / listpoint) * 615,37)
    self.ui.m_txtProgress2.text = self.activeInfo.integral .. "/" .. listpoint
end

function M:Timer(init)
    local endTime = self.active:GetRemainingTime()
    endTime = math.max(endTime,0)
	local endTime2 = self.active:GetRemainingTimeReal()
	self.ui.m_txtCount.text = TimeMgr:CheckHMSNotEmpty(endTime2)
    if endTime <= 0 and self.finishState <= 0 then
        local ranking = v2n(self.rankMess.player.ranking)
        if ranking > 0 then
            self:PlayFinishReward(true)
        end
    end
end

function M:PlayFinishReward(isInit)
    self.finishState = 2
    if not isInit then
        self.ui.m_TableViewD:ReloadData(true)
        self.ui.m_TableViewD:ShowCenter(self.nowRank)
    end
	
	local ranking = v2n(self.rankMess.player.rank)
	local thinkTable = {["activity_type"] = self.activeInfo.activeId,["activity_number"] = self.activeInfo.integral,["activity_id"] = self.nowRank}
	SdkHelper:ThinkingTrackEvent(ThinkingKey.activity,thinkTable)
	
    local rewardCfg = self.rankConfig.rankRewards
    local reward
    for _, v in ipairs(rewardCfg) do
        if self.nowRank >= v.start  and  self.nowRank <= v.finish   then
            reward = v
            break
        end
    end
    if reward and self.activeInfo.rankIsReward == false then
		--Log.Error("副本排行榜埋点","activity_type:",self.activeInfo.activeId,"activity_number",self.activeInfo.integral,"activity_id",ranking)
		
        --self:CreateScheduleFun(function()
            local rankBox = RankBoxFlyAnim.new()
            rankBox:Init(self.uiGameObject,nil,reward.rewards,nil,2)
			if nil == self.m_childGo then
				rankBox:AnimFlyItem(reward.icon,Vector3.zero)
			else
				rankBox:AnimFlyItem(reward.icon,self.m_childGo.transform.position)	
			end
       -- end,0.5,1)
		local ranking = v2n(self.rankMess.player.rank)
		local thinkTable = {["activity_type"] = self.activeInfo.activeId,
			["activity_number"] = self.activeInfo.integral,["activity_id"] = self.nowRank,
			["rankReward"] = 1}
		SdkHelper:ThinkingTrackEvent("rankRewardActivity",thinkTable)
    else
        self:ReceiveUPReward()
    end		
end

function M:SetItem(rankMess,isPush)
    self.rankMess = rankMess
    self.nowRank = self.activeInfo.rank
	SdkHelper:UserSet(UserSet.rank_Island,self.activeInfo.rank)
    self.isPush = isPush
end

function M:InitReward()
    local pointSch,sur = self:GetNowPointSch()
    local pointsList = self.rankConfig.rankPoints
    local isOver = pointSch >= #pointsList
    local nowMax = isOver and pointsList[pointSch] or pointsList[pointSch+1]
    local sumSur = 0
    if pointSch > 0 then
        sumSur = isOver and pointsList[pointSch-1] or pointsList[pointSch]
    end
    local per = sur/(nowMax - sumSur)
    local progress = math.min((pointSch + per)  * 0.18,1)
    if isOver then
        SetActive(self.ui.m_goNormal,false)
        SetActive(self.ui.m_goSpe,true)
        self.ui.m_txtProgress1.text = self.activeInfo.integral
    else
        SetActive(self.ui.m_goNormal,true)
        SetActive(self.ui.m_goSpe,false)
        self.ui.m_txtProgress.text = self.activeInfo.integral .. "/" .. nowMax
    end
    SetUISize(self.ui.m_imgSlider,progress * 1336,37)
    local rewardList = self.rankConfig.rewards
    for i = 1, 5 do
        local item = {}
        item.img = self.ui["m_imgR" .. i]
        item.txt = self.ui["m_txtC" .. i]
        item.reImg = self.ui["m_imgP"..i]
        item.mess = rewardList[i]
        SetImageSprite(item.img,ItemConfig:GetIcon(item.mess[1]),false)
        item.txt.text = "x" .. item.mess[2]
        self.rewardList[i] = item
        if i > pointSch then
            self:SetRewardState(item,RewardState.Normal)
        else
            if i > self.activeInfo.schedule and i <= pointSch then
                self:SetRewardState(item,RewardState.Unclaimed)
            else
                self:SetRewardState(item,RewardState.Received)
            end
        end
    end
end

function M:GetNowPointSch()
    local pointsList = self.rankConfig.rankPoints
    local nowSch = 0
    local integral = self.activeInfo.integral
    local sur = integral
    local temp
    for i, v in ipairs(pointsList) do
        temp = v2n(v)
        if integral >= temp then
            nowSch = i
        else
            break
        end
    end
    temp = nowSch > 0 and pointsList[nowSch] or 0
    sur = sur - temp
    return nowSch,sur
end

function M:SetRewardState(item,state)

    if state == RewardState.Normal then
        SetUIBtnGrayAndEnable(item.img,false)
        item.img.color = Color.New(1,1,1,1)
        SetActive(item.reImg,false)
    elseif state == RewardState.Unclaimed then
        SetUIBtnGrayAndEnable(item.img,true)
        SetImageSprite(item.reImg,"Sprite/ui_public/StatusIcon-wrong.png",false)
        item.img.color = Color.New(1,1,1,1)
        SetActive(item.reImg,true)
    elseif state == RewardState.Received then
        SetUIBtnGrayAndEnable(item.img,false)
        SetImageSprite(item.reImg,"Sprite/ui_public/StatusIcon-complete",false)
        item.img.color = Color.New(1,1,1,0.5)
        SetActive(item.reImg,true)
    end
end

function M:InitUIList()
    local rankMess = self.rankMess
    local rankConfig = self.rankConfig

    local function  SetRankItem(go,index,isNotTip)

        local parentObj = go
        local mess = self.rankMess.ranking[index]
        local myBg = GetChild(go,"mybg")
        local imgBox = GetChild(go,"boxIcon",UEUI.Image)

        local nowRank = mess.rank
        if self.nowRank == index then
            SetActive(myBg,true)
            if not self.initSelf then
                self.initSelf = true
                SetRankItem(self.ui.m_goUp,index)
                SetRankItem(self.ui.m_goDown,index)
            end
            if isNotTip then
                self.m_childGo = imgBox.gameObject
            end
        else
            SetActive(myBg,false)
        end
        local imgNum = GetChild(go,"imgNum",UEUI.Image)
        local rankNum = GetChild(go,"num",UEUI.Text)
        if mess.rank <= 3 then
            SetActive(rankNum,false)
            SetActive(imgNum,true)
            SetImageSprite(imgNum,"Sprite/ui_activity/ranking-crown".. mess.rank .. ".png",true)
            --rankNum.text = mess.rank
        else
            SetActive(imgNum,false)
            SetActive(rankNum,true)
            rankNum.text = mess.rank
        end

        GetChild(go,"iconBg/name",UEUI.Text).text = mess.name
		local customHeadObj = GetChild(go,"iconBg/CustomHead")
		SetHeadAndBorderByGo(customHeadObj,mess.icon,mess.border)
        --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, mess.icon)
        --if headConfig then
            --SetImageSprite(GetChild(go,"iconBg/head",UEUI.Image),headConfig["icon"],false)
        --end
        GetChild(go,"Image/point",UEUI.Text).text = mess.point


        local index = 0
        if isNotTip then
            local canvasGroup = GetComponent(go,UE.CanvasGroup)
            canvasGroup.alpha = 1
        end
        for i, v in ipairs(rankConfig.rankRewards) do
            if nowRank >= v.start  and  nowRank <= v.finish   then
                index = i
                SetImageSprite(imgBox,v.icon,false)
                break
            end
        end

        if index > 0 then
            SetActive(imgBox,true)
            RemoveUIComponentEventCallback(GetComponent(imgBox,UEUI.Button))
            AddUIComponentEventCallback(imgBox,UEUI.Button,function(arg1,arg2)
                self:SetBoxClose()
                if self.rankBox == nil then
                    self.rankBox = RankBox.new(self.uiGameObject)
                    self.rankBox:InitUI(nil,nil, function()
                        if self.activeInfo.openLevel >= RANKLEVEL then
                            self.rankBox:UpdateItem(GetChild(parentObj,"boxIcon/boxObj"),rankConfig.rankRewards[index].reward_1)
                        else
                            self.rankBox:UpdateItem(GetChild(parentObj,"boxIcon/boxObj"),rankConfig.rankRewards[index].reward)
                        end
                    end)
                end
            end)
        else
            SetActive(imgBox,false)
        end
    end

    local count = #rankMess.ranking
    local newCount = count
    local rankLength = self.nowRank

    local height = 110
    local offsetY = 20
    local up = rankLength * (height + offsetY) - offsetY
    local down =  (rankLength - 10) * (height + offsetY)
    --if self.nowRank == 1 then
    --    up = up + 15
    --elseif self.nowRank == count then
    --    down = down - 40
    --end
    local initScroll = false

    self.ui.m_TableViewD_Scroll.onValueChanged:AddListener(function(newScrollValue)
        --local relativeScroll = 1 - newScrollValue.y
        --local m_scrollY = relativeScroll * scrollableHeight
        if self.lastY and self.rankBox  then
            local temp = math.abs(self.lastY - self.ui.m_rtransContent.anchoredPosition.y)
            if temp > 1 then
                self:SetBoxClose()
            end
        end
        self.lastY = self.ui.m_rtransContent.anchoredPosition.y
        local y = self.ui.m_rtransContent.anchoredPosition.y
        if not initScroll then return end
        if y >= up then
            SetActive(self.ui.m_goUp,true)
            SetActive(self.ui.m_goDown,false)
            DOScale(self.ui.m_goUp.transform, Vector3.New(1.01, 1.01, 1), 0.2, nil, Ease.OutBack)
        elseif y <= down then
            SetActive(self.ui.m_goUp,false)
            SetActive(self.ui.m_goDown,true)
            DOScale(self.ui.m_goDown.transform, Vector3.New(1.01, 1.01, 1), 0.2, nil, Ease.OutBack)
        else
            self.ui.m_goUp.transform:SetLocalScale(1,1,1)
            self.ui.m_goDown.transform:SetLocalScale(1,1,1)

            SetActive(self.ui.m_goUp,false)
            SetActive(self.ui.m_goDown,false)
        end

    end)

    self.ui.m_TableViewD.GetCellCount = function() return newCount end
    self.ui.m_TableViewD.GetCellSize = function( tableView, index )
        return Vector2(1347,height)
    end
    self.ui.m_TableViewD.UpdateCell = function( tableView,index )
        if not tableView then
            return nil
        end
        local cell = tableView:GetReusableCell()
        local chatObj
        if not cell then
            chatObj = UEGO.Instantiate(self.ui.m_goList)
            SetActive(chatObj,true)
			self:ChangeImage(chatObj,self.activeForm.activeMess.pass_icon2)
            cell = GetAndAddComponent(chatObj,CS.CCTableViewCell)
            chatObj.name = v2s(index + 1)
        end
        local upValue = 0
        local isCreate = false
        function SetSpecialObj(cell)
            isCreate = true
            local canvasGroup = GetComponent(cell.gameObject,UE.CanvasGroup)
            canvasGroup.alpha = 0
        end

        if not isCreate then
            SetRankItem(cell.gameObject,index+1+upValue,true)
        end
        return cell
    end
    self.ui.m_TableViewD:ReloadData(true)
    local lastRank = NetInfoData:GetDataMess(NetInfoData.Define.LastLimitRank)
    if lastRank then
        self.ui.m_TableViewD:ShowCenter(lastRank)
        if lastRank ~= self.nowRank then
            self.ui.m_TableViewD:ShowCenter(self.nowRank,function()
                NetInfoData:SetDataMess(NetInfoData.Define.LastLimitRank,self.nowRank)
                initScroll = true
            end)
        else
            initScroll = true
        end
    else
        self.ui.m_TableViewD:ShowCenter(self.nowRank,function()
            NetInfoData:SetDataMess(NetInfoData.Define.LastLimitRank,self.nowRank)
            initScroll = true
        end)

    end

end

function M:SetBoxClose()
    if self.rankBox then
        self.rankBox:Destory()
        self.rankBox = nil
    end
end

function M:OnRefresh(type,param,isPush)
    if type == 1 then
        for _, v in ipairs(param) do
            if v.Id < ItemID._RESOURCE_MAX then
                MapController:AddResourceBoomAnim(v.Pos.x,v.Pos.y,v.Id,v.count)
                NetUpdatePlayerData:AddResource(PlayerDefine[v.Id], v.count,nil,nil,"UI_RankLimit")
            else
                self.bubble:FlyItem(v.Id,v.count,v.Pos)
            end
        end
		self.active:ClaimRankReward()
        self:ReceiveUPReward()
		local ranking = v2n(self.rankMess.player.rank)
		local thinkTable = {["activity_type"] = self.activeInfo.activeId,
			["activity_number"] = self.activeInfo.integral,["activity_id"] = self.nowRank,
			["rankReward"] = 2}
		SdkHelper:ThinkingTrackEvent("rankRewardActivity",thinkTable)
    elseif type == 2 then
        self:SetItem(param,isPush)
    end
end

function M:onDestroy()
    self:ClosePush()
end

function M:TickUI(deltaTime)
    if self.init then
        self.init = self.init + deltaTime
    end
    if self.init and self.init > 2 then
        self.init = nil

    end
    self.deltaTime = deltaTime + self.deltaTime
    if self.deltaTime > 0.5 then
        self.deltaTime = 0
        self:Timer()
    end
end

function M:ReceiveUPReward()
    local pointSch = self:GetNowPointSch()
    if  pointSch > self.activeInfo.schedule then
        local now = self.activeInfo.schedule + 1
        for i = pointSch,now,-1 do
            local item = self.rewardList[i]
            self.bubble:FlyItem(item.mess[1],item.mess[2],item.img.transform.position)
            self:SetRewardState(item,RewardState.Received)
			--local thinkTable = {["activity_type"] = self.activeInfo.activeId,["activity_number"] = self.activeInfo.integral,["activity_id"] = now}
			--SdkHelper:ThinkingTrackEvent(ThinkingKey.activity,thinkTable)
			--Log.Error("副本排行榜埋点","activity_type:",self.activeInfo.activeId,"activity_number",self.activeInfo.integral,"activity_id",now)
        end
        self.bubble:SetCloseCall(function()
            self.active:SetSchedule(pointSch)
            self:Close()
        end)
    end
end

function M:onUIEventClick(go,param)
    local name = go.name
    if name == "close" then
        self:CheckClose()
	elseif name == "m_btn_help" then 
		UI_SHOW(UIDefine.UI_RankLimitHelp)
    elseif string.startswith(name,"m_imgR")then
        self:ReceiveUPReward()
    end
    self:SetBoxClose()

end

function M:CheckClose()
    if self.rankBox then
        self:SetBoxClose()
    end

    if self.bubble:IsHaveItem() then
        self.bubble:Close()
    else
        self:Close()
    end
end

function M:AutoClose()
    self:CheckClose()
end

function M:ClosePush()
    if self.isPush then
        NetPushViewData:RemoveViewByIndex(PushDefine.RankLimit)
        NetPushViewData:CheckOtherView(true)
    end
    self.isPush = nil
    if self.active:IsActivityEnd() and self.finishState >= 2 then
        NetInfoData:SetDataMess(NetInfoData.Define.LastLimitRank,nil)
        self.active:CloseActive()
    end
end

return M
---



---
local ObjItem = require "Game.MapLogic.ObjItem"
local ObjItemCastle = Class(ObjItem)
local M = ObjItemCastle

function M:onInit(inf)
    self.m_State = 0
    self.m_Lv = 1
    self.m_Exp = 0
    self.m_Timestamp = 0
    self.m_RankItem = nil
    self.m_AdId = 18
	self.useSkinId = 0
	self.skinDefDecorateTrans = nil
	
	self.m_OrderLayerId = SortingLayerInGame.WORLD_ITEM
	self:setOrderLayer(SortingLayerInGame.WORLD_ITEM)
    local function onFinishDrop(drop)
        local x, y, z = GetPosByGrid(self.m_GridX, self.m_GridY)
        MapController:AddResourceAnim(self.m_GridX, self.m_GridY, ItemID.EXP, self.m_CurInf.exp_add, Vector3.New(x + 1.0, y, z),nil,"Build")
        self.m_Timestamp = TimeMgr:GetServerTime() + self.m_CurInf.time
        self:setState(3)
        drop:DestroyUI()
    end
    local drop = DropController.new()
    drop:SetOffsetByItem(nil, self.m_Config)
    drop:Init(false, onFinishDrop)
    drop:SetGrid(self.m_GridX, self.m_GridY, self.m_Vt3Pos.x + 1.2, self.m_Vt3Pos.y + 1.5)
    self.m_ObjDrop = drop

    if inf then
        local data = inf["castle"]
        if data then
            self.m_State = data["state"]
            self.m_Exp = data["exp"]
            self.m_Lv = data["lv"]
            self.m_Timestamp = data["ts"]
            self.m_RankItem = data["rewardRankItem"]
			self.useSkinId = data["useSkinId"]
            self.m_ObjDrop:Load(data["drop"])
        end
    end

    local food = {}
    local listMat = string.split(self.m_Config["id_waste"], ';')
    for i, v in ipairs(listMat) do
        local arrInf = string.split(v, '|')
        food[arrInf[1]] = arrInf[2]
    end
    self.m_Items = food
    self.m_IWantToEat = false

    --Memory
    self.m_LvMax = 5
    self.expOld = 0          -- 旧经验值
    self.expPreview = 0      -- 预览加成的经验值
    self.levelOld = 1        -- 旧等级
    self.eatEffectCount = 0  -- 吃物品的特效数量
    self:updateUpgradeInfo(self.m_Lv)
    self:setIsUpdate(true)
	if not MapControllerVisit:IsVisit() then
		local thinkTable = {
			["build_id"] = self.m_Id,
			["build_star"] = self.m_Lv,
			["build_progress"] = self.m_Exp
		}
		SdkHelper:ThinkingTrackEvent("build_star",thinkTable)
	end
end

function M:onSaveInf(inf)
    local data = {}
    data["state"] = self.m_State
    data["exp"] = self.m_Exp
    data["lv"] = self.m_Lv
    data["ts"] = self.m_Timestamp
    data["rewardRankItem"] = self.m_RankItem
    data["drop"] = self.m_ObjDrop:Save()
	data["useSkinId"] = self.useSkinId
    inf["castle"] = data
end

function M:ResetSkin()
	local mess
	if self.useSkinId > 0 then
		mess = SkinCollectConfig:GetDataByID(self.useSkinId)
	else
		mess = SkinCollectConfig:GetDefaultSkin(self.m_Id)
	end
	EventMgr:Dispatch(EventID.MAP_FLOOR_DRESS)
    -- 改为异步加载
    ResMgr:LoadAssetWithCache(mess.skin_icon, AssetDefine.LoadType.Sprite, function(spr)
        if spr then
            SetSprite3D(self.m_SpItem, spr)
        end
    end)
	self:LoadSkin(mess)
end

function M:LoadSkin(config,icon_path,isPreview)
	if not icon_path then
		icon_path = config.skin_icon
	end
    -- 异步加载皮肤图片
    ResMgr:LoadAssetWithCache(icon_path, AssetDefine.LoadType.Sprite, function(spr)
        if spr then
            SetSprite3D(self.m_SpItem, spr)
        end
    end)

	--默认装饰
	if config.default_decorate then
		if self.skinDefDecorateTrans then
			UEGO.Destroy(self.skinDefDecorateTrans.gameObject)
			self.skinDefDecorateTrans = nil
		end
        -- 异步加载装饰预制体
        ResMgr:LoadAssetWithCache(config.default_decorate, AssetDefine.LoadType.Instant, function(prefab)
            if prefab then
                local go,trans = CreateGOAndTrans(prefab)
                go.name = "skinDefDecorate"
                trans:SetParent(self.m_TransSpinePa)
                trans:SetLocalPosition(0,-0.7,0)
                trans:SetLocalScale(1,1,1)

                self:ResetTransSpinePa()

                self.skinDefDecorateTrans = trans
            end
        end)
	else
		if self.skinDefDecorateTrans then
			SetActive(self.skinDefDecorateTrans,false)
		end
	end

	--设置层级
	if isPreview then
		self:SetDecorateLayer(SortingLayerInGame.UI_TOP, 600)
	else
		self:ResetDecorateLayer()
	end

	if config.default_decorate then
		SetActive(self.m_TransSpinePa.gameObject,true)
	else
		SetActive(self.m_TransSpinePa.gameObject,false)
	end

end

function M:SetDecorateLayer(layerID,order)
	if not layerID then
        layerID = self.m_OrderLayerId
    end
    if not order then
        order = self.m_OrderIdx
    end

	--设置层级
	local renders = self.m_TransSpinePa:GetComponentsInChildren(TP(UE.Renderer),true)
	for i = 0, renders.Length - 1 do
		if not self.CacheDecorateLayerData then
			self.CacheDecorateLayerData = {}
		end
		self.CacheDecorateLayerData[renders[i]] = {[1] = renders[i].sortingLayerID,[2] = renders[i].sortingOrder}
		SetRendererOrder(renders[i], layerID, order)
	end
end

function M:ResetDecorateLayer()
	local renders = self.m_TransSpinePa:GetComponentsInChildren(TP(UE.Renderer),true)
	for i = 0, renders.Length - 1 do
		if self.CacheDecorateLayerData and self.CacheDecorateLayerData[renders[i]] then
			local cache = self.CacheDecorateLayerData[renders[i]]
			SetRendererOrder(renders[i], cache[1], cache[2])
		end
	end
end

function M:ResetTransSpinePa()
	local vtOff = GetVector3ByStr(self.m_Config["offset"])
	if vtOff then
		self.m_TransSpinePa:SetLocalPosition(vtOff.x, vtOff.y, vtOff.z)
	else
		self.m_TransSpinePa:SetLocalPosition(0,0,0)
	end

	local vtScale = GetVector3ByStr(self.m_Config["scale"])
	if vtScale then
		self.m_TransSpinePa:SetLocalScale(vtScale.x, vtScale.y, vtScale.z)
	else
		self.m_TransSpinePa:SetLocalScale(1,1,1)
	end
end

function M:ChangeSkin(skinId,icon_path,isPreview)
	if skinId < 0 then return end
	
	local skincfg = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_collection,  skinId)

	local is_have = NetHandbook:IsHaveSkin(NetHandbook.ID.Castle,skinId)
	if isPreview then
		is_have = true
	end
	if is_have then
		if not isPreview then
			self.useSkinId = skinId
		end
		EventMgr:Dispatch(EventID.MAP_CASTLE_DRESS)
        -- 改为异步加载
        ResMgr:LoadAssetWithCache(icon_path, AssetDefine.LoadType.Sprite, function(spr)
            if spr then
                SetSprite3D(self.m_SpItem, spr)
            end
        end)
		self:LoadSkin(skincfg,icon_path,isPreview)
	end
end

function M:HeightShow(IsShow)
	if IsShow then
		self.OldLayer = self.m_OrderLayerId
		self:setOrderLayer(SortingLayerInGame.UI_TOP)
		self.m_Animator3 = GetComponent(self.m_SpTileBottom.gameObject, SpriteRenderer)
		self.OldLayer2 = self.m_Animator3.sortingLayerName
		self.m_Animator3.sortingLayerName = "UI_TOP"
		self:SetDecorateLayer(SortingLayerInGame.UI_TOP)
	else
		if self.OldLayer ~= nil then
			self:setOrderLayer(self.OldLayer)
			self.m_Animator3.sortingLayerName = self.OldLayer2
			self:ResetDecorateLayer()
		end
	end
end

function M:GetUseSkinId()
	return self.useSkinId
end

function M:onLoaded()
	if not self.useSkinId or self.useSkinId <= 0 then return end
	local config = ConfigMgr:GetDataByID(ConfigDefine.ID.skin_collection,self.useSkinId)
	if config then
		self:ChangeSkin(self.useSkinId,config.skin_icon)
	end
end

function M:onEnable()
    local mapId = NetUpdatePlayerData.playerInfo.curMap
    if IsHomeMap(mapId) and NetMapNoteData:GetNoteCount(mapId,NetMapNoteData.ID.item_has,self.m_Id) <= 0 then
		if not MapControllerVisit:IsVisit() then
        	NetMapNoteData:AddNoteCount(mapId,NetMapNoteData.ID.item_has,self.m_Id)
		end
    end
    if not self.m_Animator then
        local function loadDone(clip)
            self.m_Animator = GetAndAddComponent(self.m_SpItem.gameObject, Animation)
            self.m_Animator:AddClip(clip, "castle_eat")

            self.m_Animator2 = GetAndAddComponent(self.m_SpTileBottom.gameObject, Animation)
            self.m_Animator2:AddClip(clip, "castle_eat")
        end
        ResMgr:LoadAssetAsync("Assets/ResPackage/Animation/castle_eat.anim", AssetDefine.LoadType.Animation, loadDone)
    end

    if not self.m_EffectNormal then
        local function loadDone(data, tGo, go)
            SetActive(tGo, false)
            self.m_EffectNormal = tGo
        end
        local pos = self:getNodeOffset()
        EffectConfig:CreateEffect(56, pos.x, pos.y, 0, self.m_TransWidgetBottom, loadDone)
    end

    if not self.m_EffectBusy then
        local function loadDone(data, tGo, go)
            SetActive(tGo, false)
            self.m_EffectBusy = tGo
        end
        local pos = self:getNodeOffset()
        EffectConfig:CreateEffect(57, pos.x, pos.y, 0, self.m_TransWidgetBottom, loadDone)
    end

    --if not self.m_TransRepair then
        --local function loadDone(prefab)
            --if prefab then
                --local newGo, newTrans = CreateGOAndTrans(prefab)
                --newTrans:SetParent(self.m_TransWidgetTop)
                --newTrans:SetLocalPosition(0, 0, 0)
                --newTrans:SetLocalScale(1, 1, 1)
                --SetActive(newTrans, false)
                --self.m_SpCloudRepair = SearchChild(newTrans, "sprite", SpriteRenderer)
                --self.m_TransRepair = newTrans
            --end
        --end
        --ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/castle_repair.prefab", AssetDefine.LoadType.Instant, loadDone)
    --end
	
	if not self.m_TransRepair then
		local function loadDone(prefab)
		if prefab then
		local newGo, newTrans = CreateGOAndTrans(prefab)
		newTrans:SetParent(self.m_TransWidgetTop)
		newTrans:SetLocalPosition(0, 0, -10)
		newTrans:SetLocalScale(1, 1, 1)
		SetActive(newTrans, false)
		DOLocalMoveYLoop(newTrans, newTrans.localPosition.y + 0.2, 2, LoopType.Yoyo, Ease.InOutSine)	
		--self.m_SpCloudRepair = SearchChild(newTrans, "sprite", SpriteRenderer)
		self.m_TransRepair = newTrans
		end
		end
		
		if not MapControllerVisit:IsVisit() then
			ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/castle_state1.prefab", AssetDefine.LoadType.Instant, loadDone)
		end
	end
	

    if not self.m_TransCastleStar then
        local function loadDone(prefab)
            if prefab then
                local newGo = CreateGameObjectWithParent(prefab, self.m_TransWidgetTop)
                local newTrans = newGo.transform
                SetLocalPositionTrans(newTrans, 1.5, -0.7, -10)

                local canvas = GetComponent(newTrans, TP(UE.Canvas))
                if canvas then
                    canvas.overrideSorting = true
                    canvas.sortingOrder = 0
                    canvas.sortingLayerID = SortingLayerInGame.WORLD_ITEM
                end

				SetUIFirstSibling(newGo)
                self.m_ImgStarBg = {}
                self.m_ImgStar = {}
                for i = 1, self.m_LvMax do
                    self.m_ImgStarBg[i] = SearchChild(newTrans, "star_bg_" .. tostring(i - 1), UEUI.Image)
                    self.m_ImgStar[i] = SearchChild(self.m_ImgStarBg[i].transform, "star_" .. tostring(i - 1), UEUI.Image)
                end
                self.m_ImgStarScale = SearchChild(newTrans, "star_scale", UEUI.Image)
                self.m_ImgStarOrg = SearchChild(newTrans, "star_scale/star_org", UEUI.Image)
                self.m_ImgStarPro = SearchChild(newTrans, "star_scale/star_pro", UEUI.Image)

                self.m_TransCastleStar = newTrans

                SetActive(self.m_ImgStarScale, false)
                self:updateUIPos()
                self:updateCastle()
                self:setState(self.m_State, true)
            end
        end
        ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/castle_star.prefab", AssetDefine.LoadType.Instant, loadDone)
    end

    -- 城堡广告 首次根据广告状态判断是否显示  之后通过监听事件触发
    if not self.m_TransAds then
        local function loadDone(prefab)
            if prefab then
                local newGo, newTrans = CreateGOAndTrans(prefab)
                MapController:AddUIToWorld(newTrans)
                self.m_TransAds = newTrans

                DOScaleLoop(self.m_TransAds.transform, 0.009, 1.2, LoopType.Yoyo, Ease.InOutSine)
                local isCan = ADMovieModule:GetStateByAdId(self.m_AdId)
                SetActive(self.m_TransAds, isCan)

                self:updateUIPos()

                local listener = function()

                    if not self.m_RankItem then
                        local tools = DropController.new()
                        local list, _ = tools:RandDrop(self.m_CurInf.ad_reward)
                        if list then
                            self.m_RankItem = list[1]
                        end
                    end

                    if self.m_RankItem then
                        local reward =  ADMovieModule:GetRankingByAdId(self.m_AdId)
                        local count = reward > 0 and reward or 1
                        UI_SHOW(UIDefine.UI_AD_1, self, self.m_AdId, tonumber(self.m_RankItem), count)
                    end
                end

                local btn = SearchChild(newTrans, "bg", UEUI.Button)
                self.m_ImgBgAds = SearchChild(newTrans, "bg", UEUI.Image)
                self.m_ImgAds = SearchChild(newTrans, "bg/ads", UEUI.Image)
                btn.onClick:AddListener(listener)
            end
        end
        ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/castle_ads.prefab", AssetDefine.LoadType.Instant, loadDone)
    end

    EventMgr:Add(EventID.AD_STATE, self.OnAdStateChange, self)
end

function M:OnAdStateChange(state, adId)
    if adId ~= self.m_AdId then
        return
    end

    local isCan = state == 0

    if self.m_TransAds.gameObject.activeSelf ~= isCan then
        SetActive(self.m_TransAds, isCan)
    end
end

function M:GetAdsReward()
    SetActive(self.m_TransAds, false)
    self.m_RankItem = nil
end

function M:onDrag(state)
    if state == 0 then
        self:setUIState(0)
        --SetRendererOrder(self.m_SpCloudRepair, SortingLayerInGame.WORLD_PICK, 0)
        if self.m_State == 2 then
            self.m_ObjDrop:onEffDisappear()
        end
		self:SetDecorateLayer(SortingLayerInGame.WORLD_PICK)
    elseif state == 1 then
        self:setUIState(1)
       -- SetRendererOrder(self.m_SpCloudRepair, SortingLayerInGame.WORLD_ITEM, 0)
        if self.m_State == 2 then
            self.m_ObjDrop:onEffBurstOut()
        end
		self:ResetDecorateLayer()
    end
end

function M:onGridChange(gridX, gridY)
    -- if self.m_State == 2 then
    self.m_ObjDrop:SetGrid(gridX, gridY, self.m_Vt3Pos.x + 1.2, self.m_Vt3Pos.y + 1.5)
    -- end
end

function M:onDestroy()
	self.CacheDecorateLayerData = nil
    EventMgr:Remove(EventID.AD_STATE, self.OnAdStateChange, self)
end

function M:AddDressArrow()
	local function onLoaded(prefab)
		if prefab then
			local newGo, newTrans = CreateGOAndTrans(prefab)
			newTrans:SetParent(self.m_TransWidgetTop)
			newTrans:SetLocalScale(1, 1, 1)
			--if self.m_Offset then
			--newTrans:SetLocalPosition(self.m_Offset.x, self.m_Offset.y, 0)
			--else
			newTrans:SetLocalPosition(0, 0, 0)
			newTrans.localRotation = Quaternion.Euler(0,0,0)
			--end

			--if self:getNodeRota() then
			--newTrans.localEulerAngles = Vector3.New(0, 0, self:getNodeRota())
			--end

			newTrans.name = "dress_arrow"
			local renderer = SearchChild(newTrans, "offset/m_sp_arrow", SpriteRenderer)
			DOLocalMoveYLoop(renderer.transform, 0.3, 0.5, LoopType.Yoyo, Ease.InOutSine)
			self.m_RenderArrow = renderer
		end
	end
	ResMgr:LoadAssetWithCache("Assets/ResPackage/Prefab/Map/dress_arrow.prefab", AssetDefine.LoadType.Instant, onLoaded)
end

function M:onClicked()
    if self.m_State == 0 then
        self:ShowCollectUI()

    elseif self.m_State == 1 then
        --waiting worker
		if self.m_WorkID == nil then
			self:setState(3)
		end
    elseif self.m_State == 2 then
        --drop
        self.m_ObjDrop:onClick()

    elseif self.m_State == 3 then
        -- 正在飞物品进城堡，暂时不能打开城堡升级界面
        if not MapController.hasCastleQueue then
            UI_SHOW(UIDefine.UI_CastleUpgrade, self)
        end
    end
	if self.m_RenderArrow then
		UEGO.Destroy(self.m_RenderArrow.gameObject)
		self.m_RenderArrow = nil
	end
end

function M:setState(state, isInit)
    if state ~= self.m_State or isInit then
        self.m_State = state
        SetActive(self.m_TransRepair, false)
        if state == 0 then
            self:setIsUpdate(false)
            self.m_Timestamp = 0
            --if not isInit then
                if not GuideController:IsGuiding() then
                    MapController:MoveCameraToItem(self, nil, true)
                end
                --remove building anim
                self:ShowCollectUI()
            --end
		 	SetActive(self.m_TransRepair, true)
            SetActive(self.m_EffectNormal, true)
            SetActive(self.m_EffectBusy, false)

        elseif state == 1 then
            SetActive(self.m_EffectNormal, false)
            SetActive(self.m_EffectBusy, false)


        elseif state == 2 then
            self.m_ObjDrop:ShowUI()
            SetActive(self.m_EffectNormal, false)
            SetActive(self.m_EffectBusy, false)

        elseif state == 3 then
            SetActive(self.m_EffectNormal, false)
            SetActive(self.m_EffectBusy, true)

            self:setIsUpdate(true)
           -- SetActive(self.m_TransRepair, true)
            --add building anim
        end
    end
end

--CLear UI
--function M:GetClearUIPos()
--  --  return self.m_Vt3Pos.x + 0.5, self.m_Vt3Pos.y + 1
--end
function M:GetEnergyOutPut()
    if self.m_CurInf then
        return self.m_CurInf.energy
    end
    return 0
end

function M:GetClearUIPos()
    return self.m_Vt2Pos.x + 1.2, self.m_Vt2Pos.y + 1.5
end

function M:GetCountTime()
    if self.m_CurInf then
        return self.m_CurInf.time
    end
    return 0
end

function M:GetBloodState()
    return 1, 1
end

--Worker
function M:OnStartCollect(fromAI)
    local numType = tonumber(self.m_CurInf.energy_id)
    local numConsume = tonumber(self.m_CurInf.energy)

    local workerId = 0
    if fromAI then
        workerId = WorkerController:UseVirtualWorkerNewCopy(numConsume, nil, self, numType)
    else
        workerId = WorkerController:UseWorker(numConsume, nil, self,numType )
    end
    
    if workerId <= 0 then
        return
    end
    NetNotification:NotifyNormal(NotifyDefine.CastleWake,self.m_Config.id)
    local pos = self:getNodeOffset()
    EffectConfig:CreateEffect(58, pos.x, pos.y, 0, self.m_TransWidgetBottom)

    local tween = self.m_NodeTrans:DOPunchScale(Vector3.New(0.05, 0.1, 0), 0.5, 1, 1)
    tween:SetLoops(6, LoopType.Yoyo)

    self:setState(1)
    self.m_WorkID = workerId
    EventMgr:Add(EventID.FINISH_WORKER, self.OnFinishCollect, self)
    self:setEffLightUp(true, true)
    self.m_IsCollecting = true
end

function M:OnFinishCollect(fId)
    if self.m_WorkID then
        if self.m_WorkID == fId then
            self.m_WorkID = nil
            TaskConfig:AddCastleCollect(self.m_Config.id)
            DOKill(self.m_NodeTrans)
            self.m_ObjDrop:SetDropId(self.m_CurInf.drop)
            self:setState(2)
            EventMgr:Remove(EventID.FINISH_WORKER, self.OnFinishCollect, self)
            AudioMgr:Play(31)
            self:setEffLightUp(false)
            self.m_IsCollecting = false
			EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.CASTLE_COLLECT,1)
        end
    end
end

function M:TickNode(delta)
    if self.m_State == 3 then
        local timeLast = self.m_Timestamp - TimeMgr:GetServerTime()
        if timeLast <= 0 then
            self:setState(0)
            AudioMgr:Play(24)
        end
    end
end

function M:setUIState(st)
    if st == 0 then
        DOScale(self.m_TransCastleStar, Vector3.New(0, 0, 0), 0.1, nil, Ease.InSine)
        DOColor(self.m_ImgBgAds, Color.New(1, 1, 1, 0), 0.05, nil, Ease.InSine)
        DOColor(self.m_ImgAds, Color.New(1, 1, 1, 0), 0.05, nil, Ease.InSine)

    elseif st == 1 then
        self:updateUIPos()
        DOScale(self.m_TransCastleStar, Vector3.New(0.01, 0.01, 0.01), 0.2, nil, Ease.OutSine)
        DOColor(self.m_ImgBgAds, Color.New(1, 1, 1, 1), 0.1, nil, Ease.OutSine)
        DOColor(self.m_ImgAds, Color.New(1, 1, 1, 1), 0.1, nil, Ease.OutSine)
    end
end

function M:updateUIPos()
    SetLocalPositionTrans(self.m_TransAds, self.m_Vt3Pos.x + 0.1 , self.m_Vt3Pos.y + 1.2, self.m_Vt3Pos.z)
end

function M:updateCastle()
    for i = 1, self.m_LvMax do
        if i <= self.m_Lv then
            self.m_ImgStar[i].fillAmount = 1
        elseif i == self.m_Lv + 1 then
            self.m_ImgStar[i].fillAmount = self.m_Exp / self.m_CurInf.exp
        else
            self.m_ImgStar[i].fillAmount = 0
        end
    end
end

--- 吃物品的特效
function M:setEffEat()
    local pos = self:getNodeOffset()
    -- 一次播放一个特效
    if self.eatEffectCount >= 1 then
        return
    end
    self.eatEffectCount = self.eatEffectCount + 1
    EffectConfig:CreateEffect(62, pos.x, pos.y, 0, self.m_TransWidgetBottom)
    -- 间隔一段时间播放下一个特效
    local interval = 0.5
    TimeMgr:CreateTimer(self, function()
        self.eatEffectCount = self.eatEffectCount - 1
    end, interval, 1)
end

function M:onThrough(tItem)
    if tItem == nil then
        if self.m_IWantToEat then
            self.m_IWantToEat = false
            self:setEffLightUp(false)
            self.m_Animator:Stop()
            self.m_Animator2:Stop()
            self.m_SpItem.transform:SetLocalScale(1, 1, 1)
            self.m_SpTileBottom.transform:SetLocalScale(1, 1, 1)

            if self.m_Lv < self.m_LvMax then
                DOScale(self.m_ImgStarScale.transform, Vector3.New(1, 1, 1), 0.2, function()
                    SetActive(self.m_ImgStarScale, false)
                end)
            end
            return true
        end
        return false
    end
    local exp = self:IsCanEat(tItem)
    if exp then
        self.m_IWantToEat = true
        self:setEffLightUp(true, true)
        self.m_Animator:Play("castle_eat")
        self.m_Animator2:Play("castle_eat")

        if self.m_Lv < self.m_LvMax then

            SetActive(self.m_ImgStarScale, true)
            DOScale(self.m_ImgStarScale.transform, Vector3.New(1.8, 1.8, 1), 0.3)

            self.m_ImgStarScale.rectTransform.anchoredPosition3D = self.m_ImgStarBg[self.m_Lv + 1].rectTransform.anchoredPosition3D
            self.m_ImgStarPro.color = Color.New(1, 1, 1, 1)
            self.m_ImgStarPro.fillAmount = (exp + self.m_Exp) / self.m_CurInf.exp
            self.m_ImgStarOrg.fillAmount = self.m_Exp / self.m_CurInf.exp
            DOColorLoop(self.m_ImgStarPro, Color.New(1, 1, 1, 0), 0.5, LoopType.Yoyo)
        end
        return true
    end
    return false
end

function M:IsCanEat(item)
    if not item then
        return nil
    end
    local exp = self.m_Items[tostring(item.m_Id)]
    if exp then
        return tonumber(exp)
    end
    return nil
end

function M:EatItem(tItem)
    self:setEffEat()

  
    local exp = self:IsCanEat(tItem)
	EventMgr:Dispatch(EventID.CASTLE_EAT_EXP,exp)
	if self.m_Lv >= self.m_LvMax then
		return
	end
    if exp then
        local config = ItemConfig:GetDataByID(ItemID.EXP)
        if config then
            local x, y, z = GetPosByGrid(self.m_GridX, self.m_GridY)
            MapController:AddResourceJumpAnim(self.m_GridX, self.m_GridY, config, exp, Vector3.New(x + 1.0, y, z))
        end
        self.m_Exp = self.m_Exp + exp
        if self.m_Exp >= self.m_CurInf.exp then
            self.m_Exp = self.m_Exp - self.m_CurInf.exp
            self.m_Lv = self.m_Lv + 1

		    ServerPushManager:AddSendCastle(self.m_Id,self.m_Lv)

            EventMgr:Dispatch(EventID.CASTLE_LEVEL_UP, self.m_Lv, self.m_Id, self)
            self:updateUpgradeInfo(self.m_Lv)
            if not MapController.hasCastleQueue then				
                self:ShowBuildLvUP(false, nil, nil)
            end
			self:GetCastleUpReward()
        end
    end
end

function M:GetCastleUpReward()
	if self.levelOld then
		if self.m_Lv > self.levelOld then
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.castle, self.m_Config["id_use"])
			local name = "level_reward_"..self.m_Lv
			if config[name] then
				NetGlobalData:GetRewardToMap(config[name],"CastleLvUp",true,true)
			end
		end
	end
end

function M:updateUpgradeInfo(idx)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.castle, self.m_Config["id_use"])
    if not config then
        Log.Error("###### Excel config id_use nil", self.m_Id)
        return
    end
    local exp, len = GetValueFromStrData(config["exp"], idx)
    local energy_id = GetValueFromStrData(config["energy_id"], idx)
    local energy = GetValueFromStrData(config["energy"], idx)
    local time = GetValueFromStrData(config["time"], idx)
    local drop = GetValueFromStrData(config["drop"], idx)
    local exp_add = GetValueFromStrData(config["exp_add"], idx)
    local listShow = GetValueFromStrData(config["des_up"], idx)
    local ad_reward = GetValueFromStrData(config["ad_reward"], idx)
    local iconInf = GetValueFromStrData(config["show_icon"], idx)
	local slgIconInf =  GetValueFromStrData(config["show_icon2"], idx)

    self.m_LvMax = len

    local inf = {}
    inf.exp = tonumber(exp)
    inf.energy = tonumber(energy)
    inf.energy_id = tonumber(energy_id)
    inf.time = tonumber(time)
    inf.drop = tonumber(drop)
    inf.exp_add = tonumber(exp_add) or 0
    inf.show_arrow = listShow
    inf.ad_reward = ad_reward
    inf.show_icon_inf = string.split(iconInf, ';')
	inf.show_slg_icon_inf = string.split(slgIconInf, ';')
    self.m_CurInf = inf
end

function M:GetPosJumpIn()
    return Vector3.New(self.m_Vt3Pos.x + 1.0, self.m_Vt3Pos.y - 0.5, self.m_Vt3Pos.z)
end

function M:IsBusy(limitFrom)
    return self.m_IsCollecting
end

function M:ShowCollectUI()
    if self.m_CurInf.energy_id > 0 then
        local function onFun()
            self:OnStartCollect()
        end
        ClearController:Show(ClearType.Castle, self, onFun)
    end
end

function M:CanCollect()
    local energy,consume = self:GetEnergyOutPut()

    local nowenergy
	
	local mapId = NetUpdatePlayerData.playerInfo.curMap
	if IsHomeMap(mapId) then
		nowenergy=NetUpdatePlayerData:GetResourceNumByID(ItemID.ENERGY)
	else
		nowenergy=NetUpdatePlayerData:GetResourceNumByID(ItemID.LimitEnergy)
	end
	local use = 0
	if energy ~= nil and (not consume or consume == ItemID.ENERGY or consume == ItemID.LimitEnergy )  then
		use=tonumber(energy)
	end

    if use<=nowenergy then
        return true
    end
    
    return false
end

--- 设置旧的经验值
--- @param exp number 旧经验值
function M:SetExpOld(exp)
    self.expOld = exp or self.m_Exp
end

--- 获取旧的经验值
--- @return number expOld 旧经验值
function M:GetExpOld()
    return self.expOld
end

--- 设置预览加成的经验值
--- @param expPreview number 预览加成的经验值
function M:SetExpPreview(expPreview)
    self.expPreview = expPreview
end

--- 获取预览加成的经验值
--- @return number expPreview 预览加成的经验值
function M:GetExpPreview()
    return self.expPreview
end

--- 设置旧的等级
--- @param level number 旧的等级
function M:SetLevelOld(level)
    self.levelOld = level or self.m_Lv
end

--- 获取旧的等级
--- @return number levelOld 旧的等级
function M:GetLevelOld()
    return self.levelOld
end

--- 预览增加的等级
--- @param addExp any 预览增加的经验值
--- @param level any 当前等级
--- @return integer addLevel 提升的等级
function M:AddLevelPreview(addExp, level)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.castle, self.m_Config["id_use"])
    if not config then
        Log.Error("###### Excel config id_use nil", self.m_Id)
        return
    end
    local remainExp = addExp
    local addLevel = 0
    local currentLevel = level or self.m_Lv
    if currentLevel < self.m_LvMax then
        -- 当前等级升级所需经验值
        local exp, len = GetValueFromStrData(config["exp"], currentLevel)
        for i = 1, len, 1 do
            -- 预览扣除剩余经验值
            remainExp = remainExp - v2n(exp)
            if remainExp >= 0 then
                -- 预览下一个等级所需经验值
                addLevel = addLevel + 1
                currentLevel = currentLevel + 1
                exp = GetValueFromStrData(config["exp"], currentLevel)
            else
                break
            end
        end
    end
    return addLevel
end

--- 当前等级所需经验
--- @param level number 当前等级
--- @return number|nil exp 升级需要的经验值
function M:CurrentLevelNeedExp(level)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.castle, self.m_Config["id_use"])
    if not config then
        Log.Error("###### Excel config id_use nil", self.m_Id)
        return
    end
    local currentLevel = level or self.m_Lv
    local exp = GetValueFromStrData(config["exp"], currentLevel)
    return v2n(exp)
end

--- 弹出建筑升级界面
--- @param isShowCastleUpgrade boolean 是否打开城堡升级界面
--- @param level number|nil 当前等级
--- @param callback function|nil 关闭界面后回调
function M:ShowBuildLvUP(isShowCastleUpgrade, level, callback)
    local param = {}
    param.lv = level or self.m_Lv
    param.id = self.m_Id
    UI_SHOW(UIDefine.UI_BuildLvUP, param, self, callback, isShowCastleUpgrade,self.m_Config["id_use"])
end

return M
local Panel_MyUnion_Member = Class()
local M=Panel_MyUnion_Member

local ItemBase = require("UI.Common.BaseSlideItem")
local joinItem = Class(ItemBase)

function M:OnInit()

end

function M:OnCreate(baseUI,goPanel,param)
    self.baseUI = baseUI
    self.goPanel = goPanel
	self.ui = baseUI.ui
    self.uiGameObject = goPanel
    
    --添加事件处理
    
	if param ~= nil then
		self.sliderData = param
	else

	end

	if self.slider == nil then
		self.slider = require("UI.Common.SlideRect").new()
		self.slider:Init(GetChild(self.ui.m_goJoinScroll,"ViewPort",UEUI.ScrollRect),2)
		local itemTrans = self.ui.m_goItemJoin.transform
		local joinItems = {}
		for i=1,6 do
			joinItems[i] = joinItem.new()
			joinItems[i]:Init(UEGO.Instantiate(itemTrans))
			joinItems[i]:SetParentObj(self.uiGameObject)
		end
		self.itemlist = joinItems
		self.slider:SetItems(joinItems,2,Vector2.New(5,10))
		self:RefreshMyUnionList()


		EventMgr:Add(EventID.UI_FRIEND_CLOSE,self.FriendClose,self)
		EventMgr:Add(EventID.REFRESH_UNION_LIST,self.RefreshMyUnionList,self)
		EventMgr:Add(EventID.REFRESH_APPLY_REDPOINT,self.RefreshApplyRedpoint,self)

	end
end

function M:OnRefresh(type,param)
	if type == 1 then
		self:RefreshMyUnionList()
	end
end

function M:RefreshMyUnionApplyList()
	local funBack = function(apply_list)
		EventMgr:Dispatch(EventID.REFRESH_APPLY_REDPOINT)
	end
	LeagueManager:SendLeagueApplyList(funBack)
end

function M:RefreshApplyRedpoint()
	local apply_list = LeagueManager:GetMyLeagueApplyList()
	SetActive(self.ui.m_goAcceptPoint,GetTableLength(apply_list) > 0)
end

function M:RefreshMyUnionList(sortIndex)
	local function joinBack(_data)
		local data = DeepCopy(_data.memberList)
		table.sort(data, function(a, b)
				if v2n(a.leagueDuty) ~= v2n(b.leagueDuty) then
					return v2n(a.leagueDuty) < v2n(b.leagueDuty)
				else
					return v2n(a.level) > v2n(b.level)
				end
			end)
		for k, v in pairs(data) do
			v.sortIndex = k
		end
		self.sliderData = data
		if self.slider then
			self.slider:SetData(self.sliderData,sortIndex)
		end

		local myDuty = LeagueManager:GetMyLeagueDuty()
		if myDuty ~= -1 then
			local pos_config = LeagueManager:GetUnionPosById(myDuty)
			if self.ui then
				SetActive(self.ui.m_btnAccept,pos_config.add == 1)
				SetActive(self.ui.m_btnSetting,pos_config.edit == 1)
			end
			if pos_config.add == 1 then
				self:RefreshMyUnionApplyList()
			end
		end
		LeagueManager:SetLeaguePic(_data.pic)
		if self.ui then
			SetActive(self.ui.m_btnAbout.gameObject,true)
			self.ui.m_txtCount.text = _data.members .. "/" .. _data.membersMax
		end
	end
	LeagueManager:SendGetLeagueDetails(nil,joinBack)
end

function M:onDestroy()
	EventMgr:Remove(EventID.UI_FRIEND_CLOSE,self.FriendClose,self)
	EventMgr:Remove(EventID.REFRESH_UNION_LIST,self.FriendClose,self)
	EventMgr:Remove(EventID.REFRESH_APPLY_REDPOINT,self.RefreshApplyRedpoint,self)
	for i=1,#self.itemlist do
		self.itemlist[i]:onDestroy()
	end
	self.slider = nil
end

function M:FriendClose()
	self.baseUI:Close()
	--UIMgr:Close(UIDefine.UI_MyUnion)
end
function M:onUIEventClick(go,param)
	local name = go.name
	if name == "m_btnAccept" then
		UI_SHOW(UIDefine.UI_ApplyLeagueView)
	elseif name == "m_btnSetting" then
		UI_SHOW(UIDefine.UI_LeagueEditPanel,LeagueManager.myleagueDetail)
	elseif name == "m_btnAbout" then
		local myLeagueID = LeagueManager:GetMyLeagueId()
		UI_SHOW(UIDefine.UI_LeagueDetail,myLeagueID)
	end
end

--------------------------

function joinItem:OnInit(transform)
	self.transform = transform.transform
end

function joinItem:UpdateData(data,index)
	--------base------------
	--self.data = data
	local txt_lang = self.transform:Find("txt_lang"):GetComponent(typeof(UEUI.Text))
	txt_lang.text = FriendManager:GetLanguage(data)

	local txt_name = self.transform:Find("txt_name"):GetComponent(typeof(UEUI.Text))
	txt_name.text = data.name

	local txt_rank = self.transform:Find("rank/txt_rank"):GetComponent(typeof(UEUI.Text))
	txt_rank.text = data.sortIndex

	local img_rank = self.transform:Find("rank/img_rank"):GetComponent(typeof(UEUI.Image))
	if data.sortIndex <= 3 then
		local sprite = "Assets/ResPackage/Sprite/ui_friend/haoyou_win1_paihang"..data.sortIndex..".png"
		SetActive(img_rank.gameObject,true)
		SetImageSprite(img_rank,sprite,false)
	else
		SetActive(img_rank.gameObject,false)
	end

	local txt_online = self.transform:Find("txt_online"):GetComponent(typeof(UEUI.Text))
	data.lastApi = data.lastUpdate
	local isOnLine,str = FriendManager:IsOnLine(data)
	txt_online.text = str

	local head = self.transform:Find("head/img_head"):GetComponent(typeof(UEUI.Image))
	local HeadIcon = FriendManager:GetHeadIcon(data.icon)
	if HeadIcon then
		SetImageSprite(head,HeadIcon,false)
	end

	local txt_level = self.transform:Find("head/txt_level"):GetComponent(typeof(UEUI.Text))
	txt_level.text = data.level

	local name2 = self.transform:Find("txt_name2"):GetComponent(typeof(UEUI.Text))
	local duty,path = LeagueManager:GetDutyByType(data.leagueDuty)
	name2.text = duty

	local duty = self.transform:Find("duty"):GetComponent(typeof(UEUI.Image))
	if path then
		SetActive(duty.gameObject,true)
		SetUIImage(duty,path,false)
	else
		SetActive(duty.gameObject,false)
	end

	local bg2 = self.transform:Find("bg2")
	local txt_help = self.transform:Find("bg2/txt_help"):GetComponent(typeof(UEUI.Text))
	if data.weekHelp then
		SetActive(bg2,true)
		txt_help.text = data.weekHelp
	else
		SetActive(bg2,false)
	end

	--txt_help.text = 0

	local imageBg = self.transform:Find("bg"):GetComponent(typeof(UEUI.Image))
	if v2n(data.id) == NetUpdatePlayerData:GetPlayerInfo().id then
		SetUIImage(imageBg,"Sprite/ui_friend/haoyou_win1_liebiao2.png",false)
	else
		SetUIImage(imageBg,"Sprite/ui_friend/haoyou_win1_liebiao.png",false)
	end

	local click_go = self.transform:Find("btn_click")
	local btn_click = click_go:GetComponent(typeof(UEUI.Button))
	RemoveUIComponentEventCallback(btn_click, UEUI.Button)
	AddUIComponentEventCallback(btn_click, UEUI.Button, function(arg1,arg2)
			local playerId = NetUpdatePlayerData:GetPlayerID()
			local notSelf = playerId ~= v2n(data.id)
			if notSelf then
				self:LoadTips(click_go,data)
			end
		end)
end


function joinItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function joinItem:SetParentObj(obj)
	self.parentGo = obj
end

function joinItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

function joinItem:LoadTips(go,memberData)

	if self.tipGo == nil then
		self.tipGo = FineFromParent(self.parentGo, "LeaguePostTip")
		if nil == self.tipGo  then
			local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "LeaguePostTip")
			ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant, function(cellObj)
				self.tipGo = UEGO.Instantiate(cellObj)
				self.tipGo.transform:SetParent(self.parentGo.transform)
				self.tipGo.transform.localPosition = Vector3.New(0, 0, 0)
				self.tipGo.transform.localScale = Vector3.New(1, 1, 1)
				self.tipGo.name = "LeaguePostTip"
				InitTextLanguage(self.tipGo)
				self:ShowTips(go, memberData);
			end)
		else
			self:ShowTips(go, memberData);
		end
	end
end

function joinItem:ShowTips(go, memberData)
	local newpos = self.parentGo.transform:InverseTransformPoint(go.transform.position)
	local tipsBg = self.tipGo.transform:Find("tipsbg")
	tipsBg.transform.localPosition = newpos
	tipsBg.transform.localScale = Vector3.New(1,1,1)

	local btn_job = self.tipGo.transform:Find("tipsbg/p/btn_job")
	local btn_transfer = self.tipGo.transform:Find("tipsbg/p/btn_transfer")
	local btn_expel = self.tipGo.transform:Find("tipsbg/p/btn_expel")
	--local btn_dismiss = self.tipGo.transform:Find("tipsbg/p/btn_dismiss")
	local btn_impeachment = self.tipGo.transform:Find("tipsbg/p/btn_impeachment")
	local job_txt = self.tipGo.transform:Find("tipsbg/p/btn_job/m_txtBtnJob"):GetComponent(typeof(UEUI.Text))
	local langId = memberData.leagueDuty > 2 and 9242 or 9243
	job_txt.text = LangMgr:GetLang(langId)

	local myDuty = LeagueManager:GetMyLeagueDuty()
	if myDuty == -1 then
		return
	end
	local pos_config = LeagueManager:GetUnionPosById(myDuty)
	local leaderID = LeagueManager:GetMyLeagueLeaderID()
	local playerId = NetUpdatePlayerData:GetPlayerID()
	local notSelf = playerId ~= v2n(memberData.id)

	SetActive(btn_job, pos_config.job == 1 and notSelf)
	SetActive(btn_transfer, pos_config.transfer == 1 and notSelf)
	SetActive(btn_expel, pos_config.expel == 1 and notSelf and myDuty < memberData.leagueDuty)
	--SetActive(btn_dismiss, pos_config.dismiss == 1)
	SetActive(btn_impeachment, pos_config.impeachment == 1 and v2n(memberData.id) == v2n(leaderID) and playerId ~= leaderID)

	RemoveUIComponentEventCallback(self.tipGo, UEUI.Button)
	AddUIComponentEventCallback(self.tipGo, UEUI.Button, function(arg1,arg2)
			local name = arg1.name
			if self.tipGo.gameObject.activeSelf then
				SetActive(self.tipGo , false)
			end

			if name == "btn_detail" then
				FriendManager:ShowPlayerById(v2n(memberData.id))
			elseif name == "btn_job" then
				local is_up = memberData.leagueDuty > 2 and 1 or 0
				local can_up = LeagueManager:GetIsCanUpDuty(memberData.leagueDuty,is_up)
				if not can_up then
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9269))
					return
				end
				local function LeagueDutyCallBack()
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
				end
				LeagueManager:SendLeagueDuty(memberData.id,is_up,LeagueDutyCallBack) --提拔降级
			elseif name == "btn_transfer" then
				local function TransferCallBack()
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
				end
				local function onTransfer()
					LeagueManager:SendLeagueTransfer(memberData.id,TransferCallBack) --转让
				end
				UI_SHOW(UIDefine.UI_TipsTop, 9248, onTransfer)
			elseif name == "btn_expel" then
				local function KickCallBack()
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
				end
				if v2n(myDuty) < v2n(memberData.leagueDuty) then
					LeagueManager:SendLeagueKick(memberData.id,memberData.sortIndex,KickCallBack) --踢出联盟
				else
					UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9251))
				end
			elseif name == "btn_impeachment" then
				local lastApi = memberData.lastUpdate
				local nowTime = TimeMgr:GetServerTimestamp()
				local time = nowTime - v2n(lastApi)
				local leftTime = 3 * OneDaySeconds
				if time < leftTime then
					local hour = math.floor( leftTime / 3600)
					local tips = string.format(LangMgr:GetLang(9250),hour)
					UI_SHOW(UIDefine.UI_WidgetTip, tips)
				else
					local function ImpeachCallBack()
						UI_UPDATE(UIDefine.UI_MyUnion,1)
						UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
					end
					LeagueManager:SendLeagueImpeach(ImpeachCallBack) -- 弹劾
				end
			end

		end)


	SetActive(self.tipGo.transform, true)
end

function joinItem:onDestroy()
	self.tipGo = nil
end



return M
-- 竞技达人活动主界面

local UI_AthleticTalentView = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local ScoreItem = Class(ItemBase)
local RewardItem = Class(ItemBase)
local ColorUtility = UE.ColorUtility
local GoGiftItem = require("UI.GoGiftItem")

local DayTagSprite = {
    ["1_1"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind1_1.png",
    ["1_2"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind1_2.png",
    ["1_3"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind1_3.png",
    ["2_1"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind2_1.png",
    ["2_2"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind2_2.png",
    ["2_3"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind2_3.png",
    ["3_1"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind3_1.png",
    ["3_2"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind3_2.png",
    ["3_3"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind3_3.png",
}

local SettlementNameColor = {
    [1] = "b34800",
    [2] = "42589d",
    [3] = "913a14"
}

function UI_AthleticTalentView:OnInit()
    
end

function UI_AthleticTalentView:OnCreate(param)
    CreateCommonHead(GetChild(self.ui.m_goSettlement, "podium/player/headNode",UE.Transform))
    
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
    self.activityItem = activityItem
    if not activityItem then return end
    NetAthleticTalentData:CheckDayChange(activityItem.info.activeId)

    self.dayTagList = {}
    self:InitPanel()

    self.currentDay = NetAthleticTalentData:GetDataByKey("currentDay")

    self.ui.m_txtRankToday.text = LangMgr:GetLang(9056)
    self.ui.m_txtRankTotal.text = LangMgr:GetLang(9056)

    self.openAnim = GetComponent(self.uiGameObject, UE.Animation)
    if 1 <= self.currentDay and self.currentDay <= 6 then
        PlayAnimStatusIndex(self.openAnim, "athletictalent_open")
    elseif self.currentDay == 7 then
        PlayAnimStatusIndex(self.openAnim, "athletictalent_settlement")
    end

    self:RefreshRankText()
    UIMgr:RefreshAllMainFace(47, nil, true)
    Log.Info("活动 id", activityItem.info.activeId)

    --直购礼包入口
    local function callBack(obj)
        self.directBuyGo = obj
        self.directBuyGo:SetTitleOutline("0069b3")
        self.directBuyGo:SetTitleBg("Sprite/ui_huodongjingsai/jingji_lihe.png", false)
    end
    GoGiftItem:Create(self.ui.m_transDirectBuy,ActivityTotal.AthleticTalent,nil,callBack)

    self:SetIsUpdateTick(true)

    -- 前六天才有新手引导
    if 1 <= self.currentDay and self.currentDay <= 6 then
        local isGuide = NetGlobalData:GetActivityGuideCache(ActivityTotal.AthleticTalent)
        if not isGuide then
            NetGlobalData:SetActivityGuideCache(ActivityTotal.AthleticTalent)
            self:Guide()
        end
    end
    
end

function UI_AthleticTalentView:OnRefresh(type, rank)
    local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
    -- 刷新日排行
    if type == 1 then
        if rank and isEnterRank then
            self.ui.m_txtRankToday.text = tostring(rank)
        else
            self.ui.m_txtRankToday.text = LangMgr:GetLang(9056)
        end
    -- 刷新周排行
    elseif type == 2 then
        if rank and isEnterRank then
            self.ui.m_txtRankTotal.text = tostring(rank)
        else
            self.ui.m_txtRankTotal.text = LangMgr:GetLang(9056)
        end
    -- 刷新界面
    elseif type == 3 then
        self:RefreshPanel()
        self:RefreshDayTag()
        self:RefreshRankText()
    -- 刷新奖励滚动视图
    elseif type == 4 then
        self:RefreshScroll()
        self:TurnPageScroll()
    -- 积分奖励滚动视图定位到可领奖位置
    elseif type == 5 then
        self:TurnPageScroll()
    end
end

function UI_AthleticTalentView:onDestroy()
    Tween.Kill("AutoMoveFunc")
    self:SetIsUpdateTick(false)
    self.directBuyGo = nil
    if self.scoreItemList then
        for i = 1, #self.scoreItemList do
            self.scoreItemList[i]:onDestroy()
        end
    end
    if self.rewardItemList then
        for i = 1, #self.rewardItemList do
            self.rewardItemList[i]:onDestroy()
        end
    end
    self.scoreItemList = nil
    self.scoreSlideRect = nil
    self.rewardItemList = nil
    self.rewardSlideRect = nil
    self.activityItem = nil
    self.currentDay = nil
    self.dayTagList = nil
end

function UI_AthleticTalentView:onUIEventClick(go,param)
    local name = go.name
    -- 打开帮助界面
    if name == "m_btnHelp" then
        UI_SHOW(UIDefine.UI_AthleticTalentHelp)
    elseif name == "m_btnGoBack" then
        self:Close()
    elseif name == "m_btnRank" then
        self:OpenRank()
    elseif name == "btn_CheckRank" then
        self:OpenRank()
    elseif name == "m_btnTaskTip" then
        UI_SHOW(UIDefine.UI_AthleticTalentTaskTip)
    end
end

function UI_AthleticTalentView:TickUI(deltaTime)
    local time = self:GetActiveTime()
    if time and time > 0 then
        self.ui.m_txtTime.text = TimeMgr:BaseTime(time, 2, 2)
    else
        self.ui.m_txtTime.text = LangMgr:GetLang(7077)
    end

    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    if activityItem then
        local endTime = AthleticTalentManager:GetEndTimeByDay(currentDay)
        if endTime then
            local remainTime = endTime - TimeZoneMgr:GetServerStampWithServerZone()
            self.ui.m_txtDailyTime.text = TimeMgr:BaseTime(remainTime, 2, 2)
        end
    end

    if self.currentDay ~= currentDay then
        self.currentDay = currentDay
        self:RefreshPanel()
        self:RefreshScroll()
        self:TurnPageScroll()
        self:RefreshDayTag()
        self:RefreshRankText()
        UIMgr:RefreshAllMainFace(47, nil, false)
        UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
        AthleticTalentManager:ClearEndTime()
    end
end

--- 获取活动剩余时间
--- @return integer time 剩余时间
function UI_AthleticTalentView:GetActiveTime()
    if not self.activityItem then return 0 end
    local condition = self.activityItem.info.state
    local time = 0
    if condition == 1 then
        time = self.activityItem:GetRemainingTime()
    elseif condition == 3 then
        time = self.activityItem:GetStartRemainingTime()
    elseif condition == 4 then
        time = self.activityItem:GetWaitTime()
    end
    return time
end

--- 初始化界面
function UI_AthleticTalentView:InitPanel()
    --self:GenerateScore()
    self:GenerateReward()
    self:GenerateDayToggleList()
    self:RefreshPanel()
    self:TurnPageScroll()
end

--- 刷新界面
function UI_AthleticTalentView:RefreshPanel()
    -- 刷新分数
    local score = NetAthleticTalentData:GetDailyScore()
    self.ui.m_txtMyScore.text = tostring(score)
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    if 1<= currentDay and currentDay <= 6 then
        -- 刷新主题
        local topic = AthleticTalentManager:GetSettingConfig(currentDay + 13)
        if topic then
            self.ui.m_txtTopic.text = LangMgr:GetLang(topic.value)
            local txtTopicRT = GetComponent(self.ui.m_txtTopic, UE.RectTransform)
            UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(txtTopicRT)
            local width = txtTopicRT.rect.width
            local maxWidth = self.ui.m_rtransTopic.rect.width
            if width >= maxWidth then
                local contentSizeFitter = GetComponent(self.ui.m_txtTopic, UEUI.ContentSizeFitter)
                contentSizeFitter.enabled = false
                SetUISize(self.ui.m_txtTopic, maxWidth, txtTopicRT.rect.height)
            else
                local contentSizeFitter = GetComponent(self.ui.m_txtTopic, UEUI.ContentSizeFitter)
                contentSizeFitter.enabled = true
            end
        end
        -- 刷新 banner 图
        local banner = AthleticTalentManager:GetSettingConfig(currentDay + 7)
        if banner then
            SetUIImage(self.ui.m_imgBanner, banner.value, false)
            SetUIImage(self.ui.m_imgBanner2, banner.value_2, false)
        end
        SetActive(self.ui.m_goDaily, true)
        SetActive(self.ui.m_goSettlement, false)
        SetActive(self.ui.m_goNoRank, false)
    elseif currentDay >= 7 then
        SetActive(self.ui.m_goDaily, false)
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if isEnterRank then
            SetActive(self.ui.m_goSettlement, true)
            SetActive(self.ui.m_goNoRank, false)
        else
            SetActive(self.ui.m_goSettlement, false)
            SetActive(self.ui.m_goNoRank, true)
        end
        self:ShowSettlement()
    end
    UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
end

--- 刷新天数页签
function UI_AthleticTalentView:RefreshDayTag()
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    for index, value in ipairs(self.dayTagList) do
        if value.day < currentDay then
            SetActive(value.normal, true)
            SetActive(value.selected, false)
            SetActive(value.disable, false)
        elseif value.day == currentDay then
            SetActive(value.normal, false)
            SetActive(value.selected, true)
            SetActive(value.disable, false)
        elseif value.day > currentDay then
            SetActive(value.normal, false)
            SetActive(value.selected, false)
            SetActive(value.disable, true)
        end
    end
end

--- 刷新积分和奖励的滚动视图
function UI_AthleticTalentView:RefreshScroll()
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local scoreData = AthleticTalentManager:GetScoreConfigByDay(currentDay)
    --if scoreData then
    --    self.scoreSlideRect:SetData(scoreData)
    --end
    local rewardData = AthleticTalentManager:GetRewardConfigByDay(currentDay)
    if rewardData then
        self.rewardSlideRect:SetData(rewardData)
    end
    UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
end

--- 生成天数勾选框列表
function UI_AthleticTalentView:GenerateDayToggleList()
    local toggleCount = 7

    for i = 1, toggleCount, 1 do
        local go = UEGO.Instantiate(self.ui.m_goTog.transform)
        go.transform:SetParent(self.ui.m_goParentTog.transform)
        go.transform.localScale = Vector3.New(1, 1, 1)
        local goRT = go:GetComponent(typeof(UE.RectTransform))
        local goNormal = GetChild(go, "Normal")
        local goSelected = GetChild(go, "Selected")
        local goDisable = GetChild(go, "Disable")
        local imgNormal = GetChild(go, "Normal", UEUI.Image)
        local imgSelected = GetChild(go, "Selected", UEUI.Image)
        local imgDisable = GetChild(go, "Disable", UEUI.Image)
        local togNameNormal = GetChild(go, "Normal/togName", UEUI.Text)
        local togNameSelected = GetChild(go, "Selected/togName", UEUI.Text)
        local togNameDisable = GetChild(go, "Disable/togName", UEUI.Text)
        if 1 <= i and i <= 6 then
            togNameNormal.text = tostring(i)
            togNameSelected.text = tostring(i)
            togNameDisable.text = tostring(i)
        elseif i == 7 then
            SetActive(togNameNormal, false)
            SetActive(togNameSelected, false)
            SetActive(togNameDisable, false)
        end
        SetActive(go, true)
        local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
        local day = i
        local imgIndex = day
        if 2 <= imgIndex and imgIndex <= 6 then imgIndex = 2 end
        if imgIndex == 7 then imgIndex = 3 end
        local imgKeyNormal = imgIndex .. "_1"
        local imgKeySelected = imgIndex .. "_2"
        local imgKeyDisable = imgIndex .. "_3"
        SetUIImage(imgNormal, DayTagSprite[imgKeyNormal], true)
        SetUIImage(imgSelected, DayTagSprite[imgKeySelected], true)
        SetUIImage(imgDisable, DayTagSprite[imgKeyDisable], true)
        local button = go:GetComponent(typeof(UEUI.Button))
        button.onClick:AddListener(function ()
            if 1<= day and day <= 6 then
                UI_SHOW(UIDefine.UI_AthleticTalentPointTips, day)
            end
        end)
        if day < currentDay then
            --goRT.sizeDelta = Vector2.New(150, 95)
            SetActive(goNormal, true)
            SetActive(goSelected, false)
            SetActive(goDisable, false)
        elseif day == currentDay then
            SetActive(goNormal, false)
            SetActive(goSelected, true)
            SetActive(goDisable, false)
            --goRT.sizeDelta = Vector2.New(166, 95)
            local scoreData = AthleticTalentManager:GetScoreConfigByDay(day)
            --if scoreData then
            --    self.scoreSlideRect:SetData(scoreData)
            --end
            local rewardData = AthleticTalentManager:GetRewardConfigByDay(day)
            if rewardData then
                self.rewardSlideRect:SetData(rewardData)
            end
            local score = NetAthleticTalentData:GetDailyScore()
            self.ui.m_txtMyScore.text = string.format("%s %d", LangMgr:GetLang(1000000), score)
            self:SetDirectGiftActive(day)
            self.ui.m_goArrow.transform:SetParent(goSelected.transform)
            self.ui.m_goArrow.transform.localPosition = Vector3.New(0,-80,0)
        elseif day > currentDay then
            --goRT.sizeDelta = Vector2.New(150, 95)
            SetActive(goNormal, false)
            SetActive(goSelected, false)
            SetActive(goDisable, true)
        end
        local dayTag = {
            day = day,
            normal = goNormal,
            selected = goSelected,
            disable = goDisable
        }
        table.insert(self.dayTagList, dayTag)
    end
end

function UI_AthleticTalentView:GenerateScore()
    self.scoreSlideRect = SlideRect.new()
	self.scoreSlideRect:Init(self.ui.m_scrollviewScore, 2)
    local item = self.ui.m_goScoreItem.transform
    local scoreItemList = {}
    for i = 1, 16, 1 do
        scoreItemList[i] = ScoreItem.new()
        scoreItemList[i]:Init(UEGO.Instantiate(item))
    end
    self.scoreSlideRect:SetItems(scoreItemList, 0, Vector2.New(0, 0))
    self.scoreItemList = scoreItemList

    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local scoreData = AthleticTalentManager:GetScoreConfigByDay(currentDay)
    if scoreData then
        self.scoreSlideRect:SetData(scoreData)
    end
end

function ScoreItem:OnInit(transform)
    self.transform = transform.transform
    self.bg1 = GetChild(transform, "bg1")
    self.bg2 = GetChild(transform, "bg2")
    self.textDescribe = GetChild(transform, "describe", UEUI.Text)
    self.textPoints = GetChild(transform, "points", UEUI.Text)
    self.icon = GetChild(transform, "Image", UEUI.Image)
end

function ScoreItem:UpdateData(data, index)
    self.data = data
    local showBg1 = index % 2 == 1
    SetActive(self.bg1, showBg1)
    SetActive(self.bg2, not showBg1)
    self.textDescribe.text = LangMgr:GetLang(data.lang_id)
    self.textPoints.text = "+" .. data.score
    -- SetUIImage(self.icon, data.icon, false)
end

function ScoreItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function ScoreItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function ScoreItem:onDestroy()
    UEGO.Destroy(self.transform.gameObject)
    self.transform = nil
    self.bg1 = nil
    self.bg2 = nil
    self.textDescribe = nil
    self.textPoints = nil
    self.icon = nil
end

function UI_AthleticTalentView:GenerateReward()
    self.rewardSlideRect = SlideRect.new()
    self.rewardSlideRect:Init(self.ui.m_scrollviewReward, 2)
    local item = self.ui.m_goRewardItem.transform
    local rewardItemList = {}
    for i = 1, 6, 1 do
        rewardItemList[i] = RewardItem.new()
        rewardItemList[i]:Init(UEGO.Instantiate(item))
    end
    self.rewardSlideRect:SetItems(rewardItemList, 0, Vector2.New(8, 0))
    self.rewardItemList = rewardItemList

    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local rewardData = AthleticTalentManager:GetRewardConfigByDay(currentDay)
    if rewardData then
        self.rewardSlideRect:SetData(rewardData)
    end
end

function RewardItem:OnInit(transform)
    self.transform = transform.transform
    self.textScore = GetChild(transform, "AnimObj/m_txtName", UEUI.Text)
    self.outlineScore = GetChild(transform, "AnimObj/m_txtName", UEUI.Outline)
    self.txtGetReward = GetChild(transform, "AnimObj/btnGetReward/txtGetReward", UEUI.Text)
    self.txtUnlock = GetChild(transform, "AnimObj/btnUnlock/txtUnlock", UEUI.Text)
    self.btnGetReward = GetChild(transform, "AnimObj/btnGetReward", UEUI.Button)
    self.btnUnlock = GetChild(transform, "AnimObj/btnUnlock", UEUI.Button)
    self.redImg = GetChild(transform, "AnimObj/btnGetReward/redImg")
    self.hasGet = GetChild(transform, "AnimObj/hasGet")
    self.rewardList = {}
    local parent = GetChild(transform, "rewardList")
    if parent then
        local childCount = parent.transform.childCount
        for i = 1, childCount, 1 do
            local item = {}
            item.itemReward = GetChild(parent, "itemReward" .. i)
            item.itemButton = GetComponent(item.itemReward, UEUI.Button)
            item.icon = GetChild(item.itemReward, "icon", UEUI.Image)
            item.rewardnum = GetChild(item.itemReward, "rewardnum", UEUI.Text)
            table.insert(self.rewardList, item)
        end
    end
end

function RewardItem:UpdateData(data, index)
    self.data = data
    local currentScore = NetAthleticTalentData:GetDailyScore()
    self.textScore.text = string.format("%d / %d", currentScore, data.score)
    -- 分数达标
    if currentScore >= data.score then
        self.textScore.text = string.format("%d / %d", data.score, data.score)--string.format("<color=\"#%s\">%d</color> / %d", "12FF00", data.score, data.score)
        --self.textScore.color = self:GetColorByHex("12FF00")
        local rewardState = NetAthleticTalentData:GetRewardState(data.score)
        -- 已领过
        if rewardState then
            SetActive(self.btnGetReward, false)
            SetActive(self.btnUnlock, false)
            SetActive(self.hasGet, true)
            SetActive(self.redImg, false)
            self.txtGetReward.text = LangMgr:GetLang(71)
            self.txtUnlock.text = LangMgr:GetLang(71)
        -- 未领过
        else
            SetActive(self.btnGetReward, true)
            SetActive(self.btnUnlock, false)
            SetActive(self.hasGet, false)
            SetActive(self.redImg, true)
            self.txtGetReward.text = LangMgr:GetLang(17)
            self.txtUnlock.text = LangMgr:GetLang(17)
        end
    -- 分数未达标
    else
        self.textScore.text = string.format("%d / %d", currentScore, data.score)--string.format("<color=\"#%s\">%d</color> / %d", "FF0000", currentScore, data.score)
        --self.textScore.color = self:GetColorByHex("FFFFFF")
        SetActive(self.btnGetReward, false)
        SetActive(self.btnUnlock, true)
        SetActive(self.hasGet, false)
        SetActive(self.redImg, false)
        self.txtGetReward.text = LangMgr:GetLang(17)
        self.txtUnlock.text = LangMgr:GetLang(17)
    end
    for _, value in ipairs(self.rewardList) do
        SetActive(value.itemReward, false)
    end
    local rewardStr = NetSeasonActivity:GetChangeItemId(data.reward)
    local rewardList = string.split(rewardStr, ";")
    for key, value in ipairs(rewardList) do
        local rewardTable = string.split(value, "|")
        local itemID = v2n(rewardTable[1])
        local itemNum = v2n(rewardTable[2])
        SetUIImage(self.rewardList[key].icon, ItemConfig:GetIcon(itemID), false)
        self.rewardList[key].rewardnum.text = "x" .. itemNum
        SetActive(self.rewardList[key].itemReward, true)
        self.rewardList[key].itemButton.onClick:RemoveAllListeners()
        self.rewardList[key].itemButton.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_ItemTips, itemID)
        end)
    end
    self.btnGetReward.onClick:RemoveAllListeners()
    self.btnGetReward.onClick:AddListener(function ()
        SetActive(self.btnGetReward, false)
        SetActive(self.btnUnlock, false)
        SetActive(self.hasGet, true)

        self.txtGetReward.text = LangMgr:GetLang(71)
        self.txtUnlock.text = LangMgr:GetLang(71)
        local rewardState = NetAthleticTalentData:GetRewardState(data.score)
        if not rewardState then
            -- 发放奖励
            NetGlobalData:GetRewardToMap(rewardStr, "UI_AthleticTalentView", true)
        end
        -- 标记已领奖
        NetAthleticTalentData:SetRewardState(data.score)
        -- 显示奖励界面
        local param = {}
        param.type = 13
        param.rewards = rewardStr
        UI_SHOW(UIDefine.UI_Recharge, param, function ()
            UI_UPDATE(UIDefine.UI_AthleticTalentView, 5)
        end)
        UIMgr:RefreshAllMainFace(47, nil, false)
        UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
    end)
end

function RewardItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function RewardItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function RewardItem:onDestroy()
    UEGO.Destroy(self.transform.gameObject)
    self.transform = nil
    self.textScore = nil
    self.outlineScore = nil
    self.txtGetReward = nil
    self.txtUnlock = nil
    self.btnGetReward = nil
    self.btnUnlock = nil
    self.hasGet = nil
    self.rewardList = nil
end

function RewardItem:GetColorByHex(hex)
    local _, color = ColorUtility.TryParseHtmlString("#" .. hex, nil)
    return color
end

--控制直购礼包入口显隐
function UI_AthleticTalentView:SetDirectGiftActive(day)
    SetActive(self.ui.m_transDirectBuy,NetAthleticTalentData:GetStatus(day) == 0)
end

--- 滑动到可领奖位置
function UI_AthleticTalentView:TurnPageScroll()
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local rewardData = AthleticTalentManager:GetRewardConfigByDay(currentDay)
    if not rewardData then return end
    -- 当前领奖进度
    local rewardIndex = 0
    for index, value in ipairs(rewardData) do
        local rewardState = NetAthleticTalentData:GetRewardState(value.score)
        if rewardState then
            rewardIndex = index
        else
            break
        end
    end
    if rewardIndex == 0 then return end
    -- 检查边界，超出边界不会自动滑
    local moveToIndex = math.min(rewardIndex + 1, #rewardData)
    self.rewardSlideRect:MoveToIndex(moveToIndex, 2)
end

function UI_AthleticTalentView:OpenRank()
    local isShowDayTag = true
    local function rankCallBack(data)
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            UI_SHOW(UIDefine.UI_AthleticTalentRank, data, isShowDayTag)
        -- 未上榜
        else
            UI_SHOW(UIDefine.UI_AthleticTalentRank, nil, isShowDayTag, false, nil, true)
        end
    end
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    if 1 <= currentDay and currentDay <= 6 then
        NetAthleticTalentData:RequestRankData(rankCallBack, currentDay)
    elseif currentDay == 7 then
        isShowDayTag = false
        NetAthleticTalentData:RequestRankData(rankCallBack)
    end
    NetAthleticTalentData:CheckRankList()
end

function UI_AthleticTalentView:RefreshRankText()
    local function dayRankCallBack(data)
        if not self.ui then return end
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            self.ui.m_txtRankToday.text = tostring(data.player.rank)
            SetActive(self.ui.m_goRankToday, true)
        -- 未上榜
        else
            self.ui.m_txtRankToday.text = LangMgr:GetLang(9056)
        end
    end
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    if 1 <= currentDay and currentDay <= 6 then
        NetAthleticTalentData:RequestRankData(dayRankCallBack, currentDay, true)
    else
        self.ui.m_txtRankToday.text = LangMgr:GetLang(9056)
    end

    local function totalRankCallBack(data)
        if not self.ui then return end
        -- 已上榜
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        if data.player.rank and isEnterRank then
            self.ui.m_txtRankTotal.text = tostring(data.player.rank)
            SetActive(self.ui.m_goRankTotal, true)
        -- 未上榜
        else
            self.ui.m_txtRankTotal.text = LangMgr:GetLang(9056)
        end
    end
    NetAthleticTalentData:RequestRankData(totalRankCallBack, nil, true)
end

--显示奖励结算
function UI_AthleticTalentView:ShowSettlement()
    local playerItem = GetChild(self.ui.m_goSettlement, "podium/player")
    SetActive(playerItem, false)
    NetAthleticTalentData:RequestRankData(function(data)
        if not self.ui then return end
        if not data or (not data.ranking) then
            return
        end
        local rankData = data.ranking;
        local settleList = {}
        for i = 1,3 do
            if rankData[i] then
                settleList[i] = {
                    index = rankData[i].rank;
                    name = rankData[i].name;
                    head = rankData[i].icon;
                    border = rankData[i].border;
                }
            end
        end
        for _,v in ipairs(settleList) do
            local pos = GetChild(self.ui.m_goSettlement, "podium/pos"..v.index, UE.Transform)
            local name = GetChild(self.ui.m_goSettlement, "podium/Text"..v.index, UEUI.Text)
            name.text = v.name
            name.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB(SettlementNameColor[v.index])
            name.gameObject:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow)).effectColor = Color.HexToRGB(SettlementNameColor[v.index])
            if pos.childCount then
                for i = 0, pos.childCount-1, 1 do
                    UEGO.Destroy(pos:GetChild(i).gameObject)
                end
            end
            local obj = UEGO.Instantiate(playerItem, pos)
            obj.transform.localPosition = Vector3.zero
            self:InitHeadInfo(obj,v)
            SetActive(obj, true)
        end
    end, nil, true)
end

--显示玩家头像信息
function UI_AthleticTalentView:InitHeadInfo(headObj,info)
    local icon = GetChild(headObj, "icon", UEUI.Image)--玩家头像
    --local name = GetChild(headObj, "name", UEUI.Text)--玩家名称
    local headData = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set,info.head)
    if headData then
        SetUIImage(icon,headData.icon,false)
    end

    local customHeadObj  = GetChild(headObj,"headNode/CustomHead")
    SetHeadAndBorderByGo(customHeadObj,info.head,info.border)
    customHeadObj.transform.localPosition = Vector3.zero
    
    --name.text = info.name
    if info.index == 1 then
        headObj.transform.localScale = Vector3.one*1.38
    end
end

--- 打开新手引导
function UI_AthleticTalentView:Guide()

    -- 最后一步引导回调
    local function GuideCallback4()
        UI_CLOSE(UIDefine.UI_GuideMask)
    end

    -- 引导积分达标奖励
    local function Guide4()
        local centerPos = self:ConvertToRectPos(self.ui.m_goGuide4)
        local rt = GetComponent(self.ui.m_goGuide4, typeof(UE.RectTransform))
        local width = rt.rect.width
        local height = rt.rect.height
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{3,width,height})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetBtnSize,{10,5})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-1.5,centerPos[2] / 100 + height / 100 / 2 + 2,2203116})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100 + 1, centerPos[2] / 100 + height / 100 / 2 + 0.6, 0, 0, 0})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
            GuideCallback4()
        end)
    end

    -- 引导积分获取途径
    local function Guide3()
        local centerPos = self:ConvertToRectPos(self.ui.m_goGuide3)
        local rt = GetComponent(self.ui.m_goGuide3, typeof(UE.RectTransform))
        local width = rt.rect.width
        local height = rt.rect.height
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{3,width,height})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetBtnSize,{5,5})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{3,centerPos[2] / 100 + height / 100 / 2 + 2,2203115})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100, centerPos[2] / 100 + height / 100 / 2 + 0.6, 0, 0, 0})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
            Guide4()
        end)
    end

    -- 关闭帮助界面，引导每日主题
    local function Guide2()
        local centerPos = self:ConvertToRectPos(self.ui.m_goGuide2)
        UI_SHOW(UIDefine.UI_GuideMask, {
            {3, 740, 222},             -- 遮罩类型和大小
            centerPos,                 -- 遮罩位置
            {5, 5},                    -- 遮罩按钮大小
            0.5,                       -- 缩放动画的时长
            function() Guide3() end,   -- 点击回调
            {centerPos[1] / 100, centerPos[2] / 100 + 1.6, 0, 0, 0},   -- 箭头位置
            {-1.5, 0, 2203114},                    -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
            nil,
        })
    end

    -- 第一步引导回调，打开帮助界面
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
        DOScale(self.ui.m_imgHelp.transform, 1, 0.5)
        UI_SHOW(UIDefine.UI_AthleticTalentHelp, Guide2)
    end

    -- 第一步引导帮助按钮
    local centerPos = self:ConvertToRectPos(self.ui.m_btnHelp)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 90},                -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {2, 2},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
        {1, 0, 2203113},                    -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
        nil,
    })
    DOScale(self.ui.m_imgHelp.transform, 1.5, 0.5)
end

--- 转换 UI 坐标
--- @param go any UI 节点
--- @return table 坐标表
function UI_AthleticTalentView:ConvertToRectPos(go)
    local cam = UIMgr:GetCamera()
    local screenPoint = UE.RectTransformUtility.WorldToScreenPoint(cam, go.transform.position)
    local _, pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.uiRectTransform,
        Vector2.New(screenPoint.x, screenPoint.y), cam)
    local posTable = {pos.x, pos.y}
    return posTable
end

return UI_AthleticTalentView
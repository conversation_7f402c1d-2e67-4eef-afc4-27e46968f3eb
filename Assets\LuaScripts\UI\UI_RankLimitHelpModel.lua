local UI_RankLimitHelpModel = {}

UI_RankLimitHelpModel.config = {["name"] = "UI_RankLimitHelp",["layer"] = UILayerType.Normal, ["type"] = UIType.Pop,
["isAutoClose"] = true, ["anim"] = 0, ["background"] = 1 }

function UI_RankLimitHelpModel:Init(c)
    c.ui = {}    
    c.ui.m_btnPrev = GetChild(c.uiGameObject,"root/bg/PageUI/m_btnPrev",UEUI.Button)
    c.ui.m_togG = GetChild(c.uiGameObject,"root/bg/PageUI/m_togG",UEUI.ToggleGroup)
    c.ui.m_goTog = GetChild(c.uiGameObject,"root/bg/PageUI/m_togG/m_goTog")
    c.ui.m_btnNext = GetChild(c.uiGameObject,"root/bg/PageUI/m_btnNext",UEUI.Button)
    c.ui.m_scrollview = GetChild(c.uiGameObject,"root/m_scrollview",UEUI.ScrollRect)
    c.ui.m_goPage1 = GetChild(c.uiGameObject,"root/m_scrollview/Mask/Content/m_goPage1")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_RankLimitHelpModel
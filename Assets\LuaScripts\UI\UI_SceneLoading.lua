local UI_SceneLoading = Class(BaseView)

function UI_SceneLoading:OnInit()
end

function UI_SceneLoading:OnCreate(type, text, callback)
    self.m_Type = type or 0
	if callback then
		callback()
	end
	AudioMgr:SetInLoading(true)

    self.m_SizeW = self.ui.m_imgProgress.rectTransform.sizeDelta.x

    self.ui.m_txtProgress.text = text
	self.ui.m_slider1.value = 0
    local function OnFished01(go)
        if go then
            self.ui.m_imgBack.texture = go
        end
    end
    local imgStr = "img/splash_bg"
    if (not MapControllerVisit:IsVisit() and NetUpdatePlayerData.playerInfo.curMap == MAP_ID_SECOND) then
        imgStr = "Texture/loading_bg/map2_loading_bg.png"
       ResMgr:LoadAssetAsync(imgStr, AssetDefine.LoadType.Texture2D, OnFished01)
    else
        ResMgr:LoadResourceTexture(imgStr, OnFished01)
    end

    local function OnFished02(go)
        if go then
            self.ui.m_imgTitle.texture = go
        end
    end

    ResMgr:LoadResourceTexture("img/splash_title", OnFished02)

    local function OnFished03(go)
        if go then
            self.ui.m_imgProgressBg.sprite = go
        end
    end

    ResMgr:LoadResourceSprite("img/progress_bg_update", OnFished03)

    local function OnFished04(go)
        if go then
            self.ui.m_imgProgress.sprite = go
        end
    end

    ResMgr:LoadResourceSprite("img/progress_fill_update", OnFished04)

    _G.CS.StartGame.Instance:SetUIVer(self.ui.m_txtVer)
    self.ui.m_txtVer.text = self.ui.m_txtVer.text .. "_" .. NetUpdatePlayerData:GetPlayerID()
    Game.vers = self.ui.m_txtVer.text
end

function UI_SceneLoading:OnRefresh(...)
    local params = { ... }
    if params[1] == 0 then
        self:SetProgress(params[2])
    end
end

function UI_SceneLoading:onDestroy()
    SceneMgr:GCCleanup()
end

function UI_SceneLoading:onUIEventClick(go, param)
    local name = go.name
	if name == "FAQ" then
		CS.UnityEngine.Application.OpenURL("https://www.facebook.com/mergetopia");
	end
end

function UI_SceneLoading:SetProgress(pro)
    self.ui.m_txtProgress.text = math.floor(pro * 100.0) .. "%"
    --self.ui.m_imgProgress.fillAmount = pro
	self.ui.m_slider1.value = pro
    --self.ui.m_imgSlider.rectTransform.anchoredPosition = Vector2.New(self.m_SizeW * pro, 0)
end

return UI_SceneLoading
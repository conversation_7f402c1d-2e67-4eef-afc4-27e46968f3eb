local UI_DailyTargetHelpModel = {}

UI_DailyTargetHelpModel.config = {["name"] = "UI_DailyTargetHelp", ["layer"] = UILayerType.Normal, ["type"] = UIType.Normal, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1,["onEscape"] = false}

function UI_DailyTargetHelpModel:Init(c)
    c.ui = {}    
    c.ui.m_transDesc = GetChild(c.uiGameObject,"root/bg/Context/m_transDesc",UE.Transform)
    c.ui.m_txtDesc = GetChild(c.uiGameObject,"root/bg/Context/m_transDesc/m_txtDesc",UEUI.Text)
    c.ui.m_btnPrev = GetChild(c.uiGameObject,"root/bg/PageUI/m_btnPrev",UEUI.Button)
    c.ui.m_togG = GetChild(c.uiGameObject,"root/bg/PageUI/m_togG",UEUI.ToggleGroup)
    c.ui.m_goTog = GetChild(c.uiGameObject,"root/bg/PageUI/m_togG/m_goTog")
    c.ui.m_btnNext = GetChild(c.uiGameObject,"root/bg/PageUI/m_btnNext",UEUI.Button)
    c.ui.m_scrollview = GetChild(c.uiGameObject,"root/m_scrollview",UEUI.ScrollRect)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_DailyTargetHelpModel
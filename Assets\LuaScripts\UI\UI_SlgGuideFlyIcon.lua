local UI_SlgGuideFlyIcon = Class(BaseView)

function UI_SlgGuideFlyIcon:OnInit()
    
end

function UI_SlgGuideFlyIcon:OnCreate(param)
   	self.flyfinishCnt = 0
	UIMgr.uiLockActivityRankTabPage = 6
	UIMgr:RefreshAllMainFace(49,nil,6)
end

function UI_SlgGuideFlyIcon:OnRefresh(param)
    
end

function UI_SlgGuideFlyIcon:onDestroy()
end

function UI_SlgGuideFlyIcon:onUIEventClick(go,param)
    local name = go.name
	if name == "m_btnUse" then
		SetActive(self.ui.m_goBgBlack,false)
		self:FlyIcons()
	end
end

function UI_SlgGuideFlyIcon:FlyIcons()

	local mainUIName = UIMgr:GetNowMainUI()
	local parent = self.ui.m_goMoveParent
	if not mainUIName then
		self:Close()
		return
	end
	
	local mainUI = UIMgr:GetUIItem(mainUIName)

	if not mainUI or not mainUI.ui then
		self:Close()
		return 
	end
	
	local target1 = mainUI.ui.m_goActivityTabGroup6
	if target1 and not IsNil(target1) then
		self:FlyTo(target1,self.ui.m_goFlyItem1,1,0,function ()
			SetActive(self.ui.m_goFlyItem1,false)
			if not IsNil(target1) then
				target1.transform:DOPunchScale(Vector3(0.2,0.2,1),0.3)
			end
		end)
	end
	local target2 = mainUI.ui.m_goHero
	if target2 and not IsNil(target2) then
		self:FlyTo(target2,self.ui.m_goFlyItem2,1,0.3,function ()
			SetActive(self.ui.m_goFlyItem2,false)
			if not IsNil(target2) then
				target2.transform:DOPunchScale(Vector3(0.2,0.2,1),0.3)
			end
		end)
	end
	local target3 = mainUI.ui.m_goHero
	if target3 and not IsNil(target3) then
		self:FlyTo(target3,self.ui.m_goFlyItem3,1,0.6,function ()
			SetActive(self.ui.m_goFlyItem3,false)
			if not IsNil(target3) then
				target3.transform:DOPunchScale(Vector3(0.2,0.2,1),0.3)
			end
		end)
	end
	local target4 = mainUI.ui.m_goHero
	if target4 and not IsNil(target4) then
		self:FlyTo(target4,self.ui.m_goFlyItem4,1,0.9,function ()
			SetActive(self.ui.m_goFlyItem4,false)
			if not IsNil(target4) then
				target4.transform:DOPunchScale(Vector3(0.2,0.2,1),0.3)
			end
		end)
	end

end

function UI_SlgGuideFlyIcon:FlyTo(aObj,bObj,duration,delay,callback)
	local targetPos = self:P2LP(aObj,bObj)
	self.flyfinishCnt = self.flyfinishCnt + 1
	local tween = DOLocalMove(bObj.transform,targetPos,duration,function ()
		if callback then
			callback()
		end
		self:FlyFinish()
	end)
	tween:SetDelay(delay)
end

function UI_SlgGuideFlyIcon:FlyFinish()
	self.flyfinishCnt = self.flyfinishCnt - 1
	if self.flyfinishCnt <= 0 then
		self:Close()
	end
end

function UI_SlgGuideFlyIcon:P2LP(targetGo,flyGo)
	local targetTrans = targetGo.transform
	local flyTrans = flyGo.transform
	local worldPos = targetTrans.parent:TransformPoint(targetTrans.localPosition)
	local localPos = flyTrans.parent.transform:InverseTransformPoint(worldPos)
	return localPos
end

return UI_SlgGuideFlyIcon
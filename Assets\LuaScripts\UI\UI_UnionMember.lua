local UI_UnionMember = Class(BaseView)

function UI_UnionMember:OnInit()
    self.mainItemList = {};
    self.subItemDic = {};
    self.isFirstIn = true;
    self.isDestory = false;

    EventMgr:Add(EventID.REFRESH_UNION_LIST, self.GetUnionInfo, self);
    EventMgr:Add(EventID.CLOSE_CHILD_WINDOW, self.OnCloseWin, self);
end

function UI_UnionMember:OnCreate(param)
    --创建头像
    local headNode = GetChild(self.ui.m_goSubItem, "headNode")
    CreateCommonHead(headNode.transform,0.5)
    
    local leagueData = LeagueManager:GetMyLeagueDetails();
    if IsTableNotEmpty(leagueData) then
        self:UpdateShow(leagueData);
    else
        self:GetUnionInfo();
    end
end

function UI_UnionMember:OnRefresh(param)
    
end

function UI_UnionMember:onDestroy()
    EventMgr:Remove(EventID.REFRESH_UNION_LIST, self.GetUnionInfo, self);
    EventMgr:Remove(EventID.CLOSE_CHILD_WINDOW, self.OnCloseWin, self);
    
    self.isDestory = true;
    self.tipGo = nil;
end

function UI_UnionMember:onUIEventClick(go,param)
    local name = go.name
    if name == "tipBtn" then
        UI_SHOW(UIDefine.UI_LeaguePrivilege);
    end
end

function UI_UnionMember:OnCloseWin()
    if not self.isDestory then
         self:Close();
    end
end

function UI_UnionMember:GetUnionInfo()
    local function callback(data)
        if self.ui then
            self:UpdateShow(data);
        end
    end
    LeagueManager:SendGetLeagueDetails(nil, callback);
end

function UI_UnionMember:UpdateShow(data)
    self.memberDic = {};
    self.onlineDic = {};

    local memberList = data.memberList;
    local dutyList = {};
    local leagueDuty;
    local online;
    local playerId = NetUpdatePlayerData:GetPlayerID();
    for i = 1, #memberList do
        leagueDuty = v2n(memberList[i].leagueDuty);
        if self.memberDic[leagueDuty] == nil then
            self.memberDic[leagueDuty] = {};
            table.insert(dutyList, leagueDuty);
        end
        table.insert(self.memberDic[leagueDuty], memberList[i]);

        memberList[i].lastApi = memberList[i].lastUpdate;
        online = LeagueManager:IsOnLine(memberList[i]);
        if online then
            if self.onlineDic[leagueDuty] == nil then
                self.onlineDic[leagueDuty] = 0;
            end
            self.onlineDic[leagueDuty] = self.onlineDic[leagueDuty] + 1;
        end

        if playerId == v2n(memberList[i].id) then
            self.myDuty = leagueDuty;
        end
    end

    local arr;
    for i = 1, #dutyList do
        arr = self.memberDic[dutyList[i]];
        if arr then
            table.sort(arr, function(a, b)
                return v2n(a.level) > v2n(b.level);
            end);
        end
    end

    self.ui.m_txtMember.text = data.members .. "/" .. data.membersMax;

    local config = ConfigMgr:GetData(ConfigDefine.ID.union_post);
    local mainItem;
    for i = 1, #config do
        mainItem = self.mainItemList[i];
        if not mainItem then
            local obj = CreateGameObjectWithParent(self.ui.m_goMainItem, self.ui.m_scrollview.content);
            mainItem = {content = obj, bg = GetChild(obj, "bg", UEUI.Image),
                dutyTxt = GetChild(obj, "dutyTxt", UEUI.Text),
                listObj = GetChild(obj, "listObj"),
                posIcon = GetChild(obj, "listObj/posIcon", UEUI.Image),
                numTxt = GetChild(obj, "listObj/numTxt", UEUI.Text),
                arrowBtn = GetChild(obj, "arrowBtn", UEUI.Image),
                titleBg = GetChild(obj, "titleBg", UEUI.Image),
                itemObj = GetChild(obj, "itemObj"),
                arrowState = false};
            SetActive(mainItem.content, true);
            table.insert(self.mainItemList, mainItem);
        end
        self:UpdateMainShow(mainItem, config[i]);
    end
    self.isFirstIn = false;
end

function UI_UnionMember:UpdateMainShow(mainItem, config)
    local dutyId = config.id;
    mainItem.dutyTxt.text = LangMgr:GetLang(config.name);

    local alpha = self.myDuty == dutyId and 1 or 0;
    SetUIColor(mainItem.posIcon, 1, 1, 1, alpha);

    local list = self.memberDic[dutyId];
    if list then
        local onlineNum = self.onlineDic[dutyId] or 0;
        mainItem.numTxt.text = onlineNum .. "/" .. #list;
    else
        mainItem.numTxt.text = "0/0";
    end
    UIRefreshLayout(mainItem.listObj);

    local function onChange(arg1, arg2)
        mainItem.arrowState = not mainItem.arrowState;
        self:UpdateMainShow(mainItem, config);
    end

    RemoveUIComponentEventCallback(mainItem.titleBg, UEUI.Button);
    AddUIComponentEventCallback(mainItem.titleBg, UEUI.Button, onChange);

    RemoveUIComponentEventCallback(mainItem.arrowBtn, UEUI.Button);
    AddUIComponentEventCallback(mainItem.arrowBtn, UEUI.Button, onChange);

    if self.isFirstIn then
        mainItem.arrowState = true
    end

    if mainItem.arrowState and list then
        if self.subItemDic[dutyId] == nil then
            self.subItemDic[dutyId] = {};
        end
        local count = #list;
        local num = #self.subItemDic[dutyId];
        local len = count > num and count or num;
        local subItem;
        for i = 1, len do
            subItem = self.subItemDic[dutyId][i];
            if not subItem then
                local obj = CreateGameObjectWithParent(self.ui.m_goSubItem, mainItem.itemObj);
                subItem = {content = obj, bg = GetChild(obj, "bg", UEUI.Image),
                    --headImg = GetChild(obj, "headBg/headImg", UEUI.Image),
                    nameTxt = GetChild(obj, "nameTxt", UEUI.Text),
                    dutyImg = GetChild(obj, "dutyImg", UEUI.Image),
                    dutyTxt = GetChild(obj, "dutyTxt", UEUI.Text),
                    langTxt = GetChild(obj, "langTxt", UEUI.Text),
                    onlineTxt = GetChild(obj, "onlineTxt", UEUI.Text),
                    titleLvTxt = GetChild(obj, "m_txtAuto9008", UEUI.Text),
                    levelBg = GetChild(obj, "levelBg", UEUI.Image),
                    levelTxt = GetChild(obj, "levelTxt", UEUI.Text),
                    clickBg = GetChild(obj, "clickBg");
                    customHeadObj = GetChild(obj,"headNode/CustomHead")
                };
                
                table.insert(self.subItemDic[dutyId], subItem);
            end

            if count >= i then
                self:UpdateSubShow(subItem, list[i]);
            end
            SetActive(subItem.content, count >= i);
        end

        local height = 89 + count * 146 + (count - 1) * 5 + 14;
        SetUISize(mainItem.content, 1034, height);
        SetUISize(mainItem.bg, 1028, height);

        SetImageSprite(mainItem.arrowBtn, "Sprite/ui_lianmeng/tuanduichengyuan_jiantou.png", false);
        SetActive(mainItem.bg, true);
        SetActive(mainItem.itemObj, true);
    else
        mainItem.arrowState = false;
        SetUISize(mainItem.content, 1034, 89);

        SetImageSprite(mainItem.arrowBtn, "Sprite/ui_lianmeng/tuanduichengyuan_jiantou_2.png", false);
        SetActive(mainItem.bg, false);
        SetActive(mainItem.itemObj, false);
    end
    UIRefreshLayout(self.ui.m_scrollview.content);
end

function UI_UnionMember:UpdateSubShow(subItem, data)
    local isMySelf = v2n(data.id) == NetUpdatePlayerData:GetPlayerID();
    --local headPath = FriendManager:GetHeadIcon(data.icon);
    --if headPath then
    --    SetImageSprite(subItem.headImg, headPath, false);
    --end
    
    local customHeadObj = subItem.customHeadObj
    SetHeadAndBorderByGo(customHeadObj,data.icon,data.border)
    customHeadObj.transform.localPosition = Vector3.zero
    
    subItem.nameTxt.text = data.name;

    local duty, path = LeagueManager:GetDutyByType(data.leagueDuty);
    subItem.dutyTxt.text = duty;
    if path then
        SetImageSprite(subItem.dutyImg, path, false);
        SetActive(subItem.dutyImg, true);
        SetUIPos(subItem.dutyTxt, -286, -17.5);
    else
        SetActive(subItem.dutyImg, false);
        SetUIPos(subItem.dutyTxt, -319.5, -17.5);
    end

    subItem.langTxt.text = FriendManager:GetLanguage(data);

    subItem.levelTxt.text = data.level;
    
    if isMySelf then
        subItem.onlineTxt.text = LangMgr:GetLang(9003);
        subItem.dutyTxt.color = GetColorByHex("c4463c");
        subItem.levelTxt.color = GetColorByHex("c4463c");
        UnifyOutline(subItem.nameTxt, "b74100");
        UnifyOutline(subItem.titleLvTxt, "dd6936");
        SetImageSprite(subItem.bg, "Sprite/ui_lianmeng/tuanduichengyuan_dikuang2.png", false);
        SetImageSprite(subItem.levelBg, "Sprite/ui_lianmeng/tuanduichengyuan_dikuang2_1.png", false);
    else
        local _, onlineStr = LeagueManager:IsOnLine(data);
        subItem.onlineTxt.text = onlineStr;
        
        subItem.dutyTxt.color = GetColorByHex("336ebd");
        subItem.levelTxt.color = GetColorByHex("3662ba");
        UnifyOutline(subItem.nameTxt, "20519f");
        UnifyOutline(subItem.titleLvTxt, "2f73b7");
        SetImageSprite(subItem.bg, "Sprite/ui_lianmeng/jiarutuandui_dikuang1.png", false);
        SetImageSprite(subItem.levelBg, "Sprite/ui_lianmeng/jiarutuandui_dikuang1_1.png", false);
    end

    RemoveUIComponentEventCallback(subItem.clickBg, UEUI.Button);
    AddUIComponentEventCallback(subItem.clickBg, UEUI.Button, function(arg1, arg2)
        if not isMySelf then
            self:LoadTips(subItem.clickBg, data);
        end
    end)
end

function UI_UnionMember:LoadTips(go, memberData)
    if self.tipGo == nil then
        local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "LeaguePostTip")
        ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant, function(cellObj)
            self.tipGo = UEGO.Instantiate(cellObj)
            self.tipGo.transform:SetParent(self.uiGameObject.transform)
            self.tipGo.transform.localPosition = Vector3.New(0, 0, 0)
            self.tipGo.transform.localScale = Vector3.New(1, 1, 1)
            self.tipGo.name = "LeaguePostTip"
            SetUISize(self.tipGo, 0, 0);
            SetUIAnchors(self.tipGo, 0, 0, 1, 1);
            InitTextLanguage(self.tipGo)
            self:ShowTips(go, memberData);
        end)
    else
        self:ShowTips(go, memberData);
    end
end

function UI_UnionMember:ShowTips(go, memberData)
    local btn_job = self.tipGo.transform:Find("tipsbg/p/btn_job")
    local btn_transfer = self.tipGo.transform:Find("tipsbg/p/btn_transfer")
    local btn_expel = self.tipGo.transform:Find("tipsbg/p/btn_expel")
    --local btn_dismiss = self.tipGo.transform:Find("tipsbg/p/btn_dismiss")
    local btn_impeachment = self.tipGo.transform:Find("tipsbg/p/btn_impeachment")
    local job_txt = self.tipGo.transform:Find("tipsbg/p/btn_job/m_txtBtnJob"):GetComponent(typeof(UEUI.Text))
    local langId = memberData.leagueDuty > 2 and 9242 or 9243
    job_txt.text = LangMgr:GetLang(langId)

    local myDuty = LeagueManager:GetMyLeagueDuty()
    if myDuty == -1 then
        return
    end
    local pos_config = LeagueManager:GetUnionPosById(myDuty)
    local leaderID = LeagueManager:GetMyLeagueLeaderID()
    local playerId = NetUpdatePlayerData:GetPlayerID()
    local notSelf = playerId ~= v2n(memberData.id)

    SetActive(btn_job, pos_config and pos_config.job == 1 and notSelf)
    SetActive(btn_transfer, pos_config and pos_config.transfer == 1 and notSelf)
    SetActive(btn_expel, pos_config and pos_config.expel == 1 and notSelf and myDuty < memberData.leagueDuty)
    --SetActive(btn_dismiss, pos_config.dismiss == 1)
    SetActive(btn_impeachment, pos_config and pos_config.impeachment == 1 and v2n(memberData.id) == v2n(leaderID) and playerId ~= leaderID)

    local tipsBg = self.tipGo.transform:Find("tipsbg/p");
    local count = 0;
    local child;
    local btnBg;
    for i = 1, tipsBg.childCount do
        child = tipsBg:GetChild(i - 1);
        if child.gameObject.activeSelf then
            btnBg = GetComponent(child.gameObject, UEUI.Image);
            if btnBg then
                btnBg.transform.localScale = Vector3(1, 1, 1);
                btnBg.color = GetColorByHex("ffffff");
            end
            count = count + 1;
        end
    end

    local bgHeight = 217;
    if count == 2 then
        bgHeight = 296;
    elseif count >= 3 then
        bgHeight = 296 + (count - 2) * 108;
    end

    local newpos = self.uiGameObject.transform:InverseTransformPoint(go.transform.position);
    local topPos = newpos.y + bgHeight / 2;
    local endPos = newpos.y - bgHeight / 2;
    local halfHeight = UIHeight / 2;
    local offsetY = 0;
    if topPos >= halfHeight then
        offsetY = topPos - halfHeight;
    elseif endPos <= -halfHeight then
        offsetY = endPos + halfHeight;
    end

    local tipsBg = self.tipGo.transform:Find("tipsbg")
    tipsBg.transform.localPosition = Vector3(newpos.x, newpos.y - offsetY, 0);
    tipsBg.transform.localScale = Vector3.New(1,1,1)

    RemoveUIComponentEventCallback(self.tipGo, UEUI.Button)
    AddUIComponentEventCallback(self.tipGo, UEUI.Button, function(arg1, arg2)
        SetActive(self.tipGo, false)

        local name = arg1.name
        if name == "btn_detail" then
            FriendManager:ShowPlayerById(v2n(memberData.id))
        elseif name == "btn_job" then
            local is_up = memberData.leagueDuty > 2 and 1 or 0
            local can_up = LeagueManager:GetIsCanUpDuty(memberData.leagueDuty,is_up)
            if not can_up then
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9269))
                return
            end
            local function LeagueDutyCallBack()
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
            end
            LeagueManager:SendLeagueDuty(memberData.id,is_up,LeagueDutyCallBack) --提拔降级
        elseif name == "btn_transfer" then
            local function TransferCallBack()
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
            end
            local function onTransfer()
                LeagueManager:SendLeagueTransfer(memberData.id,TransferCallBack) --转让
            end
            UI_SHOW(UIDefine.UI_TipsTop, 9248, onTransfer)
        elseif name == "btn_expel" then
            local function KickCallBack()
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
            end
            if v2n(myDuty) < v2n(memberData.leagueDuty) then
                LeagueManager:SendLeagueKick(memberData.id,memberData.sortIndex,KickCallBack) --踢出联盟
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9251))
            end
        elseif name == "btn_impeachment" then
            local lastApi = memberData.lastUpdate
            local nowTime = TimeMgr:GetServerTimestamp()
            local time = nowTime - v2n(lastApi)
            local leftTime = 3 * OneDaySeconds
            if time < leftTime then
                local hour = math.floor( leftTime / 3600)
                local tips = string.format(LangMgr:GetLang(9250),hour)
                UI_SHOW(UIDefine.UI_WidgetTip, tips)
            else
                local function ImpeachCallBack()
                    self:GetUnionInfo();
                    -- UI_UPDATE(UIDefine.UI_MyUnion,1)
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9254))
                end
                LeagueManager:SendLeagueImpeach(ImpeachCallBack) -- 弹劾
            end
        end
    end)

    SetActive(self.tipGo.transform, true)
end

return UI_UnionMember